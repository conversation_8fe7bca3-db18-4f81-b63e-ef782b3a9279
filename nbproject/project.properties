annotation.processing.enabled=true
annotation.processing.enabled.in.editor=true
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
auxiliary.org-netbeans-modules-html-editor-lib.default-html-public-id=HTML5
build.classes.dir=${build.web.dir}/WEB-INF/classes
build.classes.excludes=**/*.java,**/*.form
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
build.web.dir=${build.dir}/web
build.web.excludes=${build.classes.excludes}
client.urlPart=/index.jsp
compile.jsps=false
conf.dir=${source.root}/conf
debug.classpath=${build.classes.dir}:${javac.classpath}
debug.test.classpath=\
    ${run.test.classpath}
display.browser=true
dist.dir=dist
dist.ear.war=${dist.dir}/${war.ear.name}
dist.javadoc.dir=${dist.dir}/javadoc
dist.war=${dist.dir}/${war.name}
endorsed.classpath=
#endorsed.classpath=\
#    ${libs.javaee-endorsed-api-6.0.classpath}
excludes=
file.reference.accessors-smart-2.4.11.jar=lib/accessors-smart-2.4.11.jar
file.reference.antlr-2.7.7.jar=lib/antlr-2.7.7.jar
file.reference.blti-sandwich.jar=lib/blti-sandwich.jar
file.reference.commons-codec-1.8.jar=lib/commons-codec-1.8.jar
file.reference.commons-email-1.5.jar=lib/commons-email-1.5.jar
file.reference.commons-io-2.1.jar=lib/commons-io-2.1.jar
file.reference.commons-lang3-3.1.jar=lib/commons-lang3-3.1.jar
file.reference.commons-logging-1.1.3.jar=lib/commons-logging-1.1.3.jar
file.reference.commons-validator-1.4.0.jar=lib/commons-validator-1.4.0.jar
file.reference.dom4j-1.6.1.jar=lib/dom4j-1.6.1.jar
file.reference.hibernate-commons-annotations-4.0.2.Final.jar=lib/hibernate-commons-annotations-4.0.2.Final.jar
file.reference.hibernate-core-4.2.3.Final.jar=lib/hibernate-core-4.2.3.Final.jar
file.reference.hibernate-jpa-2.0-api-1.0.1.Final.jar=lib/hibernate-jpa-2.0-api-1.0.1.Final.jar
file.reference.HinkleworldLib.jar=lib/HinkleworldLib.jar
file.reference.javassist-3.15.0-GA.jar=lib/javassist-3.15.0-GA.jar
file.reference.javax.activation-1.2.0.jar=lib/javax.activation-1.2.0.jar
file.reference.javax.json-1.0.2.jar=lib/javax.json-1.0.2.jar
file.reference.jaxen-1.1-beta-6.jar=lib/jaxen-1.1-beta-6.jar
file.reference.jboss-logging-3.1.0.GA.jar=lib/jboss-logging-3.1.0.GA.jar
file.reference.jboss-transaction-api_1.1_spec-1.0.1.Final.jar=lib/jboss-transaction-api_1.1_spec-1.0.1.Final.jar
file.reference.jdom-1.1.3.jar=lib/jdom-1.1.3.jar
file.reference.json-smart-2.4.11.jar=lib/json-smart-2.4.11.jar
file.reference.jstl.jar=lib/jstl.jar
file.reference.lang-tag-1.7.jar=lib/lang-tag-1.7.jar
file.reference.log4j-api-2.17.2.jar=lib/log4j-api-2.17.2.jar
file.reference.log4j-core-2.17.2.jar=lib/log4j-core-2.17.2.jar
file.reference.mail.jar=lib/mail.jar
file.reference.nimbus-jose-jwt-9.31.jar=lib/nimbus-jose-jwt-9.31.jar
file.reference.oauth-20100527.jar=lib/oauth-20100527.jar
file.reference.oauth-consumer-20100527.jar=lib/oauth-consumer-20100527.jar
file.reference.oauth-provider-20100527.jar=lib/oauth-provider-20100527.jar
file.reference.oauth2-oidc-sdk-10.9.1-jdk11.jar=lib/oauth2-oidc-sdk-10.9.1-jdk11.jar
file.reference.org.apache.sling.commons.json-2.0.6.jar=lib/org.apache.sling.commons.json-2.0.6.jar
file.reference.poi-3.0.1-FINAL-20070705.jar=lib/poi-3.0.1-FINAL-20070705.jar
file.reference.standard.jar=lib/standard.jar
file.reference.xalan.jar=lib/xalan.jar
file.reference.xerces.jar=lib/xerces.jar
file.reference.xml-apis.jar=lib/xml-apis.jar
includes=**
j2ee.compile.on.save=false
j2ee.copy.static.files.on.save=true
j2ee.deploy.on.save=false
j2ee.platform=1.6-web
j2ee.platform.classpath=${j2ee.server.home}/lib/annotations-api.jar:${j2ee.server.home}/lib/c3p0-0.9.5.5.jar:${j2ee.server.home}/lib/c3p0-oracle-thin-extras-0.9.5.5.jar:${j2ee.server.home}/lib/catalina-ant.jar:${j2ee.server.home}/lib/catalina-ha.jar:${j2ee.server.home}/lib/catalina-ssi.jar:${j2ee.server.home}/lib/catalina-storeconfig.jar:${j2ee.server.home}/lib/catalina-tribes.jar:${j2ee.server.home}/lib/catalina.jar:${j2ee.server.home}/lib/ecj-4.20.jar:${j2ee.server.home}/lib/el-api.jar:${j2ee.server.home}/lib/jasper-el.jar:${j2ee.server.home}/lib/jasper.jar:${j2ee.server.home}/lib/jaspic-api.jar:${j2ee.server.home}/lib/jsp-api.jar:${j2ee.server.home}/lib/mchange-commons-java-0.2.19.jar:${j2ee.server.home}/lib/mysql-connector-java-8.0.18.jar:${j2ee.server.home}/lib/servlet-api.jar:${j2ee.server.home}/lib/tomcat-api.jar:${j2ee.server.home}/lib/tomcat-coyote.jar:${j2ee.server.home}/lib/tomcat-dbcp.jar:${j2ee.server.home}/lib/tomcat-i18n-cs.jar:${j2ee.server.home}/lib/tomcat-i18n-de.jar:${j2ee.server.home}/lib/tomcat-i18n-es.jar:${j2ee.server.home}/lib/tomcat-i18n-fr.jar:${j2ee.server.home}/lib/tomcat-i18n-ja.jar:${j2ee.server.home}/lib/tomcat-i18n-ko.jar:${j2ee.server.home}/lib/tomcat-i18n-pt-BR.jar:${j2ee.server.home}/lib/tomcat-i18n-ru.jar:${j2ee.server.home}/lib/tomcat-i18n-zh-CN.jar:${j2ee.server.home}/lib/tomcat-jdbc.jar:${j2ee.server.home}/lib/tomcat-jni.jar:${j2ee.server.home}/lib/tomcat-util-scan.jar:${j2ee.server.home}/lib/tomcat-util.jar:${j2ee.server.home}/lib/tomcat-websocket.jar:${j2ee.server.home}/lib/websocket-api.jar
j2ee.server.type=Tomcat
jar.compress=false
javac.classpath=\
    ${file.reference.antlr-2.7.7.jar}:\
    ${file.reference.dom4j-1.6.1.jar}:\
    ${file.reference.hibernate-commons-annotations-4.0.2.Final.jar}:\
    ${file.reference.hibernate-core-4.2.3.Final.jar}:\
    ${file.reference.hibernate-jpa-2.0-api-1.0.1.Final.jar}:\
    ${file.reference.javassist-3.15.0-GA.jar}:\
    ${file.reference.jboss-logging-3.1.0.GA.jar}:\
    ${file.reference.jboss-transaction-api_1.1_spec-1.0.1.Final.jar}:\
    ${file.reference.jstl.jar}:\
    ${file.reference.standard.jar}:\
    ${file.reference.commons-validator-1.4.0.jar}:\
    ${file.reference.jaxen-1.1-beta-6.jar}:\
    ${file.reference.mail.jar}:\
    ${file.reference.commons-lang3-3.1.jar}:\
    ${file.reference.commons-codec-1.8.jar}:\
    ${file.reference.blti-sandwich.jar}:\
    ${file.reference.commons-io-2.1.jar}:\
    ${file.reference.jdom-1.1.3.jar}:\
    ${file.reference.oauth-20100527.jar}:\
    ${file.reference.oauth-consumer-20100527.jar}:\
    ${file.reference.oauth-provider-20100527.jar}:\
    ${file.reference.xalan.jar}:\
    ${file.reference.xerces.jar}:\
    ${file.reference.xml-apis.jar}:\
    ${file.reference.javax.json-1.0.2.jar}:\
    ${file.reference.HinkleworldLib.jar}:\
    ${file.reference.org.apache.sling.commons.json-2.0.6.jar}:\
    ${file.reference.poi-3.0.1-FINAL-20070705.jar}:\
    ${file.reference.commons-email-1.5.jar}:\
    ${file.reference.javax.activation-1.2.0.jar}:\
    ${file.reference.commons-logging-1.1.3.jar}:\
    ${file.reference.log4j-core-2.17.2.jar}:\
    ${file.reference.log4j-api-2.17.2.jar}:\
    ${file.reference.oauth2-oidc-sdk-10.9.1-jdk11.jar}:\
    ${file.reference.nimbus-jose-jwt-9.31.jar}:\
    ${file.reference.lang-tag-1.7.jar}:\
    ${file.reference.json-smart-2.4.11.jar}:\
    ${file.reference.accessors-smart-2.4.11.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.debug=true
javac.deprecation=false
javac.processorpath=\
    ${javac.classpath}
javac.source=11
javac.target=11
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}
javac.test.processorpath=\
    ${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.preview=true
javadoc.private=false
javadoc.reference.commons-codec-1.8.jar=lib/commons-codec-1.8-javadoc.jar
javadoc.reference.commons-lang3-3.1.jar=lib/commons-lang3-3.1-javadoc.jar
javadoc.reference.commons-validator-1.4.0.jar=lib/commons-validator-1.4.0-javadoc.jar
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
lib.dir=${web.docbase.dir}/WEB-INF/lib
persistence.xml.dir=${conf.dir}
platform.active=JDK_11
resource.dir=setup
run.test.classpath=\
    ${javac.test.classpath}:\
    ${build.test.classes.dir}
# Space-separated list of JVM arguments used when running a class with a main method or a unit test
# (you may also define separate properties like run-sys-prop.name=value instead of -Dname=value):
runmain.jvmargs=
source.encoding=UTF-8
source.reference.commons-codec-1.8.jar=lib/commons-codec-1.8-sources.jar
source.reference.commons-lang3-3.1.jar=lib/commons-lang3-3.1-sources.jar
source.reference.commons-validator-1.4.0.jar=lib/commons-validator-1.4.0-sources.jar
source.root=src
src.dir=${source.root}/java
test.src.dir=test
war.content.additional=\
    ${file.reference.antlr-2.7.7.jar}:\
    ${file.reference.dom4j-1.6.1.jar}:\
    ${file.reference.hibernate-commons-annotations-4.0.2.Final.jar}:\
    ${file.reference.hibernate-core-4.2.3.Final.jar}:\
    ${file.reference.hibernate-jpa-2.0-api-1.0.1.Final.jar}:\
    ${file.reference.javassist-3.15.0-GA.jar}:\
    ${file.reference.jboss-logging-3.1.0.GA.jar}:\
    ${file.reference.jboss-transaction-api_1.1_spec-1.0.1.Final.jar}
war.ear.name=${war.name}
war.name=gradeservice.war
web.docbase.dir=web
webinf.dir=web/WEB-INF
