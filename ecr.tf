locals {
  ecr_lifecycle_policy = <<EOF
{
    "rules": [
        {
            "rulePriority": 1,
            "description": "Expire images older than 5 days",
            "selection": {
                "tagStatus": "untagged",
                "countType": "sinceImagePushed",
                "countUnit": "days",
                "countNumber": 5
            },
            "action": {
                "type": "expire"
            }
        },
        {
            "rulePriority": 2,
            "description": "Keep only the last 30 tagged images",
            "selection": {
                "tagStatus": "any",
                "countType": "imageCountMoreThan",
                "countNumber": 30
            },
            "action": {
                "type": "expire"
            }
        }
    ]
}
EOF
}

resource "aws_kms_key" "ecr_kms_key" {
  # checkov:skip=CKV2_AWS_64
  description         = "KMS key for ECR encryption"
  enable_key_rotation = true
}

resource "aws_kms_alias" "ecr_kms_alias" {
  name          = "alias/${terraform.workspace}-ecr-kms-key"
  target_key_id = aws_kms_key.ecr_kms_key.key_id
}

resource "aws_ecr_repository" "edready" {
  # checkov:skip=CKV_AWS_51
  name                 = "${terraform.workspace}-edready"
  image_tag_mutability = "MUTABLE"

  encryption_configuration {
    encryption_type = "KMS"
    kms_key         = aws_kms_key.ecr_kms_key.arn
  }

  image_scanning_configuration {
    scan_on_push = true
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_ecr_repository" "portal" {
  # checkov:skip=CKV_AWS_51
  name                 = "${terraform.workspace}-portal"
  image_tag_mutability = "MUTABLE"

  encryption_configuration {
    encryption_type = "KMS"
    kms_key         = aws_kms_key.ecr_kms_key.arn
  }

  image_scanning_configuration {
    scan_on_push = true
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_ecr_repository" "events" {
  # checkov:skip=CKV_AWS_51
  name                 = "${terraform.workspace}-events"
  image_tag_mutability = "MUTABLE"

  encryption_configuration {
    encryption_type = "KMS"
    kms_key         = aws_kms_key.ecr_kms_key.arn
  }

  image_scanning_configuration {
    scan_on_push = true
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_ecr_repository" "reporting" {
  # checkov:skip=CKV_AWS_51
  name                 = "${terraform.workspace}-reporting"
  image_tag_mutability = "MUTABLE"

  encryption_configuration {
    encryption_type = "KMS"
    kms_key         = aws_kms_key.ecr_kms_key.arn
  }

  image_scanning_configuration {
    scan_on_push = true
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_ecr_lifecycle_policy" "edready" {
  repository = aws_ecr_repository.edready.name
  policy     = local.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "portal" {
  repository = aws_ecr_repository.portal.name
  policy     = local.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "reporting" {
  repository = aws_ecr_repository.reporting.name
  policy     = local.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "events" {
  repository = aws_ecr_repository.events.name
  policy     = local.ecr_lifecycle_policy
}