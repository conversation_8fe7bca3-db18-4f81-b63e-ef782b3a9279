resource "aws_elastic_beanstalk_application" "edready" {
  name        = "Edready_"
  description = "edready version 2 java11"
  count       = terraform.workspace == "production" ? 1 : 0
  appversion_lifecycle {
    service_role          = "arn:aws:iam::341268829071:role/aws-elasticbeanstalk-service-role"
    max_count             = 150
    delete_source_from_s3 = true
  }
}

# The commented lines should be review the next time we run this for production environment
resource "aws_elastic_beanstalk_application" "event_tracker" {
  name        = "Events_"
  description = "EventTracker application"
  count       = terraform.workspace == "production" ? 1 : 0
  appversion_lifecycle {
    service_role          = "arn:aws:iam::341268829071:role/aws-elasticbeanstalk-service-role"
    max_count             = 100
    delete_source_from_s3 = true
  }
}

# The commented lines should be review the next time we run this for production environment
resource "aws_elastic_beanstalk_application" "portal" {
  name        = "Portal_"
  description = "Portal"
  count       = terraform.workspace == "production" ? 1 : 0
  appversion_lifecycle {
    service_role          = "arn:aws:iam::341268829071:role/aws-elasticbeanstalk-service-role"
    max_count             = 150
    delete_source_from_s3 = true
  }
}


resource "aws_elastic_beanstalk_application" "art" {
  name        = "Reporting_"
  description = "ART Application"
  count       = terraform.workspace == "production" ? 1 : 0
  appversion_lifecycle {
    service_role          = "arn:aws:iam::341268829071:role/aws-elasticbeanstalk-service-role"
    max_count             = 100
    delete_source_from_s3 = true
  }
}


data "aws_elastic_beanstalk_application" "portal" {
  name = "Portal_"
}

data "aws_elastic_beanstalk_application" "edready" {
  name = "Edready_"
}

data "aws_elastic_beanstalk_application" "event_tracker" {
  name = "Events_"
}

data "aws_elastic_beanstalk_application" "art" {
  name = "Reporting_"
}
