#!/bin/bash

# Course Manager Tomcat Startup Script
# This script substitutes environment variables in server.xml and starts Tomcat

set -e

echo "🚀 Starting Course Manager Application..."
echo "📊 Environment: ${ENVIRONMENT:-development}"

# Set default values if environment variables are not provided
export DB_HOST=${DB_HOST:-localhost}
export DB_PORT=${DB_PORT:-3306}
export DB_NAME=${DB_NAME:-gradeservice}
export DB_USERNAME=${DB_USERNAME:-gradeservice_user}
export DB_PASSWORD=${DB_PASSWORD:-gradeservice_pass}

echo "🔍 Database Configuration:"
echo "   Host: ${DB_HOST}"
echo "   Port: ${DB_PORT}"
echo "   Database: ${DB_NAME}"
echo "   Username: ${DB_USERNAME}"
echo "   Password: [HIDDEN]"

# Create a backup of the original server.xml
if [ ! -f /usr/local/tomcat/conf/server.xml.template ]; then
    cp /usr/local/tomcat/conf/server.xml /usr/local/tomcat/conf/server.xml.template
fi

# Substitute environment variables in server.xml
echo "🔧 Configuring Tomcat server.xml with environment variables..."
envsubst '${DB_HOST} ${DB_PORT} ${DB_NAME} ${DB_USERNAME} ${DB_PASSWORD}' \
    < /usr/local/tomcat/conf/server.xml.template \
    > /usr/local/tomcat/conf/server.xml

# Verify the substitution worked
echo "✅ Database URL configured as:"
grep -o 'url="[^"]*"' /usr/local/tomcat/conf/server.xml | head -1

# Test database connectivity (optional)
if command -v mysql &> /dev/null; then
    echo "🔍 Testing database connectivity..."
    if mysql -h"${DB_HOST}" -P"${DB_PORT}" -u"${DB_USERNAME}" -p"${DB_PASSWORD}" -e "SELECT 1;" "${DB_NAME}" &> /dev/null; then
        echo "✅ Database connection successful"
    else
        echo "⚠️  Database connection failed, but continuing startup..."
    fi
else
    echo "ℹ️  MySQL client not available, skipping connectivity test"
fi

# Start Tomcat
echo "🚀 Starting Tomcat..."
exec /usr/local/tomcat/bin/catalina.sh run