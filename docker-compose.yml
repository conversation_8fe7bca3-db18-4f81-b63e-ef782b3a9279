version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: gradeservice-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-gradeservice}
      MYSQL_USER: ${MYSQL_USER:-gradeservice_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-gradeservice_pass}
    ports:
      - "${DB_PORT:-3307}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./database:/docker-entrypoint-initdb.d/database:ro
    networks:
      - gradeservice-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Grade Service Application
  gradeservice:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gradeservice-app
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # Database connection
      JDBC_CONNECTION_STRING: ${JDBC_CONNECTION_STRING:-*************************************************************************************************}
      DB_USERNAME: ${MYSQL_USER:-gradeservice_user}
      DB_PASSWORD: ${MYSQL_PASSWORD:-gradeservice_pass}

      # Server configuration
      SERVER_CONFIG_NAME: ${SERVER_CONFIG_NAME:-docker_production}

      # Java options
      CATALINA_OPTS: "${CATALINA_OPTS:--Xmx1024m -Xms512m}"
      JAVA_OPTS: "${JAVA_OPTS:--Djava.security.egd=file:/dev/./urandom}"
    ports:
      - "${APP_PORT:-8080}:8080"
    volumes:
      - app_logs:/usr/local/tomcat/logs
      - app_temp:/usr/local/tomcat/temp
    networks:
      - gradeservice-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/gradeservice/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  mysql_data:
    driver: local
  app_logs:
    driver: local
  app_temp:
    driver: local

networks:
  gradeservice-network:
    driver: bridge
