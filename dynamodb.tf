resource "aws_dynamodb_table" "portal_audit" {
  name                        = "${terraform.workspace}-PortalAuditEvents"
  deletion_protection_enabled = true
  billing_mode                = "PAY_PER_REQUEST"
  hash_key                    = "eventType"
  range_key                   = "timestamp"
  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "eventType"
    type = "S"
  }

  attribute {
    name = "timestamp"
    type = "N"
  }
  #checkov:skip=CKV_AWS_119: thi dynamodb is already encrypted.
  server_side_encryption {
    enabled = true
  }
}