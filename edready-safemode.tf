resource "aws_elastic_beanstalk_environment" "edready_safemode" {
  count               = var.deploy_edready_safemode == true ? 1 : 0
  name                = "${terraform.workspace}-edready-safemode"
  application         = data.aws_elastic_beanstalk_application.edready.id
  cname_prefix        = "${terraform.workspace}-edready-safemode"
  solution_stack_name = data.aws_elastic_beanstalk_solution_stack.tomcat.name
  version_label       = data.external.edready_version.result.version_label

  setting {
    namespace = "aws:ec2:vpc"
    name      = "VPCId"
    value     = module.vpc.vpc_id
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "Subnets"
    value     = join(",", sort(module.vpc.private_subnets))
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "ELBSubnets"
    value     = join(",", sort(module.vpc.public_subnets))
  }

  ## JVM Options documented here: https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/command-options-specific.html
  setting {
    namespace = "aws:elasticbeanstalk:container:tomcat:jvmoptions"
    name      = "JVM Options"
    value     = var.jvm_options
  }

  setting {
    namespace = "aws:elasticbeanstalk:container:tomcat:jvmoptions"
    name      = "Xmx"
    value     = var.xmx
  }

  setting {
    namespace = "aws:elasticbeanstalk:container:tomcat:jvmoptions"
    name      = "Xms"
    value     = var.xms
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.edready_profile.name
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "SecurityGroups"
    value     = aws_security_group.edready_app_sg.id
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "SSHSourceRestriction"
    value     = "tcp,22,22,${var.vpn_ip}"
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "EC2KeyName"
    value     = var.ec2_key
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "InstanceType"
    value     = var.edready_instance_type
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = var.edready_ag_min
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = var.edready_ag_max
  }

  setting {
    namespace = "aws:elbv2:loadbalancer"
    name      = "IdleTimeout"
    value     = "120"
  }

  setting {
    namespace = "aws:elbv2:loadbalancer"
    name      = "SecurityGroups"
    value     = aws_security_group.edready_lb_sg.id
  }

  setting {
    namespace = "aws:elbv2:loadbalancer"
    name      = "ManagedSecurityGroup"
    value     = aws_security_group.edready_lb_sg.id
  }

  // Safe Mode - Beanstalk sometimes enters a loop where changes don't get deployed because an instance gets stuck in unhealthy
  setting {
    namespace = "aws:elasticbeanstalk:command"
    name      = "DeploymentPolicy"
    value     = "AllAtOnce"
  }

  // Safe Mode - Disabled to try to force changes to go through
  setting {
    namespace = "aws:elasticbeanstalk:command"
    name      = "IgnoreHealthCheck"
    value     = "true"
  }

  setting {
    namespace = "aws:autoscaling:updatepolicy:rollingupdate"
    name      = "RollingUpdateEnabled"
    value     = "false"
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = data.aws_acm_certificate.edready.arn
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLPolicy"
    value     = var.elb_ssl_policy
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "bucket.name"
    value     = var.edready_bucket_name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "locked.user.time.minutes"
    value     = var.edready_locked_user_min
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "DisableIMDSv1"
    value     = "true"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "max.login.attempts"
    value     = var.edready_login_attempts
  }
  ####
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "database.password"
    value     = var.edready_db_password
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "database.username"
    value     = var.edready_db_username
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "database.url"
    value     = var.main_db_url
  }

  // Safe Mode is not using read replica
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "database.reader.url"
    value     = var.main_db_url
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "database.pool.size"
    value     = var.main_db_pool_size
  }


  // Safe Mode is providing safe_mode values to the queues. In January of 2024, we had a major outage that took down
  // EdReady repeatedly because of issues with messages in the queues.
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.notifications.queue"
    # value     = aws_sqs_queue.et_notifications.name
    value = "safe_mode"
  }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.notification_queue_name"
    # value     = aws_sqs_queue.et_notifications.name
    value = "safe_mode"
  }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.queue_name"
    # value     = aws_sqs_queue.et_queue.name
    value = "safe_mode"
  }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.events.queue"
    # value     = aws_sqs_queue.et_queue.name
    value = "safe_mode"
  }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "et.sqs.user_notifications.queue"
    # value     = aws_sqs_queue.et_user_notifications.name
    value = "safe_mode"
  }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.user_notifications.queue"
    # value     = aws_sqs_queue.et_user_notifications.name
    value = "safe_mode"
  }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.student.submission.events.queue"
    # value     = aws_sqs_queue.event_tracker_student_submissions.name
    value = "safe_mode"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.services.secret_key"
    value     = var.nroc_services_secret_key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "questions.content.base.url"
    value     = var.questions_base_url
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "server.environment"
    value     = var.serverenv
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "server.host"
    value     = var.edready_server_host
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "advanced_reporting.url"
    value     = var.art_url
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.notification_api_url"
    value     = var.et_api_url
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "email.host"
    value     = var.mail_host
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "email.password"
    value     = var.mail_password
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "email.username"
    value     = var.mail_username
  }

  # 01/08/2024 We used to set non-prod to empty, but it would prevent terraform apply
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "email.port"
    value     = terraform.workspace == "production" ? "587" : var.mail_port
  }

  // Safe Mode - Using New Relic has caused some issues in the past
  #   setting {
  #     namespace = "aws:elasticbeanstalk:application:environment"
  #     name      = "newrelic.config.app_name"
  #     value     = "${terraform.workspace} edready"
  #   }

  // Safe Mode
  #   setting {
  #     namespace = "aws:elasticbeanstalk:application:environment"
  #     name      = "newrelic.config.license_key"
  #     value     = var.new_relic_license_key
  #   }

  // Safe Mode
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.et.rewards.queue"
    # value     = aws_sqs_queue.event_tracker_rewards.name
    value = "safe_mode"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "webflow.url"
    value     = var.webflow_url
  }

  # 01/08/2024 We used to set non-prod to empty, but it would prevent terraform apply
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = terraform.workspace == "production" ? "schedule.task.deleteUnconfirmedStudentsAccounts.frequency" : "0"
    value     = terraform.workspace == "production" ? "0 0 5 * * ?" : "0 0 5 31 2 ?" # 0 0 5 31 2 ? will never execute
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = terraform.workspace == "production" ? "schedule.task.deleteUnconfirmedStudentsAccounts.noActivityHours" : "0"
    value     = terraform.workspace == "production" ? 72 : -1
  }

  setting {
    namespace = "aws:elasticbeanstalk:managedactions"
    name      = "ManagedActionsEnabled"
    value     = "false"
  }

  # Essay grade queues variables
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "nroc.portal.essay.sync.bridge.queue"
    # value     = "portal-essay-sync-bridge-${terraform.workspace}"
    value = "safe_mode"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "message.dispatcher.poolsize"
    value     = var.message_dispatcher_poolsize
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_ROOT"
    value     = "ERROR"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_EDREADY"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_HIBERNATE"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_JPA"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_SCHEDULERS"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_HIKARI"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_LISTENERS_NOTIFICATION"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_REWARDS"
    value     = "DEBUG"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "edready.reports.s3.bucket"
    value     = var.edready_reports_s3_bucket
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_REPORTS"
    value     = "INFO"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LOG_LEVEL_POINTS_TRACKER"
    value     = "TRACE"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "REPORTS_EXPIRATION_DAYS"
    value     = "7"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "sso.ppk.environment"
    value     = var.sso_ppk_environment
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "recaptcha.score.threshold"
    value     = var.recaptcha_score_threshold
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "recaptcha.secret.key"
    value     = var.recaptcha_secret_key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "recaptcha.site.key"
    value     = var.recaptcha_site_key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "student.assessment.reasonable.studypath.duration.minutes"
    value     = var.student_assessment_reasonable_studypath_duration_minutes
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "min.learning.time.seconds.max.value"
    value     = var.min_learning_time_seconds_max_value
  }


  # General Elastic Beanstalk Environment settings
  dynamic "setting" {
    for_each = var.general_eb_environment_settings
    iterator = set_edready
    content {
      namespace = "aws:elasticbeanstalk:environment"
      name      = set_edready.value["name"]
      value     = set_edready.value["value"]
    }
  }

  # EdReady Auto Scaling Trigger settings
  dynamic "setting" {
    for_each = var.edready_autoscaling_trigger
    iterator = set_edready
    content {
      namespace = "aws:autoscaling:trigger"
      name      = set_edready.value["name"]
      value     = set_edready.value["value"]
    }
  }

  # EdReady Elastic Load Balancer v2 Listener 443 settings
  dynamic "setting" {
    for_each = var.edready_elbv2_listener_443
    iterator = set_edready
    content {
      namespace = "aws:elbv2:listener:443"
      name      = set_edready.value["name"]
      value     = set_edready.value["value"]
    }
  }

  # EdReady Elastic Beanstalk Environment Process Default settings
  dynamic "setting" {
    for_each = var.edready_eb_environment_process_default
    iterator = set_edready
    content {
      namespace = "aws:elasticbeanstalk:environment:process:default"
      name      = set_edready.value["name"]
      value     = set_edready.value["value"]
    }
  }

  # EdReady Elastic Beanstalk Application Environment settings
  dynamic "setting" {
    for_each = var.edready_eb_application_environment
    iterator = set_edready
    content {
      namespace = "aws:elasticbeanstalk:application:environment"
      name      = set_edready.value["name"]
      value     = set_edready.value["value"]
    }
  }

  # EdReady other settings
  dynamic "setting" {
    for_each = var.edready_other_settings
    iterator = set_edready
    content {
      namespace = set_edready.value["namespace"]
      name      = set_edready.value["name"]
      value     = set_edready.value["value"]
    }
  }

  tags = {
    Environment = terraform.workspace
    Application = "edready"
  }
}

data "aws_lb_listener" "http_listener_edready_safemode" {
  count             = var.deploy_edready_safemode == true ? 1 : 0
  load_balancer_arn = aws_elastic_beanstalk_environment.edready_safemode[count.index].load_balancers[0]
  port              = 80
}
resource "aws_lb_listener_rule" "redirect_http_to_https_edready_safemode" {
  count        = var.deploy_edready_safemode == true ? 1 : 0
  listener_arn = data.aws_lb_listener.http_listener_edready_safemode[count.index].arn
  priority     = 1
  action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}
