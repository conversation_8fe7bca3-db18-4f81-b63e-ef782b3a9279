# Grade Service 2 - Docker Containerization

This document provides instructions for running the Grade Service 2 application using Docker containers.

## Prerequisites

- Docker Engine 20.10 or later
- Docker Compose 2.0 or later
- At least 2GB of available RAM
- At least 5GB of available disk space

## Quick Start

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd grade-server-2
   ```

2. **Build and start the containers**:
   ```bash
   docker-compose up -d
   ```

3. **Wait for the application to start** (this may take 2-3 minutes):
   ```bash
   docker-compose logs -f gradeservice
   ```

4. **Access the application**:
   - Application: http://localhost:8080/gradeservice/
   - Database: localhost:3306 (if you need direct access)

## Container Architecture

The application consists of two main containers:

### 1. MySQL Database Container (`gradeservice-mysql`)
- **Image**: mysql:8.0
- **Port**: 3306
- **Database**: gradeservice
- **User**: gradeservice_user
- **Password**: gradeservice_pass
- **Root Password**: rootpassword

### 2. Application Container (`gradeservice-app`)
- **Base Image**: tomcat:9.0-jdk11-openjdk-slim
- **Port**: 8080
- **Context Path**: /gradeservice
- **Java Version**: OpenJDK 11
- **Tomcat Version**: 9.0

## Environment Variables

The following environment variables can be customized in `docker-compose.yml`:

### Database Configuration
- `MYSQL_ROOT_PASSWORD`: MySQL root password
- `MYSQL_DATABASE`: Database name
- `MYSQL_USER`: Application database user
- `MYSQL_PASSWORD`: Application database password

### Application Configuration
- `JDBC_CONNECTION_STRING`: Database connection URL
- `SERVER_CONFIG_NAME`: Server configuration profile
- `CATALINA_OPTS`: Tomcat JVM options
- `JAVA_OPTS`: Additional Java options

## Management Commands

### Start the application
```bash
docker-compose up -d
```

### Stop the application
```bash
docker-compose down
```

### View logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f gradeservice
docker-compose logs -f mysql
```

### Restart a service
```bash
docker-compose restart gradeservice
docker-compose restart mysql
```

### Access container shell
```bash
# Application container
docker-compose exec gradeservice bash

# Database container
docker-compose exec mysql bash
```

### Database operations
```bash
# Connect to MySQL
docker-compose exec mysql mysql -u gradeservice_user -p gradeservice

# Import SQL file
docker-compose exec -T mysql mysql -u gradeservice_user -p gradeservice < your_file.sql
```

## Data Persistence

The following data is persisted using Docker volumes:

- **mysql_data**: Database files
- **app_logs**: Application logs
- **app_temp**: Temporary application files

To backup data:
```bash
# Backup database
docker-compose exec mysql mysqldump -u root -p gradeservice > backup.sql

# List volumes
docker volume ls
```

## Troubleshooting

### Application won't start
1. Check if ports 8080 and 3306 are available
2. Ensure Docker has enough memory allocated
3. Check logs: `docker-compose logs gradeservice`

### Database connection issues
1. Verify MySQL container is healthy: `docker-compose ps`
2. Check database logs: `docker-compose logs mysql`
3. Verify connection string in docker-compose.yml

### Performance issues
1. Increase memory allocation in CATALINA_OPTS
2. Monitor resource usage: `docker stats`

### Clean restart
```bash
# Stop and remove containers, networks
docker-compose down

# Remove volumes (WARNING: This deletes all data)
docker-compose down -v

# Rebuild and start
docker-compose up -d --build
```

## Development

### Building the application manually
```bash
# Build only the application container
docker-compose build gradeservice

# Force rebuild without cache
docker-compose build --no-cache gradeservice
```

### Debugging
```bash
# Run with debug output
docker-compose up

# Access Tomcat manager (if enabled)
# http://localhost:8080/manager/html
```

## Security Considerations

1. **Change default passwords** in production
2. **Use environment files** for sensitive data
3. **Enable SSL/TLS** for production deployments
4. **Restrict network access** using Docker networks
5. **Regular security updates** of base images

## Production Deployment

For production deployment:

1. Create a `.env` file with production values
2. Use Docker Secrets for sensitive data
3. Configure proper logging and monitoring
4. Set up backup procedures
5. Use a reverse proxy (nginx/Apache) for SSL termination

## Support

For issues related to containerization, check:
1. Docker logs: `docker-compose logs`
2. Container health: `docker-compose ps`
3. Resource usage: `docker stats`
4. Network connectivity: `docker network ls`
