# options for analysis running
run:
  # timeout for analysis, e.g. 30s, 5m, default is 1m
  deadline: 5m

# all available settings of specific linters
linters-settings:
  errcheck:
    ignoretests: true
    ignore: github.com/hashicorp/terraform-plugin-sdk/helper/schema:ForceNew|Set,github.com/hashicorp/terraform-plugin-sdk/helper/schema:Set,github.com/newrelic/terraform-provider-newrelic/newrelic:Set,io:Close|Write
  gocyclo:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 20
  govet:
    # report about shadowed variables
    check-shadowing: true
    enable:
      - assign
      - bools
      - framepointer
      - stringintconv
      - structtag
  misspell:
    ignore-words:
      - newrelic

linters:
  disable-all: true
  enable:
    - errcheck
    - gocyclo
    - gofmt
    - gosimple
    - govet
    - ineffassign
    - misspell
    - revive
    - unconvert
    - unused
    - vet

issues:
  # disable limits on issue reporting
  max-per-linter: 0
  max-same-issues: 0

  exclude-rules:
    # ignore deprecation warnings since they fail ci with failure exit code
    # https://staticcheck.io/docs/checks#SA1019
    - linters:
        - staticcheck
      text: "SA1019:"
