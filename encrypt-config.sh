#!/bin/sh

echo "Compresing config..."
tar czf config.tar.gz development.tfvars testing.tfvars staging.tfvars production.tfvars production-dr.tfvars

echo "Encrypting config tarball..."
openssl enc -aes-256-cbc \
  -in ./config.tar.gz \
  -out ./config.tar.gz.enc \
  -K ${CI_ENC_KEY} \
  -iv ${CI_ENC_IV}

rm config.tar.gz
rm development.tfvars testing.tfvars staging.tfvars production.tfvars production-dr.tfvars
rm ./terraform-plans/*.tfplan

