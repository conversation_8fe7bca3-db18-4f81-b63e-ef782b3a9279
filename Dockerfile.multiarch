# Multi-stage build for Java web application - Multi-architecture support
FROM openjdk:11-jdk-slim as builder

# Install Ant for building the application and wget for downloading servlet API
RUN apt-get update && apt-get install -y ant wget && rm -rf /var/lib/apt/lists/*

# Download servlet API and annotation API for compilation
RUN mkdir -p /tmp/servlet-api && \
    cd /tmp/servlet-api && \
    wget https://repo1.maven.org/maven2/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar && \
    wget https://repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar

# Set working directory
WORKDIR /app

# Copy source code and build files
COPY . .

# Build the application with server home set to a dummy location and override platform
RUN mkdir -p /tmp/tomcat && \
    ant -Dj2ee.server.home=/tmp/tomcat \
        -Dplatform.active=default_platform \
        -Dplatforms.default_platform.home=${JAVA_HOME} \
        -Dj2ee.platform.classpath=/tmp/servlet-api/javax.servlet-api-4.0.1.jar:/tmp/servlet-api/javax.annotation-api-1.3.2.jar \
        clean dist

# Production stage - No platform specification for multi-arch support
FROM tomcat:9.0-jdk11-openjdk-slim

# Install curl for health checks and wget for downloading MySQL driver
RUN apt-get update && apt-get install -y curl wget && rm -rf /var/lib/apt/lists/*

# Download MySQL JDBC driver to Tomcat's lib directory
RUN wget -O /usr/local/tomcat/lib/mysql-connector-j.jar \
    https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar

# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*

# Copy the built WAR file
COPY --from=builder /app/dist/gradeservice.war /usr/local/tomcat/webapps/gradeservice.war

# Copy Tomcat configuration files
COPY docker/tomcat/server.xml /usr/local/tomcat/conf/server.xml
COPY docker/tomcat/context.xml /usr/local/tomcat/conf/context.xml

# Create logs directory
RUN mkdir -p /usr/local/tomcat/logs

# Set environment variables
ENV CATALINA_OPTS="-Xmx1024m -Xms512m"
ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/gradeservice/ || exit 1

# Start Tomcat
CMD ["catalina.sh", "run"]
