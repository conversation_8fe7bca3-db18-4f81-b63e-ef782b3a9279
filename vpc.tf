## By setting private_subnets and setting enable_nat_gateway to true,
## the vpc should automatically associate the subnets with the route tables
## If for some reason it doesn't, I fixed it by changing the version and running terraform init
## Even if the init fails, change it back to the original version and apply again.
## It should then try to create the subnet/route table associations
## https://registry.terraform.io/modules/terraform-aws-modules/vpc/aws/latest
module "vpc" {
  # checkov:skip=CKV_TF_1
  source                 = "terraform-aws-modules/vpc/aws"
  name                   = "${terraform.workspace}-VPC"
  cidr                   = var.vpc_cidr
  azs                    = var.azs
  private_subnets        = var.private_subnet_cidrs
  public_subnets         = var.public_subnet_cidrs
  enable_nat_gateway     = true
  single_nat_gateway     = false
  one_nat_gateway_per_az = false
  tags                   = var.tags
  version                = "~>5.0"
  enable_dns_hostnames   = true

  default_security_group_ingress = []
  default_security_group_egress  = []

  enable_flow_log                  = true
  flow_log_destination_type        = "cloud-watch-logs"
  flow_log_destination_arn         = aws_cloudwatch_log_group.flow_log.arn
  flow_log_cloudwatch_iam_role_arn = aws_iam_role.vpc_flow_log_cloudwatch.arn
  flow_log_traffic_type            = "REJECT"

  vpc_flow_log_tags = {
    Name = "${terraform.workspace}-vpc-flow-logs-cloudwatch-logs"
  }

  public_subnet_tags = {
    "kubernetes.io/cluster/${terraform.workspace}" = "shared"
    "kubernetes.io/role/elb"                       = 1
  }

  private_subnet_tags = {
    "kubernetes.io/cluster/${terraform.workspace}" = "shared"
    "kubernetes.io/role/internal-elb"              = 1
  }

}

# Cloudwatch logs
resource "aws_cloudwatch_log_group" "flow_log" {
  # checkov:skip=CKV_AWS_338
  name              = "${terraform.workspace}-flow-logs-log-group"
  kms_key_id        = data.aws_kms_key.cloudwatch.arn
  retention_in_days = 90
}

resource "aws_iam_role" "vpc_flow_log_cloudwatch" {
  name_prefix        = "${terraform.workspace}-vpc-flow-log-role-"
  assume_role_policy = data.aws_iam_policy_document.flow_log_cloudwatch_assume_role.json
}

data "aws_iam_policy_document" "flow_log_cloudwatch_assume_role" {
  statement {
    principals {
      type        = "Service"
      identifiers = ["vpc-flow-logs.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role_policy_attachment" "vpc_flow_log_cloudwatch" {
  role       = aws_iam_role.vpc_flow_log_cloudwatch.name
  policy_arn = aws_iam_policy.vpc_flow_log_cloudwatch.arn
}

resource "aws_iam_policy" "vpc_flow_log_cloudwatch" {
  name_prefix = "${terraform.workspace}-vpc-flow-log-cloudwatch-"
  policy      = data.aws_iam_policy_document.vpc_flow_log_cloudwatch.json
}

data "aws_iam_policy_document" "vpc_flow_log_cloudwatch" {
  statement {
    sid = "AWSVPCFlowLogsPushToCloudWatch"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
    ]

    resources = ["arn:aws:logs:${local.aws_deployment_region}:341268829071:log-group:*:*"]
  }
}
