# VPC
output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.vpc.vpc_id
}

# Subnets
output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = module.vpc.private_subnets
}

output "public_subnets" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnets
}

# APP SG
output "edready_app_sg" {
  description = "Edready (app level) sg id"
  value       = aws_security_group.edready_app_sg.id
}

#EdReady Beanstalk
output "edready_beanstalk_name" {
  description = "Environment and name of the EdReady Beanstalk"
  value       = aws_elastic_beanstalk_environment.edready.name
}

#Event Tracker Beanstalk
output "et_beanstalk_name" {
  description = "Environment and name of the Event Tracker Beanstalk"
  value       = aws_elastic_beanstalk_environment.event_tracker.name
}