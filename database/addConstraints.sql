use gradeservice;
ALTER TABLE UnitStateForClass ADD CONSTRAINT `class_unit` UNIQUE (`class_id`,`unit`);
ALTER TABLE TopicStateForClass ADD CONSTRAINT `unique_composite` UNIQUE (`class_id`,`unit`,`lesson`,`topic`);
ALTER TABLE TopicStateForStudentClass ADD CONSTRAINT `unique_composite` UNIQUE (`student_id`,`class_id`,`unit`,`lesson`,`topic`);
ALTER TABLE UnitStateForStudentClass ADD CONSTRAINT `unique_composite` UNIQUE (`student_id`,`class_id`,`unit`);
ALTER TABLE user ADD KEY `login` (`user_name`,`user_password`);


