LTI messages:

Lti <PERSON> failed for https://opencourses.desire2learn.com. Keep an eye on it.

WARN  org.montereyinstitute.lti.LtiCallback  - LtiDbRecord was null: from LtiDbRecord as z where z.studentId='vmitchell' and z.courseId='2371'
    LtiCallback checks each time scores are submitted for a record, so a null value is ok when topic was not launched in Lti context.

WARN  org.montereyinstitute.lti.LtiCallback  - POX Operation did not succeed, status: FAILURE; unauthorized request
    This is frustrating because Moodle 2.4 errors out on POX even when it's successful (you can see in <PERSON><PERSON>le that the score was posted). 
    So I'm not sure if this status means it actually failed or not.

------------------------------------------------------------------------------------------------------------------------------------------------------

WebService messages:

WARN  org.montereyinstitute.action.webservice.WebServiceWorker  - initTopicState failed.
    This is Ok. Some WebServiceWorker methods look up unitState, then topicState, and will apply action to unit state if topicState is null.