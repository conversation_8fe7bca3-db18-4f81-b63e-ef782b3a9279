# General Varibles
main_db_pool_size            = "20"
mail_host                    = "smtp.mailtrap.io"
mail_username                = "09050ed7c53008"
mail_port                    = "465"
new_relic_license_key        = ""
et_events_queue              = "edready-events-development"
et_notifications_queue       = "et-notifications-development"
et_user_notifications_queue  = "et-user-notifications-development"
cm_aws_sms_originationnumber = ""
cm_aws_sms_region            = "us-east-1"
deployment_policy            = "AllAtOnce"
ignore_health_check          = "true"
rolling_update               = "false"

### redis config
redis_num_cache_nodes = "1"
serverenv             = "development"

#### edready variables
edready_instance_type                                    = "t3.large"
edready_ag_min                                           = "1"
edready_ag_max                                           = "1"
questions_base_url                                       = "https://s3-us-west-1.amazonaws.com/mitecontent/nroc-question-staging"
questions_library_mathjax                                = "https://content.nroc.org/MathJax.2.7.5"
edready_bucket_name                                      = "edready-development"
message_dispatcher_poolsize                              = "2"
edready_log_level                                        = "DEBUG"
edready_server_host                                      = "https://development.edready.org"
art_url                                                  = "https://dev-reporting.edreadyservices.org"
et_api_url                                               = "https://dev-events.edreadyservices.org/api/v1/"
edready_cert                                             = "development.edready.org"
jvm_options                                              = "-javaagent:/usr/share/tomcat9/webapps/ROOT/WEB-INF/lib/newrelic-agent.jar"
xmx                                                      = "2048m"
xms                                                      = "2048m"
webflow_url                                              = "https://getdevelopment.edready.org"
edready_reports_s3_bucket                                = "edready-development-reports"
sso_ppk_environment                                      = "development"
edready_ecs_image                                        = ""
edready_min_capacity                                     = "0"
edready_max_capacity                                     = "0"
edready_ecs_cpu                                          = 1024
edready_ecs_memory                                       = 5120
edready_certificate_arn                                  = "arn:aws:acm:us-west-1:341268829071:certificate/5621d166-90d6-402f-8b73-c7f9d3b7b412"
recaptcha_score_threshold                                = "0.0"
student_assessment_reasonable_studypath_duration_minutes = "30"
min_learning_time_seconds_max_value                      = "3600"

#### art variables
art_instance_type   = "t3.large"
art_ag_min          = "1"
art_ag_max          = "1"
art_java_opts       = "-server -Xms1g -Xmx1g -Xss512k -XX:+UseCompressedOops"
art_mail_port       = "465"
art_server_host     = "development.edready.org"
art_cert            = "*.edreadyservices.org"
art_ecs_image       = ""
art_min_capacity    = "1"
art_max_capacity    = "1"
art_ecs_cpu         = 256
art_ecs_memory      = 512
art_certificate_arn = "arn:aws:acm:us-west-1:341268829071:certificate/3f39c899-4a44-491b-a4eb-f3c0a9a55942"

#### portal
portal_instance_type   = "t3.large"
portal_ag_min          = "1"
portal_ag_max          = "1"
portal_java_opts       = "-server -Xms1g -Xmx1g -Xss512k -XX:+UseCompressedOops"
portal_log_level       = "INFO"
portal_cert            = "development.nrocportal.org"
spring_profiles        = "development"
portal_ecs_image       = ""
portal_min_capacity    = "0"
portal_max_capacity    = "0"
portal_ecs_cpu         = 512
portal_ecs_memory      = 1024
portal_certificate_arn = "arn:aws:acm:us-west-1:341268829071:certificate/af2cc82f-53a5-469a-bdc9-b72bf3d6ec4b"

# event-tracker
et_instance_type       = "t3.large"
et_ag_min              = "0"
et_ag_max              = "0"
et_java_opts           = "-server -Xms1g -Xmx1g -Xss512k -XX:+UseCompressedOops"
et_log_level           = "ERROR"
et_cert                = "*.edreadyservices.org"
events_ecs_image       = ""
events_min_capacity    = "1"
events_max_capacity    = "1"
events_ecs_cpu         = 256
events_ecs_memory      = 1024
events_certificate_arn = "arn:aws:acm:us-west-1:341268829071:certificate/3f39c899-4a44-491b-a4eb-f3c0a9a55942"
