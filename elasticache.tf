resource "aws_security_group" "one_roster_redis_security_group" {
  # checkov:skip=CKV_AWS_382
  name        = "${terraform.workspace}-one-roster"
  description = "Allow inbound Redis traffic"

  vpc_id = module.vpc.vpc_id

  ingress {
    description = "Allow access to Redis"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all outbound traffic
  egress {
    description = "Allow outbound HTTP from the internet"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Create a subnet group for ElastiCache
resource "aws_elasticache_subnet_group" "one_roster_redis_subnet_group" {
  name       = "${terraform.workspace}-one-roster-redis-subnet-group"
  subnet_ids = module.vpc.private_subnets
}

resource "aws_elasticache_cluster" "one_roster_redis_cluster" {
  cluster_id               = "${terraform.workspace}-one-roster"
  engine                   = "redis"
  node_type                = "cache.t3.micro"
  num_cache_nodes          = 1
  parameter_group_name     = "default.redis7"
  subnet_group_name        = aws_elasticache_subnet_group.one_roster_redis_subnet_group.name
  security_group_ids       = [aws_security_group.one_roster_redis_security_group.id]
  port                     = 6379
  snapshot_retention_limit = 1
}

resource "aws_kms_key" "kms_key_one_roster" {
  description              = "Used to sign JWT tokens for One Roster"
  key_usage                = "SIGN_VERIFY"
  customer_master_key_spec = "RSA_2048"
  is_enabled               = true
  enable_key_rotation      = false

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "EnableRootPermissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "AllowLambdaAccess"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_kms_alias" "kms_alias_one_roster" {
  name          = "alias/${terraform.workspace}/LTI1_3/nroc/RSA_2048"
  target_key_id = aws_kms_key.kms_key_one_roster.id
}