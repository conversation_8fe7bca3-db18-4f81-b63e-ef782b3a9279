name: Terratest

on:
  push:
    branches:
      - '!main'
  pull_request:
    branches:
      - main

env:
  TF_VERSION: 0.13.0
  GO_VERSION: 1.16.3

jobs:
  validate:
    runs-on: ubuntu-latest
    name: Terraform Validate
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_version: env.TF_VERSION
          terraform_wrapper: false

      - name: Terraform init
        run: terraform init

      - name: Validate Module
        env:
          AWS_REGION: 'eu-west-1'
        run: terraform validate

      - name: Validate Examples
        run: |
          for example in $(find examples -maxdepth 1 -mindepth 1 -type d); do
            cd ${example}
            terraform init
            terraform validate
            cd -
          done

  Terratests:
    needs: validate
    name: Terratests
    runs-on: ubuntu-latest
    env:
      working-directory: ./test

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Install Go
      uses: actions/setup-go@v2
      with:
        go-version: ${{ env.GO_VERSION }}
    - name: Install dependencies
      run: |
        go mod download
        go get -u golang.org/x/lint/golint
      working-directory: ${{ env.working-directory }}
    - name: Run checks
      run: |
        go vet .
        golint .
      working-directory: ${{ env.working-directory }}

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: eu-west-1
        role-skip-session-tagging: true
        role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
        role-duration-seconds: 1800

    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v1
      with:
        terraform_version: env.TF_VERSION
        terraform_wrapper: false

    - name: Run Go Tests
      run: go test -v -timeout 30m
      working-directory: ${{ env.working-directory }}
