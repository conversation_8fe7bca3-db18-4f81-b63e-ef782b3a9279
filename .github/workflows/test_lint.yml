name: Lint Tests

on:
  workflow_dispatch:
  push:
    branches: [main]
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.22.x

      - name: Add GOBIN to PATH
        run: echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
        shell: bash

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Full git history
        run: |
          git fetch --prune --unshallow

      - name: <PERSON><PERSON> deps
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Lint
        run: make lint

      - name: Check links
        uses: gaurav-nelson/github-action-markdown-link-check@v1
        with:
          use-quiet-mode: 'yes'
          use-verbose-mode: 'yes'
          config-file: '.markdownlinkcheck.json'
          folder-path: 'website/docs'
          file-extension: '.markdown'
