name: 'Stale Issue/PR'

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'

permissions: read-all

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v8
        id: stale
        with:
          ascending: true
          close-issue-message: 'Issue closed due to inactivity.'
          close-pr-message: 'Pull request closed due to inactivity.'
          days-before-close: 10
          days-before-stale: 30
          stale-issue-label: stale
          stale-pr-label: stale
          # Not stale if have this labels
          exempt-issue-labels: 'bug,enhancement'
          exempt-pr-labels: 'bug,enhancement'
          operations-per-run: 100
          stale-issue-message: |
            This issue has been automatically marked as stale because it has been open 30 days
            with no activity. Remove stale label or comment or this issue will be closed in 10 days
          stale-pr-message: |
            This PR has been automatically marked as stale because it has been open 30 days
            with no activity. Remove stale label or comment or this PR will be closed in 10 days
