AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Sends log data from CloudWatch Logs to New Relic Infrastructure (Cloud integrations) and New Relic Logging
Parameters:
  NRLicenseKey:
    Type: String
    Description: Your NewRelic license key.
    NoEcho: true
  NRLoggingEnabled:
    Type: String
    Description: Determines if logs are forwarded to New Relic Logging
    Default: 'True'
    AllowedValues:
      - 'True'
      - 'False'
  NRInfraLogging:
    Type: String
    Description: Determines if logs are forwarded to New Relic Infrastructure
    Default: 'True'
    AllowedValues:
      - 'True'
      - 'False'
  MemorySize:
    Type: Number
    Description: Memory size for the New Relic Log Ingestion Lambda function
    Default: 128
  Timeout:
    Type: Number
    Description: Timeout for the New Relic Log Ingestion Lambda function
    Default: 30
  FunctionRole:
    Type: String
    Description: |
      IAM Role name that this function will assume. Should provide the AWSLambdaBasicExecutionRole policy. If not
      specified, an appropriate Role will be created, which will require CAPABILITY_IAM to be acknowledged.
    Default: ''
  LogIngestionFunctionArnExportName:
    Type: String
    Description: |
      The export name suffix for the function ARN
    Default: LogIngestionFunctionArn
  PermissionsBoundary:
    Type: String
    Description: IAM Role PermissionsBoundary (optional)
    Default: ''
  DebugLoggingEnabled:
    Type: String
    Description: A boolean to determine if you want to output debug messages in the CloudWatch console
    Default: "false"

Conditions:
  NoRole: !Equals ['', !Ref FunctionRole]
  NoCap: !Not [ !Equals ['', !Ref FunctionRole] ]
  HasPermissionBoundary: !Not [ !Equals ['', !Ref PermissionsBoundary] ]

Metadata:
  AWS::ServerlessRepo::Application:
    Name: NewRelic-log-ingestion
    Description: Send log data from CloudWatch Logs to New Relic Infrastructure (Cloud Integrations) and New Relic Logging.
    Author: New Relic
    SpdxLicenseId: Apache-2.0
    LicenseUrl: LICENSE
    ReadmeUrl: README.md
    HomePageUrl: https://github.com/newrelic/aws-log-ingestion
    SemanticVersion: 2.9.5
    SourceCodeUrl: https://github.com/newrelic/aws-log-ingestion

Resources:
  NewRelicLogIngestionFunctionNoCap:
    Type: AWS::Serverless::Function
    Condition: NoCap
    DeletionPolicy: Delete
    Properties:
      CodeUri: src/
      Description: Sends log data from CloudWatch Logs to New Relic Infrastructure (Cloud integrations) and New Relic Logging
      Handler: function.lambda_handler
      FunctionName: !Join ['-', ['newrelic-log-ingestion', !Select [4, !Split ['-', !Select [2, !Split ['/', !Ref AWS::StackId]]]]]]
      MemorySize:
        Ref: MemorySize
      Runtime: python3.11
      PermissionsBoundary: !If [ HasPermissionBoundary, !Ref PermissionsBoundary, !Ref AWS::NoValue ]
      Role: !Sub arn:${AWS::Partition}:iam::${AWS::AccountId}:role/${FunctionRole}
      Timeout:
        Ref: Timeout
      Environment:
        Variables:
          LICENSE_KEY: !Ref NRLicenseKey
          LOGGING_ENABLED: !Ref NRLoggingEnabled
          INFRA_ENABLED: !Ref NRInfraLogging
          DEBUG_LOGGING_ENABLED: !Ref DebugLoggingEnabled
  NewRelicLogIngestionFunction:
    Type: AWS::Serverless::Function
    Condition: NoRole
    DeletionPolicy: Delete
    Properties:
      CodeUri: src/
      Description: Sends log data from CloudWatch Logs to New Relic Infrastructure (Cloud integrations) and New Relic Logging
      Handler: function.lambda_handler
      FunctionName: !Join ['-', ['newrelic-log-ingestion', !Select [4, !Split ['-', !Select [2, !Split ['/', !Ref AWS::StackId]]]]]]
      MemorySize:
        Ref: MemorySize
      Runtime: python3.11
      PermissionsBoundary: !If [ HasPermissionBoundary, !Ref PermissionsBoundary, !Ref AWS::NoValue ]
      Timeout:
        Ref: Timeout
      Environment:
        Variables:
          LICENSE_KEY: !Ref NRLicenseKey
          LOGGING_ENABLED: !Ref NRLoggingEnabled
          INFRA_ENABLED: !Ref NRInfraLogging
          DEBUG_LOGGING_ENABLED: !Ref DebugLoggingEnabled
  LambdaInvokePermissionNoCap:
    Type: AWS::Lambda::Permission
    Condition: NoCap
    Properties:
      FunctionName: !GetAtt NewRelicLogIngestionFunctionNoCap.Arn
      Action: lambda:InvokeFunction
      Principal: !Sub logs.${AWS::Region}.amazonaws.com
      SourceArn: !Sub arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:*

  LambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Condition: NoRole
    Properties:
      FunctionName: !GetAtt NewRelicLogIngestionFunction.Arn
      Action: lambda:InvokeFunction
      Principal: !Sub logs.${AWS::Region}.amazonaws.com
      SourceArn: !Sub arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:*

Outputs:
  LogIngestionFunctionArn:
    Condition: NoRole
    Description: Log ingestion lambda function ARN
    Value: !GetAtt NewRelicLogIngestionFunction.Arn
    Export:
      Name: !Sub "${AWS::StackName}-${LogIngestionFunctionArnExportName}"
  LogIngestionFunctionNoCapArn:
    Condition: NoCap
    Description: Log ingestion lambda function ARN (custom role)
    Value: !GetAtt NewRelicLogIngestionFunctionNoCap.Arn
    Export:
      Name: !Sub "${AWS::StackName}-${LogIngestionFunctionArnExportName}"
