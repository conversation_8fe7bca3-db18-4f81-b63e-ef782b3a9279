resource "aws_elastic_beanstalk_environment" "event_tracker" {
  # checkov:skip=CKV_AWS_340
  # checkov:skip=CKV_AWS_312
  name                = "${terraform.workspace}-events"
  application         = data.aws_elastic_beanstalk_application.event_tracker.id
  cname_prefix        = "${terraform.workspace}-events"
  solution_stack_name = data.aws_elastic_beanstalk_solution_stack.springbootCorreto17.name
  version_label       = data.external.events_version.result.version_label

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = var.et_ag_min
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = var.et_ag_max
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "EC2KeyName"
    value     = var.ec2_key
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "InstanceType"
    value     = var.et_instance_type
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "SecurityGroups"
    value     = aws_security_group.et_app_sg.id
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.et_profile.name
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "SSHSourceRestriction"
    value     = "tcp,22,22,${var.vpn_ip}"
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "DisableIMDSv1"
    value     = "true"
  }

  setting {
    namespace = "aws:elasticbeanstalk:command"
    name      = "DeploymentPolicy"
    value     = var.deployment_policy
  }

  setting {
    namespace = "aws:autoscaling:updatepolicy:rollingupdate"
    name      = "RollingUpdateEnabled"
    value     = var.rolling_update
  }

  setting {
    namespace = "aws:autoscaling:updatepolicy:rollingupdate"
    name      = "RollingUpdateType"
    value     = "Health"
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "VPCId"
    value     = module.vpc.vpc_id
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "Subnets"
    value     = join(",", sort(module.vpc.private_subnets))
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "ELBSubnets"
    value     = join(",", sort(module.vpc.public_subnets))
  }

  #uncomment after the first beanstlak env creation
  setting {
    namespace = "aws:elasticbeanstalk:environment:process:default"
    name      = "HealthCheckPath"
    value     = var.et_health_check_url
  }

  setting {
    namespace = "aws:elbv2:loadbalancer"
    name      = "ManagedSecurityGroup"
    value     = aws_security_group.et_lb_sg.id
  }

  setting {
    namespace = "aws:elbv2:loadbalancer"
    name      = "SecurityGroups"
    value     = aws_security_group.et_lb_sg.id
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = data.aws_acm_certificate.event_tracker.arn
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLPolicy"
    value     = var.elb_ssl_policy
  }

  setting {
    namespace = "aws:elasticbeanstalk:application"
    name      = "Application Healthcheck URL"
    value     = "HTTPS:443${var.et_health_check_url}"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SPRING_DATASOURCE_PASSWORD"
    value     = var.et_db_password
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SPRING_DATASOURCE_URL"
    value     = var.et_db_url
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SPRING_DATASOURCE_USERNAME"
    value     = var.et_db_username
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SPRING_PROFILES_ACTIVE"
    value     = var.spring_profiles
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ET_SQS_EVENTS_QUEUE"
    value     = aws_sqs_queue.et_queue.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ET_SQS_STUDENT_SUBMISSIONS_QUEUE"
    value     = aws_sqs_queue.event_tracker_student_submissions.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "JAVA_OPTS"
    value     = var.et_java_opts
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NEW_RELIC_APP_NAME"
    value     = "${terraform.workspace} event tracker"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NEW_RELIC_LICENSE_KEY"
    value     = var.new_relic_license_key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ET_SQS_NOTIFICATIONS_QUEUE"
    value     = aws_sqs_queue.et_notifications.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ET_SQS_USER_NOTIFICATIONS_QUEUE"
    value     = aws_sqs_queue.et_user_notifications.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "TOKENS_GENERAL_SHARED_SECRET"
    value     = var.nroc_services_secret_key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NROC_SERVICES_SECRET_KEY"
    value     = var.nroc_services_secret_key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "logging.level.root"
    value     = var.et_log_level
  }

  // Notifications variables
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SCHEDULED_NOTIFICATIONS_DISTRIBUTE_MAX-NOTIFICATIONS-PER-QUEUE-MESSAGE"
    value     = "100"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SCHEDULED_NOTIFICATIONS_DISTRIBUTE_THROUGHPUT"
    value     = "100000"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SCHEDULED_NOTIFICATIONS_NOMILESTONEPROGRESS_OLDESTEVENTDURATIONINDAYS"
    value     = "7"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SCHEDULED_TASKS_TOTAL"
    value     = "8"
  }

  //Rewards variables

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ET_SQS_REWARDS_QUEUE"
    value     = aws_sqs_queue.event_tracker_rewards.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GREENLIGHT_PASSWORD"
    value     = var.et_greenlight_password
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GREENLIGHT_URL"
    value     = var.et_greenlight_url
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GREENLIGHT_USERNAME"
    value     = var.et_greenlight_username
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AWS_GL_SECRET"
    value     = var.et_aws_greenlight_secret_name
  }

  setting {
    namespace = "aws:elasticbeanstalk:managedactions"
    name      = "ManagedActionsEnabled"
    value     = "false"
  }

  # Other settings
  dynamic "setting" {
    for_each = var.et_other_settings
    iterator = set_et
    content {
      namespace = set_et.value["namespace"]
      name      = set_et.value["name"]
      value     = set_et.value["value"]
    }
  }

  # General Elastic Benastalk Environment settings
  dynamic "setting" {
    for_each = var.general_eb_environment_settings
    iterator = set_et
    content {
      namespace = "aws:elasticbeanstalk:environment"
      name      = set_et.value["name"]
      value     = set_et.value["value"]
    }
  }

  # Elastic Beanstalk Commands settings
  dynamic "setting" {
    for_each = var.et_eb_commands
    iterator = set_et
    content {
      namespace = "aws:elasticbeanstalk:command"
      name      = set_et.value["name"]
      value     = set_et.value["value"]
    }
  }

  # General Auto Scaling Trigger settings
  dynamic "setting" {
    for_each = var.general_autoscaling_trigger
    iterator = set_et
    content {
      namespace = "aws:autoscaling:trigger"
      name      = set_et.value["name"]
      value     = set_et.value["value"]
    }
  }

  # Elastic Beanstalk Application Environment settings
  dynamic "setting" {
    for_each = var.et_eb_application_environment
    iterator = set_et
    content {
      namespace = "aws:elasticbeanstalk:application:environment"
      name      = set_et.value["name"]
      value     = set_et.value["value"]
    }
  }

  tags = {
    Environment = terraform.workspace
    Application = "event-tracker"
  }
}

data "aws_lb_listener" "http_listener_event_tracker" {
  load_balancer_arn = aws_elastic_beanstalk_environment.event_tracker.load_balancers[0]
  port              = 80
}
resource "aws_lb_listener_rule" "redirect_http_to_https_event_tracker" {
  listener_arn = data.aws_lb_listener.http_listener_event_tracker.arn
  priority     = 1
  action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }
}
