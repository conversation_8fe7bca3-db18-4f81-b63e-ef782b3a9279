
# resource "aws_elastic_beanstalk_environment" "art" {
#   name                = "${terraform.workspace}-reporting"
#   application         = data.aws_elastic_beanstalk_application.art.id
#   cname_prefix        = "${terraform.workspace}-reporting"
#   solution_stack_name = data.aws_elastic_beanstalk_solution_stack.springbootCorreto17.name
#   version_label       = data.external.art_version.result.version_label

#   setting {
#     namespace = "aws:autoscaling:asg"
#     name      = "MinSize"
#     value     = var.art_ag_min
#   }

#   setting {
#     namespace = "aws:autoscaling:asg"
#     name      = "MaxSize"
#     value     = var.art_ag_max
#   }

#   setting {
#     namespace = "aws:autoscaling:launchconfiguration"
#     name      = "EC2KeyName"
#     value     = var.ec2_key
#   }

#   setting {
#     namespace = "aws:autoscaling:launchconfiguration"
#     name      = "InstanceType"
#     value     = var.art_instance_type
#   }

#   setting {
#     namespace = "aws:autoscaling:launchconfiguration"
#     name      = "SecurityGroups"
#     value     = aws_security_group.art_app_sg.id
#   }

#   setting {
#     namespace = "aws:autoscaling:launchconfiguration"
#     name      = "IamInstanceProfile"
#     value     = "edready-development"
#     #value     = aws_iam_instance_profile.art_profile.name
#   }

#   setting {
#     namespace = "aws:autoscaling:launchconfiguration"
#     name      = "DisableIMDSv1"
#     value     = "false"
#   }

#   setting {
#     namespace = "aws:autoscaling:launchconfiguration"
#     name      = "SSHSourceRestriction"
#     value     = "tcp,22,22,${var.vpn_ip}"
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:command"
#     name      = "DeploymentPolicy"
#     value     = var.deployment_policy
#   }

#   setting {
#     namespace = "aws:autoscaling:updatepolicy:rollingupdate"
#     name      = "RollingUpdateEnabled"
#     value     = var.rolling_update
#   }

#   setting {
#     namespace = "aws:autoscaling:updatepolicy:rollingupdate"
#     name      = "RollingUpdateType"
#     value     = "Health"
#   }

#   setting {
#     namespace = "aws:ec2:vpc"
#     name      = "VPCId"
#     value     = module.vpc.vpc_id
#   }

#   setting {
#     namespace = "aws:ec2:vpc"
#     name      = "Subnets"
#     value     = join(",", sort(module.vpc.private_subnets))
#   }

#   setting {
#     namespace = "aws:ec2:vpc"
#     name      = "ELBSubnets"
#     value     = join(",", sort(module.vpc.public_subnets))
#   }

#   #uncomment after the first creation
#   setting {
#     namespace = "aws:elasticbeanstalk:environment:process:default"
#     name      = "HealthCheckPath"
#     value     = var.art_health_check_url
#   }

#   setting {
#     namespace = "aws:elbv2:loadbalancer"
#     name      = "ManagedSecurityGroup"
#     value     = aws_security_group.art_lb_sg.id
#   }

#   setting {
#     namespace = "aws:elbv2:loadbalancer"
#     name      = "SecurityGroups"
#     value     = aws_security_group.art_lb_sg.id
#   }

#   setting {
#     namespace = "aws:elbv2:listener:443"
#     name      = "SSLCertificateArns"
#     value     = data.aws_acm_certificate.art.arn
#   }

#   setting {
#     namespace = "aws:elbv2:listener:443"
#     name      = "SSLPolicy"
#     value     = var.elb_ssl_policy
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application"
#     name      = "Application Healthcheck URL"
#     value     = "HTTPS:443${var.art_health_check_url}"
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_DATASOURCE_PASSWORD"
#     value     = var.art_db_password
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_DATASOURCE_USERNAME"
#     value     = var.art_db_username
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_DATASOURCE_URL"
#     value     = var.main_db_replica_url
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "EDREADY_HOST"
#     value     = var.art_server_host
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "JAVA_OPTS"
#     value     = var.art_java_opts
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "NEW_RELIC_APP_NAME"
#     value     = "${terraform.workspace} advance reporting tool"
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "NEW_RELIC_LICENSE_KEY"
#     value     = var.new_relic_license_key
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_MAIL_HOST"
#     value     = var.mail_host
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_MAIL_PASSWORD"
#     value     = var.mail_password
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_MAIL_PORT"
#     value     = var.art_mail_port
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "SPRING_MAIL_USERNAME"
#     value     = var.mail_username
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:application:environment"
#     name      = "EDREADY_NROCSERVICESSECRETKEY"
#     value     = var.nroc_services_secret_key
#   }

#   setting {
#     namespace = "aws:elbv2:listener:443"
#     name      = "SSLPolicy"
#     value     = var.elb_ssl_policy
#   }

#   setting {
#     namespace = "aws:elasticbeanstalk:managedactions"
#     name      = "ManagedActionsEnabled"
#     value     = "false"
#   }

#   # Other settings
#   dynamic "setting" {
#     for_each = var.art_other_settings
#     iterator = set_art
#     content {
#       namespace = set_art.value["namespace"]
#       name      = set_art.value["name"]
#       value     = set_art.value["value"]
#     }
#   }

#   # General Elastic Beanstalk Environment settings
#   dynamic "setting" {
#     for_each = var.general_eb_environment_settings
#     iterator = set_art
#     content {
#       namespace = "aws:elasticbeanstalk:environment"
#       name      = set_art.value["name"]
#       value     = set_art.value["value"]
#     }
#   }

#   # EB commands settings
#   dynamic "setting" {
#     for_each = var.art_eb_commands
#     iterator = set_art
#     content {
#       namespace = "aws:elasticbeanstalk:command"
#       name      = set_art.value["name"]
#       value     = set_art.value["value"]
#     }
#   }

#   # Autoscaling trigger settings
#   dynamic "setting" {
#     for_each = var.general_autoscaling_trigger
#     iterator = set_art
#     content {
#       namespace = "aws:autoscaling:trigger"
#       name      = set_art.value["name"]
#       value     = set_art.value["value"]
#     }
#   }

#   tags = {
#     Environment = terraform.workspace
#     Application = "art"
#   }
# }

# data "aws_lb_listener" "http_listener_art" {
#   load_balancer_arn = aws_elastic_beanstalk_environment.art.load_balancers[0]
#   port              = 80
# }
# resource "aws_lb_listener_rule" "redirect_http_to_https_art" {
#   listener_arn = data.aws_lb_listener.http_listener_art.arn
#   priority     = 1
#   action {
#     type = "redirect"

#     redirect {
#       port        = "443"
#       protocol    = "HTTPS"
#       status_code = "HTTP_301"
#     }
#   }

#   condition {
#     path_pattern {
#       values = ["/*"]
#     }
#   }
# }