# provider "aws" {
#   region = local.region
# }

# provider "kubernetes" {
#   host                   = module.eks_blueprints.cluster_endpoint
#   cluster_ca_certificate = base64decode(module.eks_blueprints.cluster_certificate_authority_data)
#   token                  = data.aws_eks_cluster_auth.this.token
# }

# provider "helm" {
#   kubernetes {
#     host                   = module.eks_blueprints.cluster_endpoint
#     cluster_ca_certificate = base64decode(module.eks_blueprints.cluster_certificate_authority_data)
#     token                  = data.aws_eks_cluster_auth.this.token
#   }
# }

# data "aws_eks_cluster_auth" "this" {
#   name = module.eks_blueprints.cluster_name
# }

# # data "aws_lb" "nginx-nlb" {
# #   tags = {
# #     "service.k8s.aws/stack" = "ingress-nginx/ingress-nginx-controller"
# #   }
# #   depends_on = [
# #     module.eks_blueprints_kubernetes_addons
# #   ]
# # }

# locals {
#   region             = "us-west-1"
#   remote_bucket_name = "mite-terraform-states"
#   remote_vpc_tfstate = "IaC/edready.tfstate"

#   tags = {
#     Blueprint  = "${terraform.workspace}"
#     GithubRepo = "github.com/aws-ia/terraform-aws-eks-blueprints"
#   }
# }

# data "terraform_remote_state" "vpc" {
#   backend   = "s3"
#   workspace = terraform.workspace
#   config = {
#     bucket = local.remote_bucket_name
#     key    = local.remote_vpc_tfstate
#     region = "${local.region}"
#   }
# }

# #---------------------------------------------------------------
# # EKS Blueprints
# #---------------------------------------------------------------

# module "eks_blueprints" {
#   source  = "terraform-aws-modules/eks/aws"
#   version = "~> 19.20.0"

#   cluster_name    = terraform.workspace
#   cluster_version = "1.29"

#   vpc_id                         = data.terraform_remote_state.vpc.outputs.vpc_id
#   subnet_ids                     = data.terraform_remote_state.vpc.outputs.private_subnets
#   cluster_endpoint_public_access = true

#   enable_irsa = true


#   eks_managed_node_groups = {
#     "${var.node_name}" = {
#       node_group_name         = "managed-ondemand"
#       instance_types          = var.instance_types
#       min_size                = var.min_size
#       max_size                = var.max_size
#       desired_size            = var.desired_size
#       disk_size               = var.disk_size
#       pre_bootstrap_user_data = <<-EOT
#         #!/bin/bash
#         LINE_NUMBER=$(grep -n "KUBELET_EXTRA_ARGS=\$2" /etc/eks/bootstrap.sh | cut -f1 -d:)
#         REPLACEMENT="\ \ \ \ \ \ KUBELET_EXTRA_ARGS=\$(echo \$2 | sed -s -E 's/--max-pods=[0-9]+/--max-pods=110/g')"
#         sed -i '/KUBELET_EXTRA_ARGS=\$2/d' /etc/eks/bootstrap.sh
#         sed -i "$${LINE_NUMBER}i $${REPLACEMENT}" /etc/eks/bootstrap.sh
#       EOT
#     }
#   }

#   cluster_enabled_log_types = []

#   tags = local.tags
# }


# module "eks_blueprints_kubernetes_addons" {
#   source  = "aws-ia/eks-blueprints-addons/aws"
#   version = "1.12.0"



#   #Establish EKS cluster connection
#   cluster_endpoint  = module.eks_blueprints.cluster_endpoint
#   oidc_provider_arn = module.eks_blueprints.oidc_provider_arn
#   cluster_version   = module.eks_blueprints.cluster_version
#   cluster_name      = terraform.workspace

#   # EKS Managed Add-ons
#   eks_addons = {
#     aws-ebs-csi-driver = {
#       most_recent = true
#     }
#     coredns = {
#       most_recent = true
#     }
#     vpc-cni = {
#       most_recent = true
#       configuration_values = jsonencode({
#         env = {
#           ENABLE_PREFIX_DELEGATION = "true"
#           WARM_PREFIX_TARGET       = "1"
#         }
#       })
#     }
#     kube-proxy = {
#       most_recent = true
#     }
#   }

#   # Add-ons

#   enable_aws_load_balancer_controller = true
#   aws_load_balancer_controller = {
#     name       = "aws-load-balancer-controller"
#     chart      = "aws-load-balancer-controller"
#     repository = "https://aws.github.io/eks-charts"
#     version    = "1.6.2"
#     namespace  = "kube-system"
#   }

#   #needed by the autoscalers (just the autoscalers)
#   enable_metrics_server = true
#   metrics_server = {
#     name       = "metrics-server"
#     repository = "https://kubernetes-sigs.github.io/metrics-server/"
#     chart      = "metrics-server"
#     version    = "3.11.0"
#     namespace  = "kube-system"
#     timeout    = "1200"
#   }

#   #Scale node 
#   enable_cluster_autoscaler = true
#   cluster_autoscaler = {
#     name          = "cluster-autoscaler"
#     chart_version = "9.34.0"
#     repository    = "https://kubernetes.github.io/autoscaler"
#     namespace     = "kube-system"
#   }


#   # enable_ingress_nginx = true
#   # ingress_nginx = {
#   #   name          = "ingress-nginx"
#   #   chart_version = "4.9.0"
#   #   repository    = "https://kubernetes.github.io/ingress-nginx"
#   #   namespace     = "ingress-nginx"
#   #   values = [templatefile("${path.module}/nginx-values.yaml", {
#   #   })]
#   # }


#   enable_cert_manager = true
#   cert_manager = {
#     chart_version    = "v1.13.2"
#     namespace        = "cert-manager"
#     create_namespace = true
#   }

#   depends_on = [module.eks_blueprints]

#   tags = local.tags
# }
