output "eks_cluster_id" {
  description = "EKS cluster ID"
  value       = module.eks_blueprints.cluster_id
}

output "eks_managed_nodegroups" {
  description = "EKS managed node groups"
  value       = module.eks_blueprints.eks_managed_node_groups
}


# Region used for Terratest
output "region" {
  description = "AWS region"
  value       = local.region
}

# output "loadbalancer" {
#   description = "Network Load Balancer cretead by nginx ingress"
#   value       = data.aws_lb.nginx-nlb.dns_name
# }

output "vpc_id" {
  description = "VPC ID"
  value       = data.terraform_remote_state.vpc.outputs.vpc_id
}
