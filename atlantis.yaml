version: 3
parallel_plan: true
parallel_apply: true

projects:
- name: development
  dir: .
  workspace: development
  terraform_version: 1.8.1
  workflow: development-wf
  autoplan:
    enabled: false

- name: staging
  dir: .
  workspace: staging
  terraform_version: 1.8.1
  workflow: staging-wf
  autoplan:
    enabled: false

- name: testing
  dir: .
  workspace: testing
  terraform_version: 1.8.1
  workflow: testing-wf
  autoplan:
    enabled: false

- name: production
  dir: .
  workspace: production
  terraform_version: 1.8.1
  apply_requirements: [approved]
  workflow: production-wf
  autoplan:
    enabled: false

- name: production-dr
  dir: .
  workspace: production-dr
  terraform_version: 1.8.1
  apply_requirements: [approved]
  workflow: production-dr-wf
  autoplan:
    enabled: false

workflows:
  development-wf:
    plan:
      steps:
      - run: git config --global --add safe.directory "*"
      - run: ./decrypt-config.sh
      - init
      - plan:
          extra_args: ["-var-file", "development.tfvars", "-var-file", "common-development.tfvars"]
      - run: echo "-------------- Workflow finished --------------------"
    apply:
      steps:
      - apply

  staging-wf:
    plan:
      steps:
      - run: git config --global --add safe.directory "*"
      - run: ./decrypt-config.sh
      - init
      - plan:
          extra_args: ["-var-file", "staging.tfvars", "-var-file", "common-staging.tfvars"]
      - run: echo "-------------- Workflow finished --------------------"    
    apply:
      steps:
      - apply

  testing-wf:
    plan:
      steps:
      - run: git config --global --add safe.directory "*"
      - run: ./decrypt-config.sh
      - init
      - plan:
          extra_args: ["-var-file", "testing.tfvars", "-var-file", "common-testing.tfvars"]
      - run: echo "-------------- Workflow finished --------------------"
    apply:
      steps:
      - apply
  
  production-wf:
    plan:
      steps:
      - run: git config --global --add safe.directory "*"
      - run: ./decrypt-config.sh
      - init
      - plan:
          extra_args: ["-var-file", "production.tfvars", "-var-file", "common-production.tfvars"]
      - run: echo "-------------- Workflow finished --------------------"
    apply:
      steps:
      - apply

  production-dr-wf:
    plan:
      steps:
      - run: git config --global --add safe.directory "*"
      - run: ./decrypt-config.sh
      - init
      - plan:
          extra_args: ["-var-file", "production-dr.tfvars", "-var-file", "common-production-dr.tfvars"]
      - run: echo "-------------- Workflow finished --------------------"
    apply:
      steps:
      - apply