#!/bin/bash

# Script to load SQL data into the containerized MySQL database
# Usage: ./load-database.sh [sql_file]
# Successfully loaded local_dump.sql (19GB) in ~35 minutes

set -e

# Default SQL file
SQL_FILE="${1:-database/local_dump.sql}"

# Database connection details
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="gradeservice"
DB_USER="root"
DB_PASSWORD="rootpassword"

echo "🚀 Loading database from: $SQL_FILE"

# Check if SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo "❌ Error: SQL file '$SQL_FILE' not found!"
    echo "📝 Available SQL files in database/ directory:"
    ls -la database/*.sql 2>/dev/null || echo "   No .sql files found"
    exit 1
fi

# Check if SQL file is not empty
if [ ! -s "$SQL_FILE" ]; then
    echo "⚠️  Warning: SQL file '$SQL_FILE' is empty!"
    echo "📝 Please add your database dump content to this file first."
    exit 1
fi

# Show file info
FILE_SIZE=$(du -h "$SQL_FILE" | cut -f1)
echo "📊 File size: $FILE_SIZE"
if [[ "$FILE_SIZE" == *"G"* ]]; then
    echo "📊 Estimated time: 30-60 minutes for large files"
else
    echo "📊 Estimated time: 1-5 minutes"
fi
echo ""

# Check if containers are running
echo "🔍 Checking if containers are running..."
if ! docker-compose ps | grep -q "gradeservice-mysql.*Up"; then
    echo "❌ MySQL container is not running. Starting containers..."
    ./docker-helper.sh start
    echo "⏳ Waiting for MySQL to be ready..."
    sleep 15
fi

# Test database connection
echo "🔍 Testing database connection..."
if ! docker exec gradeservice-mysql mysql -h localhost -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ Cannot connect to database. Please check if containers are healthy."
    ./docker-helper.sh status
    exit 1
fi

echo "✅ Database connection successful!"
echo ""

# Start the import process
echo "🚀 Starting database import..."
echo "📊 File: $SQL_FILE ($FILE_SIZE)"
echo "🕐 Started at: $(date)"
echo ""

# Load the SQL file with progress monitoring for large files
if docker exec -i gradeservice-mysql mysql \
    -h localhost \
    -u "$DB_USER" \
    -p"$DB_PASSWORD" \
    --force \
    "$DB_NAME" < "$SQL_FILE"; then
    echo ""
    echo "✅ Database loaded successfully!"
    echo "🕐 Completed at: $(date)"
    echo ""
    echo "🎉 You can now test login credentials at: http://localhost:8080/gradeservice/"
    echo ""
    echo "📊 Database loading summary:"
    echo "   File: $SQL_FILE"
    echo "   Size: $FILE_SIZE"
    echo "   Database: $DB_NAME"
    echo "   Host: $DB_HOST:$DB_PORT"
else
    echo "❌ Database import failed!"
    echo "💡 Troubleshooting tips:"
    echo "   1. Check Docker memory allocation (increase to 8GB+)"
    echo "   2. Check available disk space"
    echo "   3. Check container logs: docker-compose logs mysql"
    exit 1
fi
