### Common ###
data "aws_iam_policy_document" "role_assume_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "nroc_beanstalk_web_tier" {
  statement {
    actions = [
      "s3:Get*",
      "s3:list*",
      "s3:PutObject",
    ]

    resources = [
      "arn:aws:s3:::elasticbeanstalk-*",
      "arn:aws:s3:::elasticbeanstalk-*/*,",
    ]
  }

  statement {
    actions = [
      "xray:PutTraceSegments",
      "xray:PutTelemetryRecords",
    ]

    resources = [
      "arn:aws:xray:${local.aws_deployment_region}:*:*",
    ]
  }

  statement {
    actions = [
      "logs:PutlogEvents",
      "logs:CreateLogStream",
      "logs:CreateLogGroup",
      "logs:DescribeLogStreams",
      "logs:DescribeLogGrooups",
    ]

    resources = [
      "arn:aws:logs:*:*:log-group:/aws/elasticbeanstalk*",
    ]
  }
}

data "aws_iam_policy_document" "edready_s3_access" {
  statement {
    actions = [
      "s3:*"
    ]

    resources = [
      "arn:aws:s3:::edready-${terraform.workspace}*",
      "arn:aws:s3:::edready-${terraform.workspace}*/*,",
      "arn:aws:s3:::edready-points-tracker-archive*",
      "arn:aws:s3:::edready-points-tracker-archive*/*"
    ]
  }
}

data "aws_iam_policy_document" "edready_s3_access_lifecycle_delete_version" {
  statement {
    actions = [
      "s3:DeleteObject",
    ]

    resources = [
      "arn:aws:s3:::elasticbeanstalk-${local.aws_deployment_region}-341268829071/edready_/*",
    ]
  }
}

data "aws_iam_policy_document" "portal_s3_access_lifecycle_delete_version" {
  statement {
    actions = [
      "s3:DeleteObject",
    ]

    resources = [
      "arn:aws:s3:::elasticbeanstalk-${local.aws_deployment_region}-341268829071/portal_/*",
    ]
  }
}

data "aws_iam_policy_document" "et_s3_access_lifecycle_delete_version" {
  statement {
    actions = [
      "s3:DeleteObject",
    ]

    resources = [
      "arn:aws:s3:::elasticbeanstalk-${local.aws_deployment_region}-341268829071/et/*",
    ]
  }
}

data "aws_iam_policy_document" "reporting_s3_access_lifecycle_delete_version" {
  statement {
    actions = [
      "s3:DeleteObject",
    ]

    resources = [
      "arn:aws:s3:::elasticbeanstalk-${local.aws_deployment_region}-341268829071/reporting_/*",
    ]
  }
}


data "aws_iam_policy_document" "nroc_sqs_access" {
  statement {
    actions = [
      "sqs:*",
    ]

    resources = [
      aws_sqs_queue.et_queue.arn,
      aws_sqs_queue.et_notifications.arn,
      aws_sqs_queue.et_user_notifications.arn,
      aws_sqs_queue.event_tracker_rewards.arn,
      aws_sqs_queue.event_tracker_student_submissions.arn,
      aws_sqs_queue.portal_essay_sync_bridge.arn
    ]
  }
}

data "aws_iam_policy_document" "portal_sqs_access_policy" {
  statement {
    actions = [
      "sqs:DeleteMessage",
      "sqs:GetQueueUrl",
      "sqs:ListDeadLetterSourceQueues",
      "sqs:ChangeMessageVisibility",
      "sqs:PurgeQueue",
      "sqs:ReceiveMessage",
      "sqs:SendMessage",
      "sqs:GetQueueAttributes",
      "sqs:ListQueueTags"
    ]

    resources = [
      aws_sqs_queue.portal_essay_sync_bridge.arn,
    ]
  }
}

data "aws_iam_policy_document" "read_secrets_from_beanstalk" {
  statement {
    actions = [
      "secretsmanager:ListSecrets",
      "secretsmanager:DescribeSecret",
      "secretsmanager:GetRandomPassword",
      "secretsmanager:GetResourcePolicy",
      "secretsmanager:GetSecretValue",
      "secretsmanager:ListSecretVersionIds"
    ]
    resources = [
      "${terraform.workspace}" == "production" ? "arn:aws:secretsmanager:${local.aws_deployment_region}:341268829071:secret:greenligthErrorNotificationARNProduction-c2VUPO" : "arn:aws:secretsmanager:${local.aws_deployment_region}:341268829071:secret:greenligthErrorNotificationARNTesting-pDAOY9"
    ]
  }
}

data "aws_iam_policy_document" "nroc-beanstalk-enhanced-health" {
  statement {
    sid = "ElasticBeanstalkHealthAccess"
    actions = [
      "elasticbeanstalk:PutInstanceStatistics"
    ]

    resources = [
      "arn:aws:elasticbeanstalk:*:*:application/*",
      "arn:aws:elasticbeanstalk:*:*:environment/*",
    ]
  }
}

data "aws_iam_policy_document" "publish_to_gl_error_sns" {
  statement {
    actions = [
      "sns:Publish"
    ]

    resources = [
      "${terraform.workspace}" == "production" ? "arn:aws:sns:${local.aws_deployment_region}:341268829071:Greenligth-New-Error-Notificaction-Production" : "arn:aws:sns:${local.aws_deployment_region}:341268829071:Greenligth-New-Error-Notificaction"
    ]
  }
}

data "aws_iam_policy_document" "nroc_cf_access" {
  # checkov:skip=CKV_AWS_356
  statement {
    actions = [
      "cloudformation:Describe*",
      "cloudformation:EstimateTemplateCost",
      "cloudformation:Get*",
      "cloudformation:List*",
      "cloudformation:ValidateTemplate",
      "autoscaling:*",
    ]
    #checkov:skip=CKV_AWS_111:Policy exception for Cloudformation
    resources = [
      "*"
    ]
  }
}

data "aws_iam_policy_document" "nroc_dynamo" {
  statement {
    actions = [
      "dynamodb:*",
      "dax:*",
      "application-autoscaling:DeleteScalingPolicy",
      "application-autoscaling:DeregisterScalableTarget",
      "application-autoscaling:DescribeScalableTargets",
      "application-autoscaling:DescribeScalingActivities",
      "application-autoscaling:DescribeScalingPolicies",
      "application-autoscaling:PutScalingPolicy",
      "application-autoscaling:RegisterScalableTarget",
      "cloudwatch:DeleteAlarms",
      "cloudwatch:DescribeAlarmHistory",
      "cloudwatch:DescribeAlarms",
      "cloudwatch:DescribeAlarmsForMetric",
      "cloudwatch:GetMetricStatistics",
      "cloudwatch:ListMetrics",
      "cloudwatch:PutMetricAlarm",
      "ec2:DescribeVpcs",
      "ec2:DescribeSubnets",
      "ec2:DescribeSecurityGroups",
      "iam:GetRole",
      "iam:ListRoles",
      "resource-groups:ListGroups",
      "resource-groups:ListGroupResources",
      "resource-groups:GetGroup",
      "resource-groups:GetGroupQuery",
      "resource-groups:DeleteGroup",
      "resource-groups:CreateGroup",
      "tag:GetResources",
    ]

    resources = [
      "arn:aws:dynamodb:${local.aws_deployment_region}:341268829071:table/${terraform.workspace}-PortalAuditEvents",
    ]
  }
}


data "aws_iam_policy_document" "send_data_to_beanstalk" {
  statement {
    actions = [
      "elasticbeanstalk:PutInstanceStatistics",
    ]
    resources = [
      "arn:aws:elasticbeanstalk:*:341268829071:application/*",
      "arn:aws:elasticbeanstalk:*:341268829071:environment/*/*"
    ]
  }
}

data "aws_iam_policy_document" "kms_policy" {
  # checkov:skip=CKV_AWS_356
  statement {
    actions = [
      "kms:GetPublicKey",
      "Kms:Sign",
      "Kms:DescribeKey"
    ]
    resources = ["*"]
  }
}