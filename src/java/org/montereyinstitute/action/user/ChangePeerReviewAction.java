package org.montereyinstitute.action.user;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppUtil;
import gs.hinkleworld.persistence.Hibernate;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.*;
import org.montereyinstitute.model.writingcenter.PeerReviewAssignment;
import org.montereyinstitute.model.writingcenter.PeerReviewUtil;
import org.montereyinstitute.model.writingcenter.WritingCenterState;
import static org.montereyinstitute.tagmodel.ModalDialogForDevEnglishPeerReviewOptions.ASSIGNMENT_ID_PARAM;
import static org.montereyinstitute.tagmodel.ModalDialogForDevEnglishPeerReviewOptions.ASSIGNMENT_ID_VALUE_EMPTY;
import static org.montereyinstitute.tagmodel.ModalDialogForDevEnglishPeerReviewOptions.DRAFT_STEP_PARAM;

/**
 *
 * <AUTHOR> Hinkle, Hinkleworld LLC
 */
@WebServlet("/peer_review/change")
public class ChangePeerReviewAction extends Action {
    private static Logger log = LogManager.getLogger(ChangePeerReviewAction.class);
    
    public static final String NEW_REVIEWER_PARAM = "newPeerReviewer";
    public static final String NEW_REVIEWER_VALUE_NO_CHANGE = "noChange";
    public static final String NEW_REVIEWER_VALUE_CANCEL = "cancel";

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        if( log.isDebugEnabled() ) AppUtil.logRequestParams(request);
        UnitStateForStudentClass unitState = UnitStateForStudentClass.getUnitStateNeverUseSession(request);
        if( unitState == null ){
            throw new Exception("unitState is null");
        }
        log.debug("unitState id="+unitState.getId());
        Student student = unitState.getStudent();
        GSClass gsClass = unitState.getGsClass();
        Course course = CourseNode.Course.getCourse(request, CourseNode.Course.ID_DEV_ENGLISH);
        unitState.loadDevEnglishTransients();
        unitState.initAppUnitNode(course);
        UnitStateForClass unitStateForClass = unitState.getUnitStateForClass();
        unitStateForClass.initAppUnitNode(course);
        Unit unit = unitState.getAppUnitNode();
        WritingCenterState writingCenterState = unitState.getWritingCenterState();

        String assignmentIdString = request.getParameter(ASSIGNMENT_ID_PARAM);
        PeerReviewAssignment assignment = null;
        if( assignmentIdString != null ){
            if( !assignmentIdString.equals(ASSIGNMENT_ID_VALUE_EMPTY) ){
                Long assignmentId = Long.parseLong(assignmentIdString);
                assignment = unitState.getWritingCenterState().getPeerReviewAssignmentById(assignmentId);
            }
        }
        log.debug("assignment: "+assignment);

        String draftStepString = request.getParameter(DRAFT_STEP_PARAM);
        Integer draftStep = Integer.parseInt(draftStepString);
        log.debug("draftStep: "+draftStep);
        
        String newReviewerString = request.getParameter(NEW_REVIEWER_PARAM);
        log.debug("newReviewerString: "+newReviewerString);

        if( newReviewerString != null ){
            if( newReviewerString.equals(NEW_REVIEWER_VALUE_NO_CHANGE)){
                log.warn("newPeerReviewer value 'noChange' should be prevented at client");
            }
            else if( newReviewerString.equals(NEW_REVIEWER_VALUE_CANCEL) ){
                if( assignment != null ){
                    assignment.setCancelled(true);
                    Hibernate.save(assignment);
                    //This check parallels the check in WebserviceRequestToDbUtil.updateActionPendingCodes for a completed review
                    //Check to see if the reviewer of this cancelled assignment now has no remaining assignments
                    if( assignment.getStudentReviewer() != null ){
                        List<PeerReviewAssignment> reviewerAssignments = PeerReviewUtil.getOpenPeerReviewAssignmentsForStudentReviewer(assignment.getStudentReviewer(), gsClass, unitStateForClass);
                        if( reviewerAssignments == null || reviewerAssignments.isEmpty() ){
                            UnitStateForStudentClass reviewerUnitState = UnitStateForStudentClass.getNewOrExisting(assignment.getStudentReviewer(), gsClass, unit.getNumber());
                            reviewerUnitState.removeActionPendingCode(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterPeerReviewAssignedToThisStudent.getIntCode());
                            Hibernate.save(reviewerUnitState);
                        }
                    }
                }
                else{
                    PeerReviewAssignment newAssignment = new PeerReviewAssignment(gsClass, student, unitState.getUnit(), unitState, writingCenterState, null, draftStep);
                    newAssignment.setCancelled(true);
                    Hibernate.save(newAssignment);
                    writingCenterState.addPeerReviewAssignment(newAssignment);
                }
                Hibernate.save(writingCenterState);
                //This check parallels the check in WebserviceRequestToDbUtil.updateActionPendingCodes for a completed review
                //Check to see if the originator/writer of this cancelled assignment now has no remaining assignments
                String actionValue = unitState.getActionPendingCodes().get(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterPeerReviewsNeeded.getIntCode());
                if( actionValue != null ){
                    Set<PeerReviewAssignment> completedOrCancelledAssignments = writingCenterState.getCompletedOrCancelledPeerReviewAssignmentsForDraftStep(draftStep, true);
                    if( completedOrCancelledAssignments != null && completedOrCancelledAssignments.size() == unitState.getUnitStateForClass().getNumberOfPeerReviews() ){
                        unitState.addActionPendingCode(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterPeerReviewsComplete.getIntCode(), actionValue, request, response);
                        unitState.removeActionPendingCode(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterPeerReviewsNeeded.getIntCode());
                        Hibernate.save(unitState);
                    }
                }
            }
            else{ //Change reviewer to a different student
                Student newReviewerStudent = null;
                Set<Student> students = CollectionLoader.getStudents(gsClass);
                if( students != null ){
                    for( Student s : students ){
                        if( s.getStudentId().equals(newReviewerString) ){
                            newReviewerStudent = s;
                            break;
                        }
                    }
                }
                if( newReviewerStudent != null ){
                    if( assignment != null ){
                        Student previousReviewerStudent = assignment.getStudentReviewer();
                        assignment.setStudentReviewer(newReviewerStudent);
                        assignment.setCreatedTime(new Date());
                        assignment.setCancelled(false);
                        Hibernate.save(assignment);
                        //Check to see if the reviewer of this cancelled assignment now has no remaining assignments
                        if( previousReviewerStudent != null ){
                            List<PeerReviewAssignment> previousReviewerAssignments = PeerReviewUtil.getOpenPeerReviewAssignmentsForStudentReviewer(previousReviewerStudent, gsClass, unitStateForClass);
                            if( previousReviewerAssignments == null || previousReviewerAssignments.isEmpty() ){
                                UnitStateForStudentClass previousReviewerUnitState = UnitStateForStudentClass.getNewOrExisting(previousReviewerStudent, gsClass, unit.getNumber());
                                previousReviewerUnitState.removeActionPendingCode(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterPeerReviewAssignedToThisStudent.getIntCode());
                                Hibernate.save(previousReviewerUnitState);
                            }
                        }
                    }
                    else{
                        PeerReviewAssignment newAssignment = new PeerReviewAssignment(gsClass, student, unitState.getUnit(), unitState, writingCenterState, newReviewerStudent, draftStep);
                        Hibernate.save(newAssignment);
                        writingCenterState.addPeerReviewAssignment(newAssignment);
                        Hibernate.save(writingCenterState);
                    }
                    UnitStateForStudentClass reviewerUnitState = UnitStateForStudentClass.getNewOrExisting(newReviewerStudent, gsClass, unit.getNumber());
                    reviewerUnitState.addActionPendingCode(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterPeerReviewAssignedToThisStudent.getIntCode(), "peerReviewAssignment", request, response);
                    Hibernate.save(reviewerUnitState);
                }
                else{
                    log.warn("could not find newReviewerStudent");
                }
            }
        }
        
        response.sendRedirect(AppUtil.getUrl(request, response, "/report/peer_reviews?refresh=true"));
        return null;
    }

}
