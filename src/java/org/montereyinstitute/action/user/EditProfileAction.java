package org.montereyinstitute.action.user;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.Validator;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.User;
import org.montereyinstitute.tagmodel.Navigation;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet(urlPatterns={"/my_settings/*"})
public class EditProfileAction extends Action {
    private static Logger log = LogManager.getLogger(EditProfileAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);

        Validator validator = Validator.getValidator("EditProfile", request);
        validator.setInitialEmail(user.getEmail());
        validator.setInitialUserName(user.getUserName());
        validator.processRequest(request, true, false);
        String emailConflict = validator.getEmailConflict();
        if( emailConflict != null ){
            request.setAttribute("emailConflict", emailConflict);
        }
        if( validator.isTransactionStart() ){
            validator.prepopulateParams(user, user.getUserType());
        }
        log.debug("email: "+user.getEmail());
        Navigation.getNavigation(request, response).setButtonSelected("editProfile", true);
        return "/WEB-INF/jsp/user/editProfile.jsp";
    }
    
}
