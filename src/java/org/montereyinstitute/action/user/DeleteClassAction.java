package org.montereyinstitute.action.user;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppUtil;
import gs.hinkleworld.persistence.Hibernate;
import gs.hinkleworld.core.SessionUtil;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.Institution;
import org.montereyinstitute.model.StudentTabReports;
import org.montereyinstitute.model.Teacher;
import org.montereyinstitute.model.User;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet("/delete_class")
public class DeleteClassAction extends Action {
    private static Logger log = LogManager.getLogger(DeleteClassAction.class);
    
    public static final String REDIRECT_PARAM = "fwd";

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);

        SessionUtil.purgeSessionSingleton(StudentTabReports.class, request);
        GSClass gsClass = GSClass.getGSClassUseSession(request);
        if( gsClass == null ){
            throw new Exception("GSClass is null in ChangeClassStatusAction where classId="+request.getParameter(GSClass.ID_PARAM));
        }
        
        //Check if user has permission to do this action (secondary teacher not allowed)
        if( user.getIsInstitution() ){
            Institution institution = (Institution) user.getUserType();
            if( !institution.equals(gsClass.getInstitution()) ){
                return User.securityError(request, response);
            }
        }
        else if( user.getIsTeacher() ){
            Teacher teacher = (Teacher) user.getUserType();
            if( !teacher.equals(gsClass.getTeacher()) ){
                return User.securityError(request, response);
            }
        }
        
        gsClass.deleteDbReferencesToThisClass();
        Hibernate.flush();
        gsClass = (GSClass) Hibernate.get(GSClass.class, gsClass.getClassId());
        Hibernate.delete(gsClass);
        user.removeClassInCollections(gsClass);
        SessionUtil.purgeSessionSingleton(GSClass.class, request);
        if( request.getParameter(REDIRECT_PARAM) != null ){
            response.sendRedirect(AppUtil.getUrl(request, response, request.getParameter(REDIRECT_PARAM)));
        }
        else{
            response.sendRedirect(AppUtil.getUrl(request, response, "/class"));
        }
        return null;
    }
    
    
}
