package org.montereyinstitute.action.user;

import gs.hinkleworld.core.Action;
import java.util.SortedSet;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CollectionLoader;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.Teacher;
import org.montereyinstitute.model.User;
import org.montereyinstitute.tagmodel.Navigation;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet(urlPatterns={"/class/*"})
public class ViewClassesAction extends Action {
    private static Logger log = LogManager.getLogger(ViewClassesAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        
        SortedSet<GSClass> sortedClasses = CollectionLoader.getSortedClasses(user);
        request.setAttribute("SortedClasses", sortedClasses);
        for( GSClass gsClass : sortedClasses ){
            if( gsClass.getTeacher() != null ){
                gsClass.getTeacher().getUser(); //loading for jsp
            }
        }
        
        if( user.getIsTeacher() ){
            request.setAttribute("userAsTeacher", (Teacher) user.getUserType());
        }
        
        Navigation.getNavigation(request, response).setButtonSelected("viewClasses", true);
        return "/WEB-INF/jsp/user/viewClasses.jsp";
    }
    
}
