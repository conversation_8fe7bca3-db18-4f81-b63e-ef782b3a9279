package org.montereyinstitute.action.user;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppModel;
import gs.hinkleworld.core.AppUtil;
import gs.hinkleworld.persistence.Hibernate;
import gs.hinkleworld.core.SessionUtil;
import gs.hinkleworld.core.Validator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.Course;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet("/register_class")
public class RegisterClassAction extends Action {
    private static Logger log = LogManager.getLogger(RegisterClassAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        Institution institution = (Institution) SessionUtil.getSessionSingleton(Institution.class, request);
        GSClass gsClass = (GSClass) SessionUtil.getSessionSingleton(GSClass.class, request);
        
        log.debug("gsClass: "+gsClass);
        log.debug("institution: "+institution);
        if( institution == null || gsClass == null ){
            log.warn("Access requires Institution and GSClass in session");
            return "/WEB-INF/jsp/student/loginHelp.jsp";            
        }
        
        if( gsClass.getCourseId().equals(Course.ID_DEV_MATH) && request.getParameterValues("validatorInput") == null ){
            log.info("Attempting to register DM1");
            request.setAttribute("showDM1Confirm", "true");
        }
        else{
            request.setAttribute("showDM1Confirm", "false");
        }
        
        request.setAttribute("teachers", CollectionLoader.getSortedTeachers(institution));
        Course appCourseNode = Course.getCourse(request, gsClass.getCourseId());
        Set<Course> inheritableCourses = new HashSet<Course>();
        inheritableCourses.add(appCourseNode);
        //DevMath1 and DevMath2 are inheritable from each other, so get both
        if( appCourseNode.getId().equals(Course.ID_DEV_MATH) ){
            Course otherCourseNode = AppModel.getAppModel(request).getCourses().get(Course.ID_DEV_MATH_2);
            if( otherCourseNode != null ) inheritableCourses.add(otherCourseNode);
        }
        else if( appCourseNode.getId().equals(Course.ID_DEV_MATH_2) ){
            Course otherCourseNode = AppModel.getAppModel(request).getCourses().get(Course.ID_DEV_MATH);
            if( otherCourseNode != null ) inheritableCourses.add(otherCourseNode);
        }
        
        request.setAttribute("sortedClassesForCourse", CollectionLoader.getSortedClassesForMultipleCourses(institution, inheritableCourses));

        Teacher teacher = null;
        Validator validatorForTeacher = Validator.getOneOfMultipleValidators("CreateTeacher", request);
        validatorForTeacher.processRequest(request, false, false);
        String emailConflict = validatorForTeacher.getEmailConflict();
        if( emailConflict != null ){
            request.setAttribute("emailConflict", emailConflict);
        }
        if( request.getParameter("teacherId") != null ){
            if( request.getParameter("teacherId").equals("0") ){
                if( validatorForTeacher.isAllValid() ){
                    User newUser = new User();
                    newUser.setUserTypeId(User.UserTypeCode.Teacher.getTypeNumber());
                    Hibernate.save(newUser);
                    teacher = new Teacher();
                    teacher.setUserId(newUser.getId());
                    teacher.setInstitution(institution);
                    validatorForTeacher.saveValidResults(request, newUser, teacher);
                    if( validatorForTeacher.getSaveError() ){
                        teacher = null;
                    }
                }
                else{
                    teacher = null;
                }
            }
            else{
                teacher = Teacher.getTeacher(request.getParameter("teacherId"));
                if( teacher == null && !validatorForTeacher.isTransactionStart() ) request.setAttribute("invalidTeacher", true);
            }
        }
        else{
            if( !validatorForTeacher.isTransactionStart() ) request.setAttribute("invalidTeacher", true);
        }
        
        Validator validatorForClass = Validator.getOneOfMultipleValidators("CreateClass", request);
        boolean nonValidatorChecks = true; //Allows validation checks outside the validator to be passed into validator so triggered behaviors (like saving or purging session) won't trigger.
        if( request.getParameter("GSClass_0_className") != null ){
            nonValidatorChecks = institution.isClassnameUniqueForInstitution(request.getParameter("GSClass_0_className"), gsClass);
            if( !nonValidatorChecks ){
                request.setAttribute("invalidClassName", true);
            }
        }
        log.debug("nonValidatorChecks: "+nonValidatorChecks);
        
        validatorForClass.processRequest(request, false, false, nonValidatorChecks);
        if( validatorForClass.isTransactionStart() ){
            validatorForClass.prepopulateParams(gsClass);
        }

        if( teacher == null ){
            log.debug("teacher is null. return unregistered");
            return "/WEB-INF/jsp/user/checkClassStatusUnregistered.jsp";
        }

        if( validatorForClass.isAllValid() ){
            gsClass.setTeacher(teacher);
            GSClass prototypeClass = null;
            if( request.getParameter("inheritClass") != null ){
                prototypeClass = GSClass.getGSClass(request.getParameter("inheritClass"));
                if( prototypeClass != null ){
                    gsClass.inheritSettings(prototypeClass, appCourseNode);
                }
            }
            Hibernate.saveOrMerge(gsClass);
            SessionUtil.purgeSessionSingleton(Institution.class, request);
            User user = User.getUserUseSession(request);
            //May or may not be logged in. If so, need to update it with this class
            if( user != null ){
                user.updateClassInCollections(gsClass);
                Hibernate.saveOrMerge(user);
            }
            if( teacher != null ){
                teacher.updateClassInCollections(gsClass);
                Hibernate.saveOrMerge(teacher);
            }
            validatorForTeacher.endSession(request);
            validatorForClass.endSession(request);
            if( prototypeClass != null ){
                try{
                    List<TopicStateForClass> topicStatesForClass = prototypeClass.loadTopicStatesForClass();
                    if( topicStatesForClass != null ){
                        for( TopicStateForClass prototypeTopicStateForClass : topicStatesForClass ){
                            TopicStateForClass inheritingTopicStateForClass = TopicStateForClass.getNewOrExisting(gsClass, prototypeTopicStateForClass.getUnit(), prototypeTopicStateForClass.getLesson(), prototypeTopicStateForClass.getTopic());
                            inheritingTopicStateForClass.setCloaked(prototypeTopicStateForClass.getCloaked());
                            Hibernate.save(inheritingTopicStateForClass);
                        }
                    }
                }
                catch(Throwable t){
                    log.error(t, t);
                }
            }
            sendCourseRegisteredEmail(request, gsClass);
            log.info("class registered: "+gsClass);
            return "/WEB-INF/jsp/user/checkClassStatusRegistered.jsp";
        }
        
        log.info("return unregistered");
        return "/WEB-INF/jsp/user/checkClassStatusUnregistered.jsp";
    }

    private void sendCourseRegisteredEmail(HttpServletRequest request, GSClass gsClass) {
        try{
            if( AppUtil.getAppModel().getCourseRegisteredEmailRecipient() != null ){
                Map<String,String> nonResourceText = new HashMap<String,String>();
                nonResourceText.put("institution", gsClass.getInstitution().getInstitutionName());
                nonResourceText.put("location", gsClass.getSiteUrl());
                nonResourceText.put("classType", gsClass.getCourseId());
                nonResourceText.put("className", gsClass.getClassName());
                nonResourceText.put("courseId", gsClass.getSiteClassId());
                nonResourceText.put("instructorName", gsClass.getTeacher().getFullName());
                nonResourceText.put("instructorEmail", gsClass.getTeacher().getEmail());
                Boolean success = AppUtil.getAppModel().getEmail().send(AppUtil.getAppModel().getCourseRegisteredEmailRecipient(), AppUtil.getAppModel().getCourseRegisteredEmailRecipient(), "courseRegistered", nonResourceText, null, request);
                if( success ){
                    request.setAttribute("emailSent", true);
                }
                else{
                    request.setAttribute("emailSent", false);
                }
            }
        }
        catch(Exception e){
            log.error(e, e);
            request.setAttribute("emailSent", false);
        }
    }
    
}
