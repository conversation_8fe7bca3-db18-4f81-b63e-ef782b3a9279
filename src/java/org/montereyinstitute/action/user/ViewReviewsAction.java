package org.montereyinstitute.action.user;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.SessionUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.Course;
import org.montereyinstitute.model.UnitStateForStudentClass.ActionPending;
import org.montereyinstitute.tagmodel.Navigation;
import org.montereyinstitute.util.DevEnglishUtil;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet(urlPatterns={"/reviews/*"})
public class ViewReviewsAction extends Action {
    private static Logger log = LogManager.getLogger(ViewReviewsAction.class);
    public static final String REDIRECT_FROM_PLAYER_PARAM = "navFromPlayer";
    public static final List<Integer> actionPendingCodesToCheck = new ArrayList<Integer>();
    static{
        actionPendingCodesToCheck.add(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode());
        actionPendingCodesToCheck.add(UnitStateForStudentClass.ActionPending.DevEnglishWritingCenterTeacherFinalGradeNeeded.getIntCode());
    }

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null || !user.getIsTeacher() ) return User.securityError(request, response);

        Course course = Course.getCourse(request, Course.ID_DEV_ENGLISH);
        Set<GSClass> sortedClasses = CollectionLoader.getSortedClasses(user);
        request.setAttribute("SortedClasses", sortedClasses);
        GSClass gsClass = GSClass.getGSClassUseSession(request);
        
        //Check session for unitStatesActionPending... set at login if available.
        //Clear session regardless, so subsequent requests will always refresh.
        List<UnitStateForStudentClass> unitStatesActionPending = (List<UnitStateForStudentClass>) request.getSession().getAttribute(UnitStateForStudentClass.ACTION_PENDING_SESSION_PARAM);
        if( unitStatesActionPending == null || unitStatesActionPending.isEmpty() ){
            unitStatesActionPending = UnitStateForStudentClass.getUnitStatesForActionPendingUseSession(request, response, sortedClasses, actionPendingCodesToCheck);
        }
        request.getSession().removeAttribute(UnitStateForStudentClass.ACTION_PENDING_SESSION_PARAM);
        
        Navigation.getNavigation(request, response).setButtonSelected("viewReviews", true);
        if( unitStatesActionPending != null && !unitStatesActionPending.isEmpty() ){
            request.setAttribute(UnitStateForStudentClass.ACTION_PENDING_SESSION_PARAM, unitStatesActionPending);
            Navigation.getNavigation(request, response).getSelectedButton().setAlertMode(true);
        }
        else{
            Navigation.getNavigation(request, response).getSelectedButton().setAlertMode(false);
        }

        return "/WEB-INF/jsp/user/viewReviews.jsp";
    }

}
