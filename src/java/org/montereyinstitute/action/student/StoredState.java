package org.montereyinstitute.action.student;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * This is a test class for JSON state storage, not used in CM production. 
 */
@Entity
public class StoredState implements Serializable {
    
    @Id @GeneratedValue(strategy=GenerationType.AUTO)
    private Long id;

    @Column(length=500)
    private String stateText;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStateText() {
        return stateText;
    }

    public void setStateText(String stateText) {
        this.stateText = stateText;
    }

}
