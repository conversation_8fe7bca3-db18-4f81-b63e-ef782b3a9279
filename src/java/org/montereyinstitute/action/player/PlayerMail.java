package org.montereyinstitute.action.player;

import gs.hinkleworld.core.AppModel;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.*;
import org.montereyinstitute.action.webservice.WebServiceAction;

/**
 *
 * <AUTHOR>
 */
@WebServlet("/playermail")
public class PlayerMail extends HttpServlet {
    private static final Logger log = LogManager.getLogger(WebServiceAction.class);
    
    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try{
            String subject = request.getParameter("subject");
            String filename = request.getParameter("filename");
            String email = request.getParameter("email");
            String message = request.getParameter("message");
            String body = request.getParameter("body");
            
            if(subject != null && filename != null && email != null && message != null && body != null){
                File bodyFile = File.createTempFile(filename.replace(".html", ""), ".html");
                OutputStream out = null;
                
                try{
                    out = new BufferedOutputStream(new FileOutputStream(bodyFile));
                    IOUtils.write(body, out, "UTF-8");
                }
                finally{
                    if(out != null){
                        out.close();
                    }
                }
                
                AppModel.getAppModel(request).getEmail().send(subject, email, message, bodyFile);
                bodyFile.delete();
            }
            else{
                throw new IllegalArgumentException("Missing parameters.  subject=" + subject + ", filename=" + filename + ", email=" + email + ", message=" + message + ", body=" + body);
            }
        }
        catch(Throwable t){
            log.error("Exception in PlayerMail", t);
            response.sendError(500);
        }
    }
}
