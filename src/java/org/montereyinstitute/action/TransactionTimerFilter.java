package org.montereyinstitute.action;

import java.io.IOException;
import java.util.Date;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.*;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
// This was used to handle V1 urls, but has been discontinued
//@WebFilter("/*")
public class TransactionTimerFilter  implements Filter {
    private static final Logger log = LogManager.getLogger(TransactionTimerFilter.class);
    private static final String[] pathsToWatch = {
        "/activity",
        "/admin/datamigration",
        "/ajax/dialog",
        "/change_class_status",
        "/change_institution_status",
        "/change_student_status",
        "/check_status",
        "/class",
        "/course_settings",
        "/create_institution",
        "/create_teacher",
        "/delete_class",
        "/delete_institution",
        "/delete_student",
        "/delete_teacher",
        "/edit_institution",
        "/edit_teacher",
        "/home",
        "/institution",
        "/logout",
        "/lostpassword",
        "/lti-de",
        "/my_settings",
        "/peer_review/change",
        "/playermail",
        "/register_class",
        "/report",
        "/report/class_summary",
        "/report/class_summary/institution_summary",
        "/report/full_class_export",
        "/report/peer_reviews",
        "/report/unit_progress",
        "/report/unit_progress/de",
        "/report/unit_progress/institution_summary",
        "/reviews",
        "/site_management",
        "/student",
        "/teacher",
        "/testltilaunchinsession",
        "/topics",
        "/usercreate",
        "/webservice"
    };
    
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest)servletRequest;
        String path = request.getServletPath();
        boolean logit = false;
        Exception exception = null;
        
        for(int i = 0; i < pathsToWatch.length; i++){
            if(path.startsWith(pathsToWatch[i])){
                logit = true;
            }
        }

        long start = new Date().getTime();

        try{
            chain.doFilter(request, servletResponse);
        }
        catch(IOException e){
            exception = e;
        } catch (ServletException e) {
            exception = e;
        }

        if(logit || exception != null){
            long totalTime = new Date().getTime() - start;
            
            log.debug("Transaction complete: " + path + ", time(ms) = " + totalTime);
            if(totalTime > 5000){
                log.info("Transaction complete: " + path + ", time(ms) = " + totalTime+", (courseId="+request.getParameter("courseId")+", pathInfo="+request.getPathInfo()+")");
            }
            if(exception != null){
                log.info("Exception caught in chain during: " + path + ", total time=" + totalTime + ", parameters=" + request.getParameterMap(), exception);
            }
        }
    }

    @Override
    public void destroy() {
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

}
