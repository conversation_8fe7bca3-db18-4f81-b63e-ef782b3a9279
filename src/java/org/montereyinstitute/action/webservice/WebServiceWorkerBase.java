
package org.montereyinstitute.action.webservice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.Institution;
import org.montereyinstitute.model.Student;
import org.montereyinstitute.model.UnitStateForClass;
import org.montereyinstitute.model.UnitStateForStudentClass;

/**
 * <AUTHOR>
 */
public abstract class WebServiceWorkerBase {
    private static final Logger log = LogManager.getLogger(WebServiceWorkerBase.class);

    public static final int STATUS_SUCCESS = 0;
    public static final int STATUS_ERROR_SERVER_EXCEPTION = 1;
    public static final int STATUS_ERROR_INVALID_QUERY_PARAMETERS = 2;
    public static final int STATUS_ERROR_EMAIL_EXCEPTION = 3;
    public static final int STATUS_ERROR_INVALID_STUDENT = 4;
    public static final int STATUS_ERROR_INVALID_NONCE = 5;
    public static final int STATUS_ERROR_INVALID_WRITING_CENTER = 6;
 
    protected int status = 0;
    protected int errorCode = 0; //settable by any validator for messaging
    protected String plainTextResponse; //setting this will remove all xml
    protected String jsonResponse; //setting this will remove all xml
    protected String jspXml = WebServiceAction.jspXmlForStartup;
    
    protected HttpServletRequest request;
    protected HttpServletResponse response;
    protected Institution institution;
    protected GSClass gsClass;
    protected Student student;
    protected CourseNode.Course appCourseNode;
    protected CourseNode.Unit appUnitNode;
    protected CourseNode.Lesson appLessonNode;
    protected CourseNode.Topic appTopicNode;
    protected CourseNode.Question appQuestionNode;
    protected UnitStateForStudentClass unitState;
    protected UnitStateForClass unitStateForClass;
    
    protected abstract void init(HttpServletRequest request, HttpServletResponse response) throws Throwable;    
    
    protected void debugProperties() {
        if( !log.isDebugEnabled() ) return;
        log.debug("institution: "+institution);
        log.debug("gsClass: "+gsClass);
        log.debug("student: "+student);
        log.debug("unitState: "+unitState);
        log.debug("appCourseNode: "+appCourseNode);
        log.debug("appUnitNode: "+appUnitNode);
        log.debug("appLessonNode: "+appLessonNode);
        log.debug("appTopicNode: "+appTopicNode);
        log.debug("appQuestionNode: "+appQuestionNode);
    }

    protected void infoProperties() {
        log.info("institution: "+institution);
        log.info("gsClass: "+gsClass);
        log.info("student: "+student);
        log.info("unitState: "+unitState);
        log.info("appCourseNode: "+appCourseNode);
        log.info("appUnitNode: "+appUnitNode);
        log.info("appLessonNode: "+appLessonNode);
        log.info("appTopicNode: "+appTopicNode);
        log.info("appQuestionNode: "+appQuestionNode);
    }

    protected void quickTest(){
        log.debug("quickTest currently does nothing");
    } 
    
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public GSClass getGsClass() {
        return gsClass;
    }

    public Student getStudent() {
        return student;
    }

    public String getPlainTextResponse() {
        return plainTextResponse;
    }

    public Institution getInstitution() {
        return institution;
    }

    public CourseNode.Course getAppCourseNode() {
        return appCourseNode;
    }

    public CourseNode.Unit getAppUnitNode() {
        return appUnitNode;
    }

    public CourseNode.Lesson getAppLessonNode() {
        return appLessonNode;
    }

    public CourseNode.Topic getAppTopicNode() {
        return appTopicNode;
    }

    public CourseNode.Question getAppQuestionNode() {
        return appQuestionNode;
    }

    public UnitStateForClass getUnitStateForClass() {
        return unitStateForClass;
    }

    public UnitStateForStudentClass getUnitState() {
        return unitState;
    }

    public void setAppCourseNode(CourseNode.Course appCourseNode) {
        this.appCourseNode = appCourseNode;
    }

    public String getJsonResponse() {
        return jsonResponse;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

}
