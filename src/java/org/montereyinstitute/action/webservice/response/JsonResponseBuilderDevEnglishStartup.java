package org.montereyinstitute.action.webservice.response;

import gs.hinkleworld.persistence.Hibernate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.*;
import org.apache.sling.commons.json.*;
import org.montereyinstitute.action.webservice.WebServiceWorkerBase;
import org.montereyinstitute.action.webservice.WebServiceWorkerDevEnglish;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.Unit;
import static org.montereyinstitute.model.NestingActivityStateType.*;
import org.montereyinstitute.model.writingcenter.ScaffoldState;
import org.montereyinstitute.model.writingcenter.TaglessDocumentProcessorIn;
import org.montereyinstitute.model.writingcenter.WritingCenterState;
import org.montereyinstitute.model.writingcenter.WritingReviewState;
import org.montereyinstitute.util.DevEnglishReportUtil;

/**
 * <AUTHOR>
 */
public class JsonResponseBuilderDevEnglishStartup extends JsonResponseBuilderBase {
    private static final Logger log = LogManager.getLogger(JsonResponseBuilderDevEnglishStartup.class);
    
    protected final WebServiceWorkerDevEnglish wsw;
    private Boolean viewAll = false;
    private Boolean includeReview1TeacherState = false;
    private Boolean includeReviewNTeacherState = false;
    private Boolean includeReviewFinalTeacherState = false;

    public JsonResponseBuilderDevEnglishStartup(WebServiceWorkerDevEnglish wsw) {
        super(wsw);
        this.wsw = wsw;
    }
    
    @Override
    public void init() throws Throwable {
        log.debug("init");
        addStatus(wsw.getStatus());
        if( wsw.getStatus() != WebServiceWorkerBase.STATUS_SUCCESS ) return;
        if( wsw.getRequest().getParameter("all") != null && wsw.getRequest().getParameter("all").equals("true") ) viewAll = true;
        rootJSON.put("viewAll", viewAll);
        log.debug("includeReview1TeacherState (request attribute): "+wsw.getRequest().getAttribute("includeReview1TeacherState"));
        if( wsw.getRequest().getAttribute("includeReview1TeacherState") != null ){
            includeReview1TeacherState = (Boolean) wsw.getRequest().getAttribute("includeReview1TeacherState");
        }
        if( wsw.getRequest().getAttribute("includeReviewNTeacherState") != null ){
            includeReviewNTeacherState = (Boolean) wsw.getRequest().getAttribute("includeReviewNTeacherState");
        }
        if( wsw.getRequest().getAttribute("includeReviewFinalTeacherState") != null ){
            includeReviewFinalTeacherState = (Boolean) wsw.getRequest().getAttribute("includeReviewFinalTeacherState");
        }
        log.debug("includeReview1TeacherState :"+includeReview1TeacherState);
        log.debug("includeReviewNTeacherState :"+includeReviewNTeacherState);
        log.debug("includeReviewFinalTeacherState :"+includeReviewFinalTeacherState);
        addStudent(rootJSON);
        addGSClass(rootJSON);
        JSONObject unitStateForClassJSON = addUnitStateForClass(rootJSON);
        if( unitStateForClassJSON != null ){
            addUnitStateForClassInnerArrays(unitStateForClassJSON);
        }
        JSONObject unitStateJSON = addUnitStateForStudentClass(rootJSON);
        if( unitStateJSON != null ){
            addDevEnglishProgress(unitStateJSON);
            addUnitActivityStatesArray(unitStateJSON);
            addAnnotationStatesArray(unitStateJSON);
            addWritingCenterState(unitStateJSON);
        }
        addMessage(rootJSON);
    }
    
    private void addUnitStateForClassInnerArrays(JSONObject addToJSON) throws JSONException{
        log.debug("addUnitStateForClassInnerArrays");
        UnitStateForStudentClass unitState = wsw.getUnitState();
        if( unitState == null ){
            log.warn("unitState is null");
            return;
        }
        UnitStateForClass unitStateForClass = wsw.getUnitState().getUnitStateForClass();
        if( unitStateForClass == null ){
            log.warn("unitStateForClass is null");
            return;
        }
        if( unitStateForClass.getWritingCenterSelfReviewSteps() != null ){
            JSONArray reviewStepsAB = new JSONArray();
            for( Integer key : unitStateForClass.getWritingCenterSelfReviewSteps().keySet() ){
                addReviewStepToArray(reviewStepsAB, key, unitStateForClass.getWritingCenterSelfReviewSteps().get(key));
            }
            addToJSON.put("writingCenterSelfReviewSteps", reviewStepsAB);
        }
        if( unitStateForClass.getWritingCenterTeacherReviewSteps() != null ){
            JSONArray reviewStepsAB = new JSONArray();
            for( Integer key : unitStateForClass.getWritingCenterTeacherReviewSteps().keySet() ){
                addReviewStepToArray(reviewStepsAB, key, unitStateForClass.getWritingCenterTeacherReviewSteps().get(key));
            }
            addToJSON.put("writingCenterTeacherReviewSteps", reviewStepsAB);
        }
        if( unitStateForClass.getWritingCenterPeerReviewSteps() != null ){
            JSONArray reviewStepsAB = new JSONArray();
            for( Integer key : unitStateForClass.getWritingCenterPeerReviewSteps().keySet() ){
                addReviewStepToArray(reviewStepsAB, key, unitStateForClass.getWritingCenterPeerReviewSteps().get(key));
            }
            addToJSON.put("writingCenterPeerReviewSteps", reviewStepsAB);
        }
        if( unitStateForClass.getWritingCenterScaffoldTips() != null ){
            JSONArray scaffoldTipsAB = new JSONArray();
            for( Integer key : unitStateForClass.getWritingCenterScaffoldTips().keySet() ){
                addScaffoldTipToArray(scaffoldTipsAB, key, unitStateForClass.getWritingCenterScaffoldTips().get(key));
            }
            addToJSON.put("writingCenterScaffoldTips", scaffoldTipsAB);
        }
    }
    
    private JSONObject addReviewStepToArray(JSONArray addToJSON, Integer draft, Integer review) throws JSONException{
        if( draft == null || review == null ) return null;
        JSONObject stepJSON = new JSONObject();
        stepJSON.put("draft", draft);
        stepJSON.put("review", review);
        addToJSON.put(stepJSON);
        return stepJSON;
    }

    private JSONObject addScaffoldTipToArray(JSONArray addToJSON, Integer scaffoldId, String tip) throws JSONException{
        if( scaffoldId == null || tip == null ) return null;
        JSONObject scaffoldTipJSON = new JSONObject();
        scaffoldTipJSON.put("scaffoldId", scaffoldId);
        scaffoldTipJSON.put("tip", tip);
        addToJSON.put(scaffoldTipJSON);
        return scaffoldTipJSON;
    }
    
    private JSONObject addDevEnglishProgress(JSONObject addToJSON) throws Throwable{
        log.debug("addDevEnglishProgress");
        UnitStateForStudentClass unitState = wsw.getUnitState();
        if( unitState == null ){
            log.warn("unitState is null");
            return null;
        }
        Unit unit = unitState.getAppUnitNode();
        if( unit == null ){
            log.warn("unit is null");
            return null;
        }
        JSONObject progressJSON = new JSONObject();
        {
            Integer status = DevEnglishReportUtil.getActivityStatus(unitState, DEV_ENGLISH_INTRO.name(), unit);
            progressJSON.put("intro", status);
        }
        {
            Integer status = DevEnglishReportUtil.getActivityStatus(unitState, DEV_ENGLISH_PRE_READING.name(), unit);
            progressJSON.put("preReading", status);
        }
        {
            Integer status = DevEnglishReportUtil.getActivityStatus(unitState, DEV_ENGLISH_ACTIVE_READING.name(), unit);
            progressJSON.put("activeReading", status);
        }
        {
            Integer status = DevEnglishReportUtil.getActivityStatus(unitState, DEV_ENGLISH_POST_READING.name(), unit);
            progressJSON.put("postReading", status);
        }
        {
            Integer status = DevEnglishReportUtil.getActivityStatus(unitState, DEV_ENGLISH_PRE_WRITING.name(), unit);
            progressJSON.put("preWriting", status);
        }
        {
            Integer status = DevEnglishReportUtil.getActivityStatus(unitState, DEV_ENGLISH_WRITING_CENTER.name(), unit);
            progressJSON.put("writingCenter", status);
        }
        {
            Integer status = DevEnglishReportUtil.getReviewStatusWithMasteryCheck(unitState);
            progressJSON.put("review", status);
        }
        addToJSON.put("devEnglishProgress", progressJSON);
        return progressJSON;
    }
    
    private JSONArray addUnitActivityStatesArray(JSONObject addToJSON) throws JSONException{
        log.debug("addUnitActivityStatesArray");
        UnitStateForStudentClass unitState = wsw.getUnitState();
        if( unitState == null ){
            log.warn("unitState is null");
            return null;
        }
        List<NestingActivityState> activityStates = unitState.getUnitActivityStatesFlatList();
        JSONArray activityStatesJSON = new JSONArray();
        if( activityStates != null ){
            for( NestingActivityState nas : unitState.getUnitActivityStatesFlatList() ){
                addActivityStateToArray(activityStatesJSON, nas);
            }
        }
        addToJSON.put("unitActivityStates", activityStatesJSON);
        return activityStatesJSON;
    }

    private JSONObject addActivityStateToArray(JSONArray addToJSON, NestingActivityState nas) throws JSONException{
        if( nas == null ){
            log.warn("activity state is null");
            return null;
        }
        JSONObject activityStateJSON = new JSONObject();
        String parentId = "";
        if( nas.getParent() != null && nas.getParent().getId() != null ) parentId = nas.getParent().getId().toString();
        addPropertiesToUnitActivityState(activityStateJSON, nas.getId().toString(), nas.getConfigId(), parentId, nas.getActivityType(), nas.getSiblingNumberForType());
        
        JSONArray questionStatesJSON = new JSONArray();
        if( nas.getInitialQuestionState() != null ){
            log.debug("initial question state");
            QuestionState qs = nas.getInitialQuestionState();
            JSONObject questionStateJSON = new JSONObject();
            addPropertiesToQuestionState(questionStateJSON, qs.getIndexOfSet(), qs.getQuestionId(), qs.getAttempts(), qs.getResponse(), qs.getAssessmentType(), null, null);
            questionStatesJSON.put(questionStateJSON);
        }
        if( nas.getModifiedQuestionStates() != null ){
            for( QuestionState qs : nas.getModifiedQuestionStates() ){
                log.debug("modified question state");
                JSONObject questionStateJSON = new JSONObject();
                addPropertiesToQuestionState(questionStateJSON, qs.getIndexOfSet(), qs.getQuestionId(), qs.getAttempts(), qs.getResponse(), qs.getAssessmentType(), null, null);
                questionStatesJSON.put(questionStateJSON);
            }
        }
        activityStateJSON.put("questionStates", questionStatesJSON);
        addToJSON.put(activityStateJSON);
        return activityStateJSON;
    }
    
    private JSONArray addAnnotationStatesArray(JSONObject addToJSON) throws JSONException{
        log.debug("addAnnotationStatesArray");
        List<AnnotationState> annotationStates = wsw.getAnnotationStates();
        log.debug("annotationStates: "+annotationStates);
        if( annotationStates == null ){
            log.warn("annotationStates is null");
            return null;
        }
        JSONArray annotationStatesJSON = new JSONArray();
        for( AnnotationState as : annotationStates ){
            addAnnotationStateToArray(annotationStatesJSON, as);
        }
        addToJSON.put("annotationStates", annotationStatesJSON);
        return annotationStatesJSON;
    }
    
    private JSONObject addAnnotationStateToArray(JSONArray addToJSON, AnnotationState as) throws JSONException{
        if( as == null ){
            log.warn("annotation state is null");
            return null;
        }
        JSONObject annotationStateJSON = new JSONObject();
        annotationStateJSON.put("dbId", as.getId());
        annotationStateJSON.put("startElementId", as.getStartElementId());
        annotationStateJSON.put("endElementId", as.getEndElementId());
        annotationStateJSON.put("selectedText", as.getSelectedText());
        annotationStateJSON.put("commentText", as.getCommentText());
        annotationStateJSON.put("annotationQuestionId", as.getAnnotationQuestionId());
        addToJSON.put(annotationStateJSON);
        return annotationStateJSON;
    }
    
    private JSONObject addWritingCenterState(JSONObject addToJSON) throws JSONException, Throwable{
        log.debug("addWritingCenterState");
        UnitStateForStudentClass unitState = wsw.getUnitState();
        if( unitState == null ){
            log.warn("unitState is null");
            return null;
        }
        WritingCenterState wcState = unitState.getWritingCenterState();
        if( wcState == null ){
            log.warn("writing center state is null");
            return null;
        }
        JSONObject wcJSON = new JSONObject();
        wcJSON.put("dbId", wcState.getId());
        wcJSON.put("currentStep", wcState.getCurrentStep());
        
        if( !wcState.getUseTaglessDoc() ){
            log.debug("converting to tagless doc");
            TaglessDocumentProcessorIn tagDocProcessor = new TaglessDocumentProcessorIn();
            tagDocProcessor.initDocument(wcState.getDocumentContent(), wcState.getId());
            tagDocProcessor.process();
            tagDocProcessor.examine();
            wcState.setDocumentContent(tagDocProcessor.getFinalDocument()); // DON'T BE CONFUSED: getFinalDocument gets final doc for tag processor (i.e. after all processing)... that's different than final doc for writing center

            TaglessDocumentProcessorIn tagDocProcessor2 = new TaglessDocumentProcessorIn();
            tagDocProcessor2.initDocument(wcState.getFinalDocument());
            tagDocProcessor2.process();
            tagDocProcessor2.examine();
            wcState.setFinalDocument(tagDocProcessor2.getFinalDocument());

            wcState.setUseTaglessDoc(true);
            Hibernate.save(wcState);

            for( WritingReviewState reviewState : wcState.getReviewNState() ){
                tagDocProcessor.assignIndexesToReview(reviewState, reviewState.getConfigId().intValue());
                Hibernate.save(reviewState);
            }
            for( WritingReviewState reviewState : wcState.getReviewNTeacherState() ){
                tagDocProcessor.assignIndexesToReview(reviewState, reviewState.getConfigId().intValue());
                Hibernate.save(reviewState);
            }
            for( WritingReviewState reviewState : wcState.getReviewNPeerState() ){
                tagDocProcessor.assignIndexesToReview(reviewState, reviewState.getConfigId().intValue());
                Hibernate.save(reviewState);
            }
        }
        
        //These inits set reviewN state configIds, so these must come before adding reviewN states
        if( wcState.getDocumentContent() != null ){
            wcState.initResponseDocumentWithTags();
            wcJSON.put("documentContent", wcState.getResponseDocumentWithTags());
        }
        if( wcState.getSecondDraft() != null ){
            wcState.initResponseSecondDraftWithTags();
            wcJSON.put("secondDraft", wcState.getResponseSecondDraftWithTags());
        }
        if( wcState.getFinalDocument()!= null ){
            wcState.initResponseFinalDocumentWithTags();
            wcJSON.put("finalDocument", wcState.getResponseFinalDocumentWithTags());
        }
        if( viewAll || includeReviewFinalTeacherState ){
            wcState.initResponseGradedFinalDocumentWithTags();
            wcJSON.put("gradedFinalDocument", wcState.getResponseGradedFinalDocumentWithTags());
            wcJSON.put("finalGrade", wcState.getFinalGrade());
        }
        
        wcJSON.put("review1Pending", (unitState.getHasDevEnglishTeacherReview1Pending() || unitState.getHasDevEnglishPeerReview1Pending()) );
        wcJSON.put("reviewNPending", (unitState.getHasDevEnglishTeacherReviewNPending() || unitState.getHasDevEnglishPeerReviewNPending()) );
        addScaffoldStatesArray(wcJSON, "scaffold1States", wcState.getScaffold1State());
        addScaffoldStatesArray(wcJSON, "scaffold2States", wcState.getScaffold2State());
        addReviewStatesArray(wcJSON, "review1States", wcState.getReview1State());
        addReviewStatesArray(wcJSON, "reviewNStates", wcState.getReviewNState());
        addReviewStatesArray(wcJSON, "review1PeerStates", wcState.getReview1PeerStateSorted());
        addReviewStatesArray(wcJSON, "reviewNPeerStates", wcState.getReviewNPeerState());
        if( viewAll || includeReview1TeacherState ) addReviewStatesArray(wcJSON, "review1TeacherStates", wcState.getReview1TeacherState());
        if( viewAll || includeReviewNTeacherState ) addReviewStatesArray(wcJSON, "reviewNTeacherStates", wcState.getReviewNTeacherState());
        if( viewAll || includeReviewFinalTeacherState ){
            addReviewStatesArray(wcJSON, "reviewFinalTeacherStates", wcState.getReviewFinalTeacherState());
        }
        addToJSON.put("writingCenterState", wcJSON);
        return wcJSON;
    }
    
    protected JSONArray addScaffoldStatesArray(JSONObject addToJSON, String propertyName, Set<ScaffoldState> scaffoldStates) throws JSONException{
        if( scaffoldStates == null ){
            log.warn("scaffold states set is null");
            return null;
        }
        JSONArray scaffoldsJSON = new JSONArray();
        for( ScaffoldState ss : scaffoldStates ){
            addScaffoldStateToArray(scaffoldsJSON, ss);
        }
        addToJSON.put(propertyName, scaffoldsJSON);
        return scaffoldsJSON;
    }
    
    private JSONObject addScaffoldStateToArray(JSONArray addToJSON, ScaffoldState ss) throws JSONException{
        log.debug("addScaffoldStateToArray");
        if( ss == null ){
            log.warn("scaffold state is null");
            return null;
        }
        JSONObject scaffoldJSON = new JSONObject();
        scaffoldJSON.put("dbId", ss.getId());
        scaffoldJSON.put("configId", ss.getConfigId());
        scaffoldJSON.put("text", ss.getText());
        addToJSON.put(scaffoldJSON);
        return scaffoldJSON;
    }
    
    // Make sure this is called after TaglessDocumentProcessorOut.process() so that transient comment code ids are set correctly
    private JSONArray addReviewStatesArray(JSONObject addToJSON, String propertyName, Set<WritingReviewState> reviewStates) throws JSONException{
        log.debug("addReviewStatesArray: "+propertyName);
        if( reviewStates == null ){
            log.warn("review states set is null");
            return null;
        }
        JSONArray reviewsAB = new JSONArray();
        for( WritingReviewState rs : reviewStates ){
            addReviewStateToArray(reviewsAB, rs);
        }
        addToJSON.put(propertyName, reviewsAB);
        return reviewsAB;
    }
    
    private JSONObject addReviewStateToArray(JSONArray addToJSON, WritingReviewState rs) throws JSONException{
        log.debug("addReviewStateToArray");
        if( rs == null ){
            log.warn("review state is null");
            return null;
        }
        JSONObject reviewJSON = new JSONObject();
        reviewJSON.put("dbId", rs.getId());
        if( rs.getPlayerTagCode() != null ){ //for reviewN
            log.debug("configId: "+rs.getPlayerTagCode()+", dbId: "+rs.getId()+", "+rs.getText());
            reviewJSON.put("configId", rs.getPlayerTagCode());
        }
        else{ //for review1
            log.debug("configId: "+rs.getConfigId()+", dbId: "+rs.getId()+", "+rs.getText());
            reviewJSON.put("configId", rs.getConfigId());
        }
        reviewJSON.put("text", rs.getText());
        reviewJSON.put("yes", rs.getYes());
        reviewJSON.put("type", rs.getType());
        reviewJSON.put("liked", rs.getLiked());
        reviewJSON.put("collapsed", rs.getCollapsed());
        reviewJSON.put("reviewerId", rs.getReviewerId());
        addToJSON.put(reviewJSON);
        return reviewJSON;
    }
    
    protected JSONObject addMessage(JSONObject addToJSON) throws JSONException, Throwable{
        log.debug("addMessage");
        UnitStateForStudentClass unitState = wsw.getUnitState();
        if( unitState == null ){
            log.warn("unitState is null");
            return null;
        }
        JSONObject messageJSON = new JSONObject();
        if( wsw.getRequest().getAttribute("writingCenterTeacherReviewComplete") != null )
            messageJSON.put("writingCenterTeacherReviewComplete", (Integer)wsw.getRequest().getAttribute("writingCenterTeacherReviewComplete"));
        if( wsw.getRequest().getAttribute("writingCenterTeacherReviewComplete") != null )
            messageJSON.put("writingCenterPeerReviewComplete", (Integer)wsw.getRequest().getAttribute("writingCenterPeerReviewComplete"));
        WritingCenterState wcState = unitState.getWritingCenterState();
        if( wcState != null ){
            if( unitState.getIsDevEnglishWritingCenterFinalGradeComplete()){
                messageJSON.put("writingCenterFinalGrade", wcState.getFinalGrade());
                // current system w/out finalComment
                if( wcState.getFinalComment() == null ){
                    wcState.initResponseGradedFinalDocumentWithTags();
                    messageJSON.put("writingCenterGradedFinalDocument", wcState.getResponseGradedFinalDocumentWithTags());
                    addReviewStatesArray(messageJSON, "writingCenterReviewFinalTeacherStates", wcState.getReviewFinalTeacherState());
                }
                else{ // legacy support for finalComment, which was later replaced by reviewFinalTeacherState                
                    Set<WritingReviewState> legacyReviews = new HashSet<WritingReviewState>();
                    WritingReviewState legacyReview = new WritingReviewState();
                    legacyReview.setId(0L);
                    legacyReview.setConfigId(-1);
                    legacyReview.setText( wcState.getFinalComment());
                    legacyReview.setCollapsed(false);
                    legacyReviews.add(legacyReview);
                    addReviewStatesArray(messageJSON, "writingCenterReviewFinalTeacherStates", legacyReviews);
                    if( wcState.getFinalDocument() != null ) messageJSON.put("writingCenterGradedFinalDocument", wcState.getFinalDocument());
                }
            }
        }
        if( wsw.getRequest().getAttribute("writingCenterPeerReviewAssignedToThisStudent") != null )
            messageJSON.put("writingCenterPeerReviewAssignedToThisStudent", true);
        addToJSON.put("message", messageJSON);
        return messageJSON;
    }

}
