package org.montereyinstitute.action.webservice.response;

import org.montereyinstitute.action.webservice.WebServiceWorkerBase;

/**
 * <AUTHOR>
 */
public class JsonResponseBuilderStatusOnly extends JsonResponseBuilderBase {

    private final WebServiceWorkerBase wsw; 

    public JsonResponseBuilderStatusOnly(WebServiceWorkerBase wsw) {
        super(wsw);
        this.wsw = wsw;
    }

    @Override
    public void init() throws Throwable {
        addStatus(wsw.getStatus());
    }
    
}
