package org.montereyinstitute.action.webservice.response;

import java.util.*;
import org.apache.logging.log4j.*;
import org.apache.sling.commons.json.*;
import org.montereyinstitute.action.webservice.WebServiceWorkerBase;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.Unit;

/**
 * <AUTHOR>
 */
public abstract class JsonResponseBuilderBase {
    private static final Logger log = LogManager.getLogger(JsonResponseBuilderBase.class);
    
    private WebServiceWorkerBase wsw;
    protected JSONObject rootJSON = new JSONObject();
    protected ResourceBundle appText;

    public JsonResponseBuilderBase(WebServiceWorkerBase wsw) {
        this.wsw = wsw;
        appText = ResourceBundle.getBundle("org.montereyinstitute.resource.Text", wsw.getRequest().getLocale());
    }

    public abstract void init() throws Throwable;
    
    public String getJsonString(){
        return rootJSON.toString();
    }
    
    protected void addStatus(Integer statusInt) throws JSONException{
        log.debug("addStatus");
        if( statusInt.equals(0) ){
            String status = appText.getString("webservice-success");
            if( status != null ) rootJSON.put("status", status);
        }
        else{
            String status = appText.getString("webservice-failure");
            String desc = appText.getString("webservice-status-"+statusInt);
            if( status != null ) rootJSON.put("status", status);
            if( desc != null ) rootJSON.put("desc", desc);
        }
    }
    
    protected JSONObject addStudent(JSONObject addToJSON) throws JSONException{
        log.debug("addStudent");
        Student student = wsw.getStudent();
        if( student == null ) return null;
        JSONObject studentJSON = new JSONObject();
        studentJSON.put("id", student.getStudentId());
        addToJSON.put("student", studentJSON);
        return studentJSON;
    }
    protected JSONObject addGSClass(JSONObject addToJSON) throws JSONException{
        log.debug("addGSClass");
        GSClass gsClass = wsw.getGsClass();
        if( gsClass == null ){
            log.warn("gsClass is null");
            return null;
        }
        JSONObject gsClassJSON = new JSONObject();
        gsClassJSON.put("classId", gsClass.getClassId());
        gsClassJSON.put("active", gsClass.getActive());
        gsClassJSON.put("threshold", gsClass.getThreshold());
        gsClassJSON.put("quickPreassessment", gsClass.getQuickPreassessment());
        gsClassJSON.put("disablePreassessment", gsClass.getDisablePreassessment());
        gsClassJSON.put("retakePreassessment", gsClass.getRetakePreassessment());
        gsClassJSON.put("retakeTopicReview", gsClass.getRetakeTopicReview());
        gsClassJSON.put("allowEmailHelp", gsClass.getAllowEmailHelp());
        addToJSON.put("gsClass", gsClassJSON);
        return gsClassJSON;
    }
    
    protected JSONObject addUnitStateForClass(JSONObject addToJSON) throws JSONException{
        log.debug("addUnitStateForClass");
        UnitStateForClass unitStateForClass = wsw.getUnitState().getUnitStateForClass();
        if( unitStateForClass == null ){
            log.warn("unitState is null");
            return null;
        }
        GSClass gsClass = unitStateForClass.getGsClass();
        if( gsClass == null ){
            log.warn("gsClass is null");
            return null;
        }
        JSONObject unitStateForClassJSON = new JSONObject();
        unitStateForClassJSON.put("dbId", unitStateForClass.getId().toString());
        unitStateForClassJSON.put("classId", gsClass.getClassId().toString());
        unitStateForClassJSON.put("unitNumber", unitStateForClass.getUnit());
        unitStateForClassJSON.put("readingAssignmentAudioLensOn", unitStateForClass.getReadingAssignmentAudioLensOn());
        unitStateForClassJSON.put("readingAssignmentVocabularyLensOn", unitStateForClass.getReadingAssignmentVocabularyLensOn());
        unitStateForClassJSON.put("readingAssignmentGrammarLensOn", unitStateForClass.getReadingAssignmentGrammarLensOn());
        unitStateForClassJSON.put("reviewMasteryLevel", unitStateForClass.getReviewMasteryLevel());
        unitStateForClassJSON.put("reviewAllowRetake", unitStateForClass.getReviewAllowRetake());
        unitStateForClassJSON.put("showScaffoldTips", unitStateForClass.getShowScaffoldTips());
        unitStateForClassJSON.put("readingAssignmentTextModified", unitStateForClass.getReadingAssignmentTextModified());
        unitStateForClassJSON.put("writingCenterPreInstructionsMessage", unitStateForClass.getWritingCenterPreInstructionsMessage());
        addToJSON.put("unitStateForClass", unitStateForClassJSON);
        return unitStateForClassJSON;
    }
    
    protected JSONObject addUnitStateForStudentClass(JSONObject addToJSON) throws JSONException{
        log.debug("addUnitStateForStudentClass");
        UnitStateForStudentClass unitState = wsw.getUnitState();
        if( unitState == null ){
            log.warn("unitState is null");
            return null;
        }
        Student student = unitState.getStudent();
        if( student == null ){
            log.warn("student is null");
            return null;
        }
        GSClass gsClass = unitState.getGsClass();
        if( gsClass == null ){
            log.warn("gsClass is null");
            return null;
        }
        Unit unit = unitState.getAppUnitNode();
        if( unit == null ){
            log.warn("unit is null");
            return null;
        }
        JSONObject unitStateJSON = new JSONObject();
        unitStateJSON.put("dbId", unitState.getId());
        unitStateJSON.put("classId", gsClass.getClassId());
        unitStateJSON.put("studentId", student.getStudentId());
        unitStateJSON.put("unitId", unit.getId());
        unitStateJSON.put("unitNumber", unit.getNumber());
        unitStateJSON.put("preattempts", unitState.getPreattempts());
        unitStateJSON.put("preassessmentComplete", unitState.getPreassessmentComplete());
        unitStateJSON.put("preassessmentInProgress", unitState.getPreassessmentInProgress());
        unitStateJSON.put("preassessmentScore", unitState.getPreassessmentScore());
        unitStateJSON.put("attempts", unitState.getAttempts());
        unitStateJSON.put("formCode", unitState.getFormCode());
        unitStateJSON.put("randomizer", unitState.getRandomCommaSep());
        addToJSON.put("unitStateForStudentClass", unitStateJSON);
        return unitStateJSON;
    }
 
    protected void addPropertiesToUnitActivityState(JSONObject activityStateJSON, String dbId, String configId, String parentId, String activityType, Integer siblingNumberForType) throws JSONException{
        activityStateJSON.put("dbId", dbId);
        activityStateJSON.put("configId", configId);
        activityStateJSON.put("parentId", parentId);
        activityStateJSON.put("activityType", activityType);
        activityStateJSON.put("siblingNumberForType", siblingNumberForType);
    } 
    
    protected void addPropertiesToQuestionState(JSONObject questionStateJSON, Integer indexOfSet, String questionId, Integer attempts, String response, String responseType, Boolean responseCorrect, Boolean responseSkipped) throws JSONException{
        questionStateJSON.put("indexOfSet", indexOfSet);
        questionStateJSON.put("questionId", questionId);
        questionStateJSON.put("attempts", attempts);
        questionStateJSON.put("responseType", responseType);
        questionStateJSON.put("response", response);
        questionStateJSON.put("responseCorrect", responseCorrect);
        questionStateJSON.put("responseSkipped", responseSkipped);
    }

}
