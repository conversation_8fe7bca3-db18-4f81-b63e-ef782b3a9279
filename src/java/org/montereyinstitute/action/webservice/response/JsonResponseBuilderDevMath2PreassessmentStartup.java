package org.montereyinstitute.action.webservice.response;

import org.apache.logging.log4j.*;
import org.apache.sling.commons.json.JSONObject;
import org.montereyinstitute.action.webservice.WebServiceWorkerBase;
import org.montereyinstitute.action.webservice.WebServiceWorkerDevMath2Preassessment;

/**
 * <AUTHOR>
 */
public class JsonResponseBuilderDevMath2PreassessmentStartup extends JsonResponseBuilderDevMath2Base {
    private static final Logger log = LogManager.getLogger(JsonResponseBuilderDevMath2PreassessmentStartup.class);
    
    protected final WebServiceWorkerDevMath2Preassessment wsw;

    public JsonResponseBuilderDevMath2PreassessmentStartup(WebServiceWorkerDevMath2Preassessment wsw) {
        super(wsw);
        this.wsw = wsw;
    }
    
    @Override
    public void init() throws Throwable {
        log.debug("init");
        addStatus(wsw.getStatus());
        if( wsw.getStatus() != WebServiceWorkerBase.STATUS_SUCCESS ) return;
        addStudent(rootJSON);
        addGSClass(rootJSON);
        addUnitStateForClass(rootJSON);
        JSONObject unitStateJSON = addUnitStateForStudentClass(rootJSON);
        if( unitStateJSON != null ){
            addUnitActivityStatesArray(unitStateJSON, wsw.getActivityStates());
        }
    }
    
}
