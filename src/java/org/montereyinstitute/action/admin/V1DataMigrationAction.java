package org.montereyinstitute.action.admin;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.persistence.Hibernate;
import gs.hinkleworld.core.AppUtil;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.Bookmark.*;
import org.montereyinstitute.model.Score.TopicScoreType;
import org.montereyinstitute.model.Score.UnitScoreType;
import org.montereyinstitute.model.TimeOnTask.DevMathTopicTimeType;
import org.montereyinstitute.tagmodel.Navigation;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet("/admin/datamigration")
public class V1DataMigrationAction extends Action {
    private static Logger log = LogManager.getLogger(V1DataMigrationAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);

        Long totalRows = (Long) Hibernate.getObjectHQL("select count(*) from V1DataRow");
        if( request.getParameter("doDataMigration") != null && request.getParameter("doDataMigration").equals("true") ){
            try{
                processDataTable(request, totalRows);
                request.setAttribute("MigrationDone", true);
            }
            catch(Throwable t){
                request.setAttribute("ErrorMsg", "An Error Ocurred.<br/>Message: "+t.getMessage()+"<br/>See log for thread ["+Thread.currentThread().getName()+"] at "+new Date());
                log.error(t, t);
            }
        }
        request.setAttribute("V1DataRows", totalRows);
        Navigation.getNavigation(request, response).setButtonSelected("siteManagement", true);
        return "/WEB-INF/jsp/admin/dataMigration.jsp";
    }
    
    private void processDataTable(HttpServletRequest request, Long totalRows) throws Throwable{
//        String hql = "from V1DataRow as v where v.classId = 33 and (v.studentId is null or v.studentId = 'matt1215_33')";
        String hql = "from V1DataRow as v where v.orphan is null or v.orphan = false";
        Integer batchSize = 100;
        List<V1DataRow> listHQL = Hibernate.getListHQL(hql, batchSize);
        Integer count = 0;
        Integer matchedCount = 0;
        Integer unmatchedCount = 0;
        while( listHQL != null && listHQL.size() > 0 && totalRows > matchedCount+unmatchedCount ){
            log.info("processing "+count+"...");
            for( V1DataRow row : listHQL ){
                Boolean unmatched = false;
                count++;
                log.debug("row: "+row.getStudentId()+" - "+row.getClassId()+" - "+row.getDataKey()+" - "+row.getDataValue()+" - "+row.getMigrationId());        
                GSClass c = null;
                if( row.getClassId() != null ){
                    c = (GSClass) Hibernate.get(GSClass.class, row.getClassId());
                }
                if( c == null ){
                    unmatched = true;
                }
                else{
                    Student s = null;
                    if( row.getStudentId() != null ){
                        s = (Student) Hibernate.get(Student.class, row.getStudentId());
                    }
                    if( s == null ){
                        if( row.getDataKey().equals("THRESHOLD") ||  
                            row.getDataKey().equals("QUICKPREASSESSMENT") ||  
                            row.getDataKey().equals("RETAKEPREASSESSMENT") ||  
                            row.getDataKey().equals("CLOAKED") ){
                                if( !processClassDataRow(c, row) ) unmatched = true;
                        } 
                        else{
                            unmatched = true;
                        }
                    }
                    else{
                        if( !processStudentDataRow(c, s, row) ) unmatched = true;
                        else{
                            if( s.getClasses() == null ){
                                s.setClasses(new HashSet<GSClass>());
                            }
                            s.getClasses().add(c);
                            Hibernate.save(s);
                        }
                    }
                }
                if( unmatched ){
                    unmatchedCount++;
                    row.setOrphan(true);
                }
                else{
                    matchedCount++;
                    Hibernate.delete(row);
                }
                Hibernate.flush();
            }
            Hibernate.commit();
            Hibernate.end();
            listHQL = null;
            Hibernate.evictQueryRegions();
            Runtime.getRuntime().gc();
            Hibernate.begin();
            listHQL = Hibernate.getListHQL(hql, batchSize);
        }
        log.info("unmatched: "+unmatchedCount);

        listHQL = null;
        Hibernate.evictQueryRegions();
        Runtime.getRuntime().gc();

        hql = "from V1DataRow as v where v.orphan = true";
        listHQL = Hibernate.getListHQL(hql);
        StringBuilder sb = new StringBuilder();
        for( V1DataRow row : listHQL ){
            sb.append(row.getStudentId());
            sb.append("\t");
            sb.append(row.getClassId());
            sb.append("\t");
            sb.append(row.getDataKey());
            sb.append("\t");
            sb.append(row.getDataValue());
            sb.append("\t");
            sb.append(row.getMigrationId());
            sb.append(System.lineSeparator());
            Hibernate.delete(row);
        }
        Hibernate.flush();
        AppUtil.writeFileInConfigDirectory("GradeService-V1DataOrphans.out", sb.toString(), false);
    }

    private Boolean processClassDataRow(GSClass c, V1DataRow row) throws Throwable {
        if( row.getDataKey().equals("THRESHOLD") ){
            Integer threshold = Integer.parseInt(row.getDataValue());
            c.setThreshold(threshold);
            return true;
        }
        else if( row.getDataKey().equals("QUICKPREASSESSMENT") ){
            Boolean quickPre = Boolean.parseBoolean(row.getDataValue());
            c.setQuickPreassessment(quickPre);
            return true;
        }
        else if( row.getDataKey().equals("RETAKEPREASSESSMENT") ){
            Boolean retakePre = Boolean.parseBoolean(row.getDataValue());
            c.setRetakePreassessment(retakePre);
            return true;
        }
        else if( row.getDataKey().equals("CLOAKED") ){
            for( String cloakedTopic : row.getDataValue().split(",") ){
                Pattern p = Pattern.compile("U(\\d+)L(\\d+)T(\\d+)");
                Matcher m = p.matcher(cloakedTopic);
                if( m.matches() ){
                    Integer unit = Integer.parseInt(m.group(1));
                    Integer lesson = Integer.parseInt(m.group(2));
                    Integer topic = Integer.parseInt(m.group(3));
                    TopicStateForClass tsc = TopicStateForClass.getNewOrExisting(c, unit, lesson, topic);
                    tsc.setCloaked(true);
                    Hibernate.save(tsc);
                }
            }
            return true;
        }
        return false;
    }

    private Boolean processStudentDataRow(GSClass c, Student s, V1DataRow row) throws Throwable {
        Pattern p = Pattern.compile("U(\\d+)L(\\d+)T(\\d+)(.+)");
        Matcher m = p.matcher(row.getDataKey());
        if( m.matches() ){
            return processTopicStudentDataRow(c, s, row, m);
        }
        else{
            return processUnitStudentDataRow(c, s, row);
        }
    }

    private Boolean processTopicStudentDataRow(GSClass c, Student s, V1DataRow row, Matcher m) throws Throwable {
        Integer unit = Integer.parseInt(m.group(1));
        Integer lesson = Integer.parseInt(m.group(2));
        Integer topic = Integer.parseInt(m.group(3));
        TopicStateForStudentClass tssc = TopicStateForStudentClass.getNewOrExisting(s, c, unit, lesson, topic);
        String descriptor = m.group(4);
        if( descriptor.equals("ATTEMPTS") ){
            Integer attempts = Integer.parseInt(row.getDataValue());
            tssc.setAttempts(attempts);
            Hibernate.save(tssc);
            return true;
        }
        else if( descriptor.equals("TIME") ){
            descriptor = DevMathTopicTimeType.TOTAL.getDescription();
            String[] split = row.getDataValue().split(":");
            Integer hours = Integer.parseInt(split[0]);
            Integer minutes = Integer.parseInt(split[1]);
            Integer seconds = Integer.parseInt(split[2]);
            Float total = minutes.floatValue();
            total += hours.floatValue()*60.0F;
            total += seconds.floatValue()/60.0F;
            tssc.addOrUpdateTime(descriptor, total);
            Hibernate.save(tssc);
            return true;
        }
        else if( descriptor.equals("PRE") ){
            tssc.setPreassessmentState(row.getDataValue());
            Hibernate.save(tssc);
            return true;
        }
        else if( descriptor.equals("SCORE") ){
            descriptor = TopicScoreType.REVIEW.getDescription();
            Integer score = Integer.parseInt(row.getDataValue());
            tssc.addOrUpdateScore(descriptor, score, false);
            Hibernate.save(tssc);
            return true;
        }
        else if( descriptor.equals("PRE_SCORE") ){
            descriptor = TopicScoreType.PREASSESSMENT.getDescription();
            Integer prescore = Integer.parseInt(row.getDataValue());
            tssc.addOrUpdateScore(descriptor, prescore, false);
            Hibernate.save(tssc);
            return true;
        }
        return false;
    }

    private Boolean processUnitStudentDataRow(GSClass c, Student s, V1DataRow row) throws Throwable {
        Pattern p = Pattern.compile("U(\\d+)(.+)");
        Matcher m = p.matcher(row.getDataKey());
        if( m.matches() ){
            Integer unit = Integer.parseInt(m.group(1));
            UnitStateForStudentClass ussc = UnitStateForStudentClass.getNewOrExisting(s, c, unit);
            String descriptor = m.group(2);
            if( descriptor.equals("PREATTEMPTS") ){
                Integer preattempts = Integer.parseInt(row.getDataValue());
                ussc.setPreattempts(preattempts);
                Hibernate.save(ussc);
                return true;
            }
            else if( descriptor.equals("PRESCORE") ){
                descriptor = UnitScoreType.PREASSESSMENT.getDescription();
                Integer score = Integer.parseInt(row.getDataValue());
                ussc.addOrUpdateScore(descriptor, score, false);
                Hibernate.save(ussc);
                return true;
            }
            else if( descriptor.equals("BOOKMARK") ){
                if( row.getDataValue().contains("Unit Map:U") ){ // :U ensures empty values will be ignored
                    String shortValue = row.getDataValue().replace("Unit Map:", "");
                    ussc.addOrUpdateBookmark( UnitBookmarkType.CURRENT_TAB.getDescription(), UnitBookmarkValue.UNIT_MAP.toString().toLowerCase());
                    ussc.addOrUpdateBookmark( UnitBookmarkType.CURRENT_TOPIC.getDescription(), shortValue);
                }
                else if( row.getDataValue().contains("My Learning Path:U") ){
                    String shortValue = row.getDataValue().replace("My Learning Path:", "");
                    ussc.addOrUpdateBookmark( UnitBookmarkType.CURRENT_TAB.getDescription(), UnitBookmarkValue.MY_LEARNING_PATH.toString().toLowerCase());
                    ussc.addOrUpdateBookmark( UnitBookmarkType.CURRENT_TOPIC.getDescription(), shortValue);
                }
                Hibernate.save(ussc);
                return true;
            }
        } 
        return false;
    }
    
}
