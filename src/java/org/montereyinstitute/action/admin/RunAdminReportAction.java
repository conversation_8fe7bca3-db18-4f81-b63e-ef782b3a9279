package org.montereyinstitute.action.admin;

import java.util.Arrays;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.action.admin.test.NoHibernateTestAction;
import org.montereyinstitute.schedule.job.SessionActivityReportArchiveJob;

/**
 *
 * <AUTHOR>
 */
@WebServlet("/admin/activityReport")
public class RunAdminReportAction extends NoHibernateTestAction {
    private static final Logger log = LogManager.getLogger(RunAdminReportAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.info("ADMIN ACTIVITY REPORT STARTED");
        new Thread(new SessionActivityReportArchiveJob()).start();
        request.setAttribute("MessageKey", "adminMessage-activityReportStarted");
        return "/WEB-INF/jsp/user/message.jsp";
    }
    
}
