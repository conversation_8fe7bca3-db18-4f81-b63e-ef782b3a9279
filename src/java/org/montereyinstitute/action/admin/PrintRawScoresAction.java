package org.montereyinstitute.action.admin;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.persistence.Hibernate;
import java.util.List;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.Course;
import org.montereyinstitute.model.QuestionState;
import org.montereyinstitute.model.Score;
import org.montereyinstitute.model.TopicStateForStudentClass;
import org.montereyinstitute.model.UnitStateForStudentClass;

/**
 * <AUTHOR>
 */
@WebServlet("/admin/rawScores")
public class PrintRawScoresAction extends Action {
    private static Logger log = LogManager.getLogger(PrintRawScoresAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        if( request.getParameter("unitStateId") == null ){
            log.info("unitStateId parameter required");
            return null;
        }
        Long unitStateId = Long.parseLong(request.getParameter("unitStateId"));
        UnitStateForStudentClass unitState = (UnitStateForStudentClass) Hibernate.get(UnitStateForStudentClass.class, unitStateId);
        
        log.info("courseId: "+unitState.getGsClass().getCourseId());
        Course appCourseNode = Course.getCourse(request, unitState.getGsClass().getCourseId());

        Score unitPreScore = unitState.getScoreByDescription("unit preassessment");
        if( unitPreScore != null ){
            log.info("Preassessment Score: "+unitPreScore.getValue());
        }
        unitState.loadTopicStates(appCourseNode);
        if( unitState.getTopicStates() != null ){
            for( TopicStateForStudentClass topicState : unitState.getTopicStates() ){
                Score topicPreScore = topicState.getScoreByDescription("topic preassessment");
                if( topicPreScore != null ){
                    log.info("Topic Pre Score: "+topicPreScore.getValue());
                }
            }
        }

        List<QuestionState> questionStates = unitState.getQuestionStatesForThisContext();
        if( questionStates != null ){
            for( QuestionState questionState : questionStates ){
                Score questionScore = questionState.getScoreByDescription("question primary");
                if( questionScore != null ){
                    log.info("Question Score: "+questionScore.getValue());
                }
                
            }
        }
        
        return null;
    }
    
}
