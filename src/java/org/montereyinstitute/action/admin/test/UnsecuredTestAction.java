package org.montereyinstitute.action.admin.test;

import gs.hinkleworld.core.AppUtil;
import gs.hinkleworld.persistence.Hibernate;
import java.io.IOException;
import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.hibernate.HibernateException;

/**
 * <AUTHOR>
 */
public abstract class UnsecuredTestAction extends HttpServlet {
    private static final Logger log = LogManager.getLogger(UnsecuredTestAction.class);
    
    protected abstract String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable;
    
    private void doActionForward(HttpServletRequest request, HttpServletResponse response){
        try{
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            if( log.isDebugEnabled() ) System.out.println();
            log.debug("fullURL: "+request.getRequestURL().append("?").append(request.getQueryString()));
            log.debug("method: "+request.getMethod());
            log.debug("serverName: "+request.getServerName());
            log.debug("servletPath: "+request.getServletPath());
            log.debug("pathInfo: "+request.getPathInfo());
            log.debug("session id: "+request.getSession().getId());
            log.debug("session is new: "+request.getSession().isNew());
            request.setAttribute("ServletPath", request.getServletPath()); //in jsp, pageContext.request.servletPath will get path to jsp, not servlet
            if( !request.getServletPath().contains("ajax/dialog") ){
                request.setAttribute("Referer", request.getServletPath()); //Use this to create links back to GS (which servlet in GS will be the )
                request.getSession().setAttribute("Referer", request.getServletPath()); //This is necessary for ajax/dialog paths which should refer back to parent page
            }

            Hibernate.begin();
            String destination = doAction(request, response);
            Hibernate.commit();
            Hibernate.end();
            if( destination != null ){
                RequestDispatcher dispatcher = request.getRequestDispatcher(destination);
                dispatcher.forward(request, response);
            }
        }
        catch(Throwable t){
            log.info("fullURL for exception: "+request.getRequestURL().append("?").append(request.getQueryString()));
            AppUtil.logRequestParams(request);
            if( t instanceof HibernateException ){
                log.error(t);
            }
            else{
                log.error(t, t);
            }
            Hibernate.rollback();
            Hibernate.end();
            try{
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } 
            catch (IOException ex) {
                log.error(ex, ex);
            }
        }
    };

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response){
        doActionForward(request, response);
    }
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response){
        doActionForward(request, response);
    }

}
