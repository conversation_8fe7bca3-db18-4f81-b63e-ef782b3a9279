package org.montereyinstitute.action.admin;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.persistence.Hibernate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;

/**
 *
 * <AUTHOR>
 */
@WebServlet("/admin/checkUnitStates")
public class RemoveUnitStateForClassDups extends Action {
    private static Logger log = LogManager.getLogger(RemoveUnitStateForClassDups.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        boolean delete = false;
        if( request.getParameter("delete") != null && request.getParameter("delete").equals("true") ) delete = true;
        log.info("delete: "+delete);
        
        int totalDups = 0;
        
        String institutionParam = request.getParameter("institution");
        if( institutionParam.equals("all") ){
            List<Institution> institutions = Hibernate.getListHQL("from Institution");
            log.info("institutions size: "+institutions.size());
            for( Institution institution : institutions ){
                totalDups += processInstitution(institution, delete);
            }
        }
        else{
            Long institutionId = Long.parseLong(institutionParam);
            Institution institution = (Institution) Hibernate.get(Institution.class, institutionId);
            if( institution == null ){
                log.warn("null institution");
                return null;
            }
            else{
                totalDups += processInstitution(institution, delete);
            }
        }
        log.info("totalDups: "+totalDups);
        return null;
    }

    private int processInstitution(Institution institution, boolean delete) throws Throwable{
        log.info("institution: "+institution+", "+institution.getInstitutionName());
        int totalDups = 0;
        Set<GSClass> classes = CollectionLoader.getClasses(institution);
        log.info("classes size: "+classes.size());
        for( GSClass gsClass : classes ){
            log.info("gsClass: "+gsClass);
            
            String hql = "from UnitStateForClass as usfc"
                +" where usfc.gsClass.id="+gsClass.getClassId()
                +" order by unit, id";
            List<UnitStateForClass> states = Hibernate.getListHQL(hql);
            
            //SEPARATE STATES BY UNIT
            Map<Integer,List<UnitStateForClass>> map = new HashMap<Integer,List<UnitStateForClass>>();
            for( int i=0; i<states.size(); i++ ){
                UnitStateForClass state = states.get(i);
                List<UnitStateForClass> unitList = map.get(state.getUnit());
                if( unitList == null ){
                    unitList = new ArrayList<UnitStateForClass>();
                    map.put(state.getUnit(), unitList);
                }
                unitList.add(state);
            }
            
            //DELETE DUPS FROM EACH UNIT LIST
            for( Integer unit : map.keySet() ){
                log.debug("unit "+unit);
                List<UnitStateForClass> unitList = map.get(unit);
                log.debug("unitList size: "+unitList.size());
                if( unitList.size() > 0 ){
                    for( int i=0; i<unitList.size(); i++ ){
                        if( i==0 ) continue;
                        UnitStateForClass dup = unitList.get(i);
                        log.info("found dup: "+dup.getId()+", unit "+dup.getUnit());
                        totalDups++;
                        if( delete ){
                            Hibernate.delete(dup);
                            log.debug("deleted");
                        }
                    }
                }
                else{
                    log.info("no dups");
                }
            }
        }
        log.info("totalDups for institution: "+totalDups);
        return totalDups;
    }
}
