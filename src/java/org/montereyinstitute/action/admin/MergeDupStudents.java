
package org.montereyinstitute.action.admin;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.persistence.Hibernate;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CollectionLoader;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.Student;
import org.montereyinstitute.model.TopicStateForStudentClass;
import org.montereyinstitute.model.UnitStateForStudentClass;
import org.montereyinstitute.model.User;
import org.montereyinstitute.tagmodel.Navigation;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
//@WebServlet("/admin/merge_dup_students")
public class MergeDupStudents extends Action  {
    private static Logger log = LogManager.getLogger(MergeDupStudents.class);
    
    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);

        if( request.getParameter("doAdminAction") != null && request.getParameter("doAdminAction").equals("true") ){
            try{
                String hql = "from GSClass";
                List<GSClass> classes = Hibernate.getListHQL(hql);
                if( classes != null ){
                    log.info("Retrieved classes: "+classes.size());
                    for( GSClass gsClass : classes ){
                        processClass(gsClass);
                    }
                }
                else{
                    log.warn("No classes were retrieved.");
                }
                request.setAttribute("SuccessMsg", true);
            }
            catch(Throwable t){
                request.setAttribute("ErrorMsg", "An Error Ocurred.<br/>Message: "+t.getMessage()+"<br/>See log for thread ["+Thread.currentThread().getName()+"] at "+new Date());
                log.error(t, t);
            }
        }
        Navigation.getNavigation(request, response).setButtonSelected("siteManagement", true);
        return "/WEB-INF/jsp/admin/mergeDupStudents.jsp";
    }

    private void processClass(GSClass gsClass) throws Throwable{
        log.info("processClass: "+gsClass);
        Set<Student> students = CollectionLoader.getStudents(gsClass);
        if( students == null || students.isEmpty() ){
            log.info("No students were retrieved");
            return;
        }
        for( Student student : students ){
            Student matchingStudent = searchForMatchingStudent(gsClass, students, student);
            Student keepStudent = student;
            Student removeStudent = matchingStudent;
            if( student.getSiteUrl().startsWith("http://") && matchingStudent.getSiteUrl().startsWith("https://") ){
                keepStudent = matchingStudent;
                removeStudent = student;
            }
            //mergeStudents(keepStudent, removeStudent, gsClass);
        }
    }

    private Student searchForMatchingStudent(GSClass gsClass, Set<Student> students, Student targetStudent) {
        String classIdSuffix_1 = "_"+gsClass.getClassId();
        int lastIndex_1 = targetStudent.getStudentId().lastIndexOf(classIdSuffix_1);
        String studentIdWithoutClassIdSuffix_1 = targetStudent.getStudentId();
        if( lastIndex_1 >= 0 ){
            studentIdWithoutClassIdSuffix_1 = studentIdWithoutClassIdSuffix_1.substring(0, lastIndex_1);
        }
        String siteUrlWithoutScheme_1 = targetStudent.getSiteUrl().replaceFirst("https://", "").replaceFirst("http://", "");
        
        for( Student student : students ){
            if( student.equals(targetStudent) ) continue;
            String classIdSuffix = "_";
            int lastIndex = student.getStudentId().lastIndexOf(classIdSuffix);
            String studentIdWithoutClassIdSuffix = student.getStudentId();
            if( lastIndex >= 0 ){
                studentIdWithoutClassIdSuffix = studentIdWithoutClassIdSuffix.substring(0, lastIndex);
            }
            String siteUrlWithoutScheme = student.getSiteUrl().replaceFirst("https://", "").replaceFirst("http://", "");
            if( !studentIdWithoutClassIdSuffix.equals(studentIdWithoutClassIdSuffix_1) ) continue;
            if( !siteUrlWithoutScheme.equals(siteUrlWithoutScheme_1) ) continue;
            if( !student.getFirstName().equals(targetStudent.getFirstName()) ) continue;
            if( !student.getLastName().equals(targetStudent.getLastName()) ) continue;
            log.info("Match found...");
            log.info("targetStudent: "+targetStudent);
            log.info("matchStudent: "+student);
            log.info("studentId: "+targetStudent.getStudentId()+" (target)");
            log.info("studentId: "+student.getStudentId()+" (match)");
            log.info("siteUrl: "+targetStudent.getSiteUrl()+" (target)");
            log.info("siteUrl: "+student.getSiteUrl()+" (match)");
            log.info("firstName: "+targetStudent.getFirstName()+" (target)");
            log.info("firstName: "+student.getFirstName()+" (match)");
            log.info("lastName: "+targetStudent.getLastName()+" (target)");
            log.info("lastName: "+student.getLastName()+" (match)");
            return student;
        }
        return null;
    }

    private void mergeStudents(Student keepStudent, Student removeStudent, GSClass gsClass) throws Throwable {
        List<UnitStateForStudentClass> keepUnitStates = keepStudent.loadAllUnitStatesForOneClass(gsClass, null);
        List<UnitStateForStudentClass> removeUnitStates = removeStudent.loadAllUnitStatesForOneClass(gsClass, null);
        for( UnitStateForStudentClass removeUnitState : removeUnitStates ){
            mergeUnitState(removeUnitState, keepUnitStates);
        }
        
        
        List<TopicStateForStudentClass> keepTopicStates = keepStudent.loadAllTopicStatesForOneClass(gsClass, null);
        List<TopicStateForStudentClass> removeTopicStates = removeStudent.loadAllTopicStatesForOneClass(gsClass, null);
    }

    private void mergeUnitState(UnitStateForStudentClass removeUnitState, List<UnitStateForStudentClass> keepUnitStates) throws Throwable{
        UnitStateForStudentClass matchingKeepUnitState = null;
        for( UnitStateForStudentClass keepUnitState : keepUnitStates ){
        }
    }

}
