package org.montereyinstitute.action.report;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CollectionLoader;
import org.montereyinstitute.model.CourseNode;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.Student;
import org.montereyinstitute.model.TopicStateForStudentClass;
import org.montereyinstitute.model.UnitStateForStudentClass;
import org.montereyinstitute.model.User;
import org.montereyinstitute.tagmodel.Navigation;
import org.montereyinstitute.view.devMath.TopicStateView;
import org.montereyinstitute.view.devMath.UnitStateView;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet(urlPatterns={"/report/full_class_export"})
public class FullClassExportAction extends Action {
    private static Logger log = LogManager.getLogger(UnitProgressReportForDevMathAction.class);
    
    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        
        GSClass gsClass = GSClass.getGSClassUseSession(request);
        if( gsClass == null ){
            response.sendRedirect(AppUtil.getUrl(request, response, "/report/unit_progress"));
            return null;
        }
        CourseNode.Course appCourseNode = CourseNode.Course.getCourse(request, gsClass.getCourseId());
        request.setAttribute("AppCourse", appCourseNode);
            
        SortedSet<Student> sortedStudents = CollectionLoader.getSortedStudents(gsClass);
        
        //These load data to avoid lazy loading errors in jsp
        Map<Student,Map<String,UnitStateView>> studentUnitStateViews = new HashMap<Student,Map<String,UnitStateView>>();
        Map<Student,Map<String,TopicStateView>> studentTopicStateViews = new HashMap<Student,Map<String,TopicStateView>>();

        Map<Student, Map<String,UnitStateForStudentClass>> unitStatesMap = gsClass.loadSortedUnitStatesMapForAllUnits(appCourseNode);
        Map<Student, List<TopicStateForStudentClass>> topicStatesMap = gsClass.loadTopicStatesForStudentClassMapForAllUnits(appCourseNode);
        //Some students may not have unitStates, make sure unitStateMap has all students, and all unitStates are initialized with their topicStates
        for( Student s : sortedStudents ){
            log.debug("student: "+s.getStudentId());
            studentUnitStateViews.put(s, new HashMap<String,UnitStateView>());
            studentTopicStateViews.put(s, new HashMap<String,TopicStateView>());
            Map<String,UnitStateForStudentClass> unitStates = unitStatesMap.get(s);
            if( unitStates == null ){
                log.debug("unitStates is null");
                unitStatesMap.put(s, null);
            }
            else{
                for( UnitStateForStudentClass unitState : unitStates.values() ){
                    log.debug("unitState id="+unitState.getId());
                    unitState.initAppUnitNode(appCourseNode);
                    studentUnitStateViews.get(s).put(unitState.getAppUnitNode().getId(), new UnitStateView(unitState));
                    List<TopicStateForStudentClass> topicStatesList = topicStatesMap.get(s);
                    if( topicStatesList != null ){
                        unitState.initTopicStatesFromListWithUnitCheck(appCourseNode, topicStatesList);
                        unitState.verifyPreattemptsWithTopicsStates();
                        for( TopicStateForStudentClass topicState : topicStatesList ){
                            topicState.initAppCourseNodes(appCourseNode);
                            studentTopicStateViews.get(s).put(topicState.getAppTopicNode().getId(), new TopicStateView(topicState));
                        }
                    }
                }
            }
        }
        request.setAttribute("UnitStatesMap", unitStatesMap);
        request.setAttribute("unitStateViews", studentUnitStateViews);
        request.setAttribute("topicStateViews", studentTopicStateViews);

        Navigation.getNavigation(request, response).setButtonSelected("viewReport", true);
        String filename = "NROC-Math-FullClassExport-"+gsClass.getClassName()+".xls";
        String contentDispositionHeader = "attachment; filename=\""+filename+"\"";
        response.setHeader("Content-Disposition", contentDispositionHeader);
        request.setAttribute("IncludeFile", "/WEB-INF/jsp/reports/fullClassExportTable.jsp");
        return "/WEB-INF/jsp/reports/excelDownloadWrapper.jsp";
        
    }
    
}
