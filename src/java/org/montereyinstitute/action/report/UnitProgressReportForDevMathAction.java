package org.montereyinstitute.action.report;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppModel;
import gs.hinkleworld.core.AppUtil;
import java.util.*;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.tagmodel.Navigation;
import org.montereyinstitute.tagmodel.UnitProgressReport;
import org.montereyinstitute.tagmodel.ViewSettings;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet(urlPatterns={"/report/unit_progress"})
public class UnitProgressReportForDevMathAction extends Action {
    private static Logger log = LogManager.getLogger(UnitProgressReportForDevMathAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        if( ViewSettings.getViewSettingsUseSession(request).getIsForDevEnglish() ){
            response.sendRedirect(AppUtil.getUrl(request, response, "/report/unit_progress/de"));
            return null;
        }
        
        request.setAttribute("TitleKey", "report-unitProgress-title");
        request.setAttribute("SortedClasses", CollectionLoader.getSortedClasses(user));

        GSClass gsClass = GSClass.getGSClassUseSession(request);
        if( gsClass != null ){ 
            Course appCourseNode = Course.getCourse(request, gsClass.getCourseId());
            request.setAttribute("AppCourse", appCourseNode);
            Unit unit = Unit.getUnit(request, gsClass.getCourseId(), 1);
            if( unit != null ){
                Map<String,TopicStateForClass> topicStatesForClassMapForUnit = gsClass.loadTopicStatesForClassMapForUnit(unit.getNumber(), appCourseNode);
                UnitProgressReport upr = UnitProgressReport.getUnitProgressReport(request);
                upr.refresh(request, unit, topicStatesForClassMapForUnit);
                SortedSet<Student> sortedStudents = CollectionLoader.getSortedStudents(gsClass);
                Map<Student, UnitStateForStudentClass> unitStatesMap = gsClass.loadSortedUnitStatesMap(unit.getNumber(), appCourseNode);
                Map<Student, List<TopicStateForStudentClass>> topicStatesForStudentClassMap = gsClass.loadTopicStatesForStudentClassMapForUnit(unit.getNumber(), appCourseNode);
                //Some students may not have unitStates, make sure unitStateMap has all students, and all unitStates are initialized with their topicStates
                for( Student s : sortedStudents ){
                    log.debug("student: "+s.getStudentId());
                    s.getActive(gsClass);
                    UnitStateForStudentClass unitState = unitStatesMap.get(s);
                    if( unitState == null ){
                        log.debug("unitState is null");
                        unitStatesMap.put(s, null);
                    }
                    else{
                        log.debug("unitState id="+unitState.getId());
                        List<TopicStateForStudentClass> topicStatesList = topicStatesForStudentClassMap.get(s);
                        if( topicStatesList != null ){
                            unitState.initTopicStatesFromListWithUnitCheck(appCourseNode, topicStatesList);
                            unitState.verifyPreattemptsWithTopicsStates();
                        }
                    }
                }
                ClassStatisticsForUnit classStats = new ClassStatisticsForUnit(unitStatesMap.values(), unit, upr.getCheckboxInactiveStudents());

                request.setAttribute("TopicStatesForClassMap", topicStatesForClassMapForUnit);
                request.setAttribute("SortedStudents", sortedStudents);
                request.setAttribute("ClassStatisticsForUnit", classStats);
                request.setAttribute("UnitStateMap", unitStatesMap);

                if( request.getParameter("export") != null && request.getParameter("export").equals("true") ){
                    String filename = "NROC-Math-UnitProgress-"+unit.getId()+"-"+gsClass.getClassName()+".xls";
                    String contentDispositionHeader = "attachment; filename=\""+filename+"\"";
                    response.setHeader("Content-Disposition", contentDispositionHeader);
                    request.setAttribute("IncludeFile", "/WEB-INF/jsp/reports/unitProgressTable.jsp");
                    return "/WEB-INF/jsp/reports/excelDownloadWrapper.jsp";
                }
            }
        }
        
        Navigation.getNavigation(request, response).setButtonSelected("viewReport", true);
        return "/WEB-INF/jsp/reports/unitProgress.jsp";
    }

}