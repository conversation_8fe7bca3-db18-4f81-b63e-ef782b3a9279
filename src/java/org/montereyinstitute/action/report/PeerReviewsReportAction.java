package org.montereyinstitute.action.report;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppModel;
import gs.hinkleworld.core.AppUtil;
import gs.hinkleworld.persistence.Hibernate;
import gs.hinkleworld.core.SessionUtil;
import java.util.*;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.tagmodel.Navigation;
import org.montereyinstitute.tagmodel.ViewSettings;
import org.montereyinstitute.util.DevEnglishUtil;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet(urlPatterns={"/report/peer_reviews/*"})
public class PeerReviewsReportAction extends Action {
    private static final Logger log = LogManager.getLogger(PeerReviewsReportAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        if( ViewSettings.getViewSettingsUseSession(request).getIsForDevMath() ){
            response.sendRedirect(AppUtil.getUrl(request, response, "/report"));
            return null;
        }
        
        request.setAttribute("SortedClasses", CollectionLoader.getSortedClasses(user));
        request.setAttribute("AppCourse", AppModel.getAppModel(request).getCourses().get(Course.ID_DEV_ENGLISH));

        StudentTabReports report = (StudentTabReports) SessionUtil.getSessionSingleton(StudentTabReports.class, request);
        Boolean newReport = false;
        if( report == null || (request.getParameter("refresh") != null && request.getParameter("refresh").equals("true")) ){
            report = new StudentTabReports();
            newReport = true;
            SessionUtil.setSessionSingleton(report, request);
        }
        
        GSClass gsClass = GSClass.getGSClassUseSession(request);
        if( gsClass != null ){
            PrefetchedQuestionStates prefetchedQuestionStates = null;
            if(gsClass.getPrefetchedQuestionStates() == null || newReport ){
                prefetchedQuestionStates = new PrefetchedQuestionStates(gsClass.getClassId());
            }
            else{
                prefetchedQuestionStates = gsClass.getPrefetchedQuestionStates();
            }
            gsClass.setPrefetchedQuestionStates(prefetchedQuestionStates);
        }
        
        boolean classChanged = true;
        if( report.getCurrentClass() != null && report.getCurrentClass().equals(gsClass) ){
            classChanged = false;
        }
        report.setCurrentClass(gsClass);

        Unit unit = Unit.getUnit(request, Course.ID_DEV_ENGLISH, 1);
        boolean unitChanged = true;
        if( report.getCurrentUnit()!= null && report.getCurrentUnit().equals(unit) ){
            unitChanged = false;
        }
        report.setCurrentUnit(unit);
        
        Course appCourseNode = Course.getCourse(request, Course.ID_DEV_ENGLISH);
        
        if( gsClass != null && unit != null ){
            UnitStateForClass unitStateForClass = UnitStateForClass.getUnitStateForClassUseSession(request, gsClass, unit.getNumber());
            if( unitStateForClass == null ){
                unitStateForClass = UnitStateForClass.createUnitStateForClass(gsClass, unit.getNumber(), appCourseNode);
                SessionUtil.setSessionSingleton(unitStateForClass, request);
            }
            unitStateForClass.initAppUnitNode(appCourseNode);
            List<Integer> peerReviewDraftSteps = new ArrayList<Integer>();
            if( unitStateForClass.getWritingCenterPeerReviewSteps() != null ) peerReviewDraftSteps.addAll(unitStateForClass.getWritingCenterPeerReviewSteps().keySet());
            Collections.sort(peerReviewDraftSteps);
            log.debug("peerReviewDraftSteps: "+peerReviewDraftSteps);
            request.setAttribute("peerReviewDraftSteps", peerReviewDraftSteps);

            
            Map<Student, UnitStateForStudentClass> unitStatesMap = report.getSortedUnitStatesMapForCurrentClassAndUnit();
            if( unitStatesMap == null || classChanged || unitChanged ){
                unitStatesMap = gsClass.loadSortedUnitStatesMap(unit.getNumber(), appCourseNode);
                if( unitStatesMap != null ){
                    report.addUnitStates(unitStatesMap.values());
                }
                unitStatesMap = report.getSortedUnitStatesMapForCurrentClassAndUnit(); //This will fill all students into map
            }
            log.debug("unitStatesMap: "+unitStatesMap);
            if( unitStatesMap != null ){
                int loadCount = 0;
                for( Student s : unitStatesMap.keySet() ){
                    s.getActive(gsClass); //sets view for JSP
                    UnitStateForStudentClass unitState = unitStatesMap.get(s);
                    if( unitState != null ){
                        log.debug("unitState id="+unitState.getId());
                        if( unitState.getHasLoadedDevEnglishTransients() ) continue;
                        if( !newReport && loadCount < StudentTabReports.BATCH_SIZE ){
                            unitState.loadDevEnglishTransients();
                            loadCount++;
                        }
                        else{
                            request.setAttribute("moreToLoad", true);
                        }
                    }
                }
            }

            request.getSession().setAttribute("SortedStudents", report.getCurrentSortedStudents());
            request.setAttribute("UnitStateMap", unitStatesMap);
            request.setAttribute("UnitStateForClass", unitStateForClass);
        }

        //This servlet sets "moreToLoad" attribute for client to call more loading.
        //I'm adding a loopCount so the client won't enter an infinite loop if an error occurs with the "moreToLoad"
        Integer loopMaxCount = 0; 
        if( report != null && report.getCurrentSortedStudents() != null ){
            loopMaxCount = report.getCurrentSortedStudents().size() / StudentTabReports.BATCH_SIZE;
            loopMaxCount++; //add one for remainder
            loopMaxCount += 2; //add two extra for possible non-fatal server errors
        }
        request.setAttribute("loopMaxCount", loopMaxCount);
        
        Navigation.getNavigation(request, response).setButtonSelected("viewReport", true);
        if( request.getPathInfo() != null && request.getPathInfo().contains("ajax/replaceTable") ){
            return "/WEB-INF/jsp/reports/devEnglish/peerReviewsTable.jsp";
        }
        return "/WEB-INF/jsp/reports/devEnglish/peerReviews.jsp";
    }

}