package org.montereyinstitute.action.report;

import gs.hinkleworld.core.Action;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CollectionLoader;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.User;
import org.montereyinstitute.tagmodel.Navigation;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet("/report")
public class ReportHomeAction extends Action {
    private static Logger log = LogManager.getLogger(ReportHomeAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        request.setAttribute("SortedClasses", CollectionLoader.getSortedClasses(user));
        if( user.getIsAdministrator() && request.getParameter("dupClassMode") != null ){
            request.setAttribute("SortedClasses", CollectionLoader.getAdminDuplicateClasses(user));
        }
        
        GSClass gsClass = GSClass.getGSClassUseSession(request);

        Navigation.getNavigation(request, response).setButtonSelected("viewReport", true);
        return "/WEB-INF/jsp/reports/reportHome.jsp";
    }
    
}
