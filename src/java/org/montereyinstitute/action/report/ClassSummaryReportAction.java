package org.montereyinstitute.action.report;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.AppModel;
import gs.hinkleworld.core.AppUtil;
import java.util.*;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.tagmodel.*;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet("/report/class_summary")
public class ClassSummaryReportAction extends Action {
    private static Logger log = LogManager.getLogger(ClassSummaryReportAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        log.debug("doAction");
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        if( ViewSettings.getViewSettingsUseSession(request).getIsForDevEnglish() ){
            response.sendRedirect(AppUtil.getUrl(request, response, "/report/unit_progress/de"));
            return null;
        }
        request.setAttribute("SortedClasses", CollectionLoader.getSortedClasses(user));

        GSClass gsClass = GSClass.getGSClassUseSession(request);
        if( gsClass != null ){
            Course appCourseNode = Course.getCourse(request, gsClass.getCourseId());
            request.setAttribute("AppCourse", appCourseNode);
            Map<String,TopicStateForClass> topicStatesForClassMap = gsClass.loadTopicStatesForClassMap(appCourseNode);
            ClassSummaryReport csr = ClassSummaryReport.getClassSummaryReport(request);
            csr.refresh(request, appCourseNode, topicStatesForClassMap);
            SortedSet<Student> sortedStudents = CollectionLoader.getSortedStudents(gsClass);
            ClassStatisticsForAllUnits classStats = new ClassStatisticsForAllUnits(gsClass, sortedStudents, appCourseNode, csr.getCheckboxInactiveStudents());
            
            request.setAttribute("TopicStatesForClassMap", topicStatesForClassMap);
            request.setAttribute("SortedStudents", sortedStudents);
            request.setAttribute("ClassStatisticsForAllUnits", classStats);

            if( request.getParameter("export") != null && request.getParameter("export").equals("true") ){
                String filename = "NROC-Math-ClassSummary-"+gsClass.getClassName()+".xls";
                String contentDispositionHeader = "attachment; filename=\""+filename+"\"";
                response.setHeader("Content-Disposition", contentDispositionHeader);
                request.setAttribute("IncludeFile", "/WEB-INF/jsp/reports/classSummaryTable.jsp");
                return "/WEB-INF/jsp/reports/excelDownloadWrapper.jsp";
            }
        }
        
        Navigation.getNavigation(request, response).setButtonSelected("viewReport", true);
        return "/WEB-INF/jsp/reports/classSummaryGraphical.jsp";
    }
    
}
