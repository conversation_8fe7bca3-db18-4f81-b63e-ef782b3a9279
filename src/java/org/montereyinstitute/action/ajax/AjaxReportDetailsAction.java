package org.montereyinstitute.action.ajax;

import gs.hinkleworld.core.Action;
import gs.hinkleworld.core.SessionUtil;
import java.io.PrintWriter;
import java.net.URLEncoder;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.PrefetchedQuestionStates;
import org.montereyinstitute.model.Student;
import org.montereyinstitute.model.StudentStatisticsForClass;
import org.montereyinstitute.model.StudentStatisticsForClassOfDevEnglish;
import org.montereyinstitute.model.StudentTabReports;
import org.montereyinstitute.model.User;

/**
 * <AUTHOR>
 */
@WebServlet("/ajax/report/*")
public class AjaxReportDetailsAction extends Action {
    private static Logger log = LogManager.getLogger(AjaxReportDetailsAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        log.debug("doAction");
        log.debug("params: "+request.getQueryString());

        StudentTabReports report = (StudentTabReports) SessionUtil.getSessionSingleton(StudentTabReports.class, request);
        Boolean newReport = false;
        if( report == null || (request.getParameter("refresh") != null && request.getParameter("refresh").equals("true")) ){
            report = new StudentTabReports();
            newReport = true;
            SessionUtil.setSessionSingleton(report, request);
        }
        
        GSClass gsClass = GSClass.getGSClassUseSession(request);
        if( gsClass != null ){
            PrefetchedQuestionStates prefetchedQuestionStates = null;
            if(gsClass.getPrefetchedQuestionStates() == null || newReport ){
                prefetchedQuestionStates = new PrefetchedQuestionStates(gsClass.getClassId());
            }
            else{
                prefetchedQuestionStates = gsClass.getPrefetchedQuestionStates();
            }
            gsClass.setPrefetchedQuestionStates(prefetchedQuestionStates);
        }
        
        report.setCurrentClass(gsClass);
        
        Student student = Student.getStudentUseSession(request, gsClass);
        CourseNode.Course appCourseNode = CourseNode.Course.getCourse(request, CourseNode.Course.ID_DEV_ENGLISH);
        StudentStatisticsForClassOfDevEnglish statsForClass = (StudentStatisticsForClassOfDevEnglish) report.getCurrentStatsForClass();
        if( statsForClass == null ){
            statsForClass = (StudentStatisticsForClassOfDevEnglish) StudentStatisticsForClass.getNewInstance(student, gsClass, appCourseNode);
            statsForClass.init();
        }
        report.addStatForClass(statsForClass);
        
        StringBuilder details = new StringBuilder();
        details.append("unitsAttempted-").append(URLEncoder.encode(student.getStudentId(), "UTF-8")).append("=");
        details.append(statsForClass.getUnitsAttempted());
        details.append("&");
        details.append("unitsCompleted-").append(URLEncoder.encode(student.getStudentId(), "UTF-8")).append("=");
        details.append(statsForClass.getUnitsCompleted());
        details.append("&");
        details.append("averageUnitReviewScore-").append(URLEncoder.encode(student.getStudentId(), "UTF-8")).append("=");
        details.append(statsForClass.getAverageUnitReviewScoreFormatted());
        details.append("&");
        details.append("totalTimeFormatted-").append(URLEncoder.encode(student.getStudentId(), "UTF-8")).append("=");
        details.append(statsForClass.getTotalTimeFormatted());
        PrintWriter out = response.getWriter();
        out.print(details.toString());
        out.close();
        return null;
    }
    
}
