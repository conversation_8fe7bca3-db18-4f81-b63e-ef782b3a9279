package org.montereyinstitute.action.ajax;

import gs.hinkleworld.core.Action;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.User;
import org.montereyinstitute.tagmodel.Dialog;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
@WebServlet("/ajax/dialog/*")
public class AjaxDialogAction extends Action {
    private static Logger log = LogManager.getLogger(AjaxDialogAction.class);

    @Override
    protected String doAction(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        User user = User.getUserUseSession(request);
        if( user == null ) return User.securityError(request, response);
        try{
            log.debug("doAction");
            log.debug("params: "+request.getQueryString());
            Dialog dialog =  Dialog.getDialog(request); //This is a factory method with subclass determined by request param
            dialog.init(request, response);
            request.setAttribute("Dialog", dialog);
            return dialog.getDestination();
        }
        catch(Throwable t){
            log.error(t, t);
        }
        return null;
    }
    
}
