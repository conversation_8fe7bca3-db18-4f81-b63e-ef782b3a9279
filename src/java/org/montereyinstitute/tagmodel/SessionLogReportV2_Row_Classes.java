package org.montereyinstitute.tagmodel;

import org.apache.logging.log4j.*;
import org.montereyinstitute.model.GSClass;

/**
 *
 * <AUTHOR>
 */
public class SessionLogReportV2_Row_Classes extends SessionLogReportV2_Row_Base implements Comparable<SessionLogReportV2_Row_Classes> {
    private static final Logger log = LogManager.getLogger(SessionLogReportV2_Row_Classes.class);
    
    private GSClass gsClass;
    private Integer sessions; //total student sessions
    private Integer activeStudents; //students with at least 1 session log entry (and not deactivated)
    private Integer enrolledStudents; //studens not deactivated

    public GSClass getGsClass() {
        return gsClass;
    }

    public String getGsClassName_nonNull(){
        if( gsClass == null || gsClass.getClassName() == null ) return "";
        return gsClass.getClassName();
    }
    
    public void setGsClass(GSClass gsClass) {
        this.gsClass = gsClass;
    }

    public Integer getSessions() {
        if( sessions == null ) return 0;
        return sessions;
    }

    public void setSessions(Integer sessions) {
        this.sessions = sessions;
    }

    public Integer getActiveStudents() {
        if( activeStudents == null ) return 0;
        return activeStudents;
    }

    public void setActiveStudents(Integer activeStudents) {
        this.activeStudents = activeStudents;
    }

    public Integer getEnrolledStudents() {
        if( enrolledStudents == null ) return 0;
        return enrolledStudents;
    }

    public void setEnrolledStudents(Integer enrolledStudents) {
        this.enrolledStudents = enrolledStudents;
    }

    @Override
    public int compareTo(SessionLogReportV2_Row_Classes other) {
        if( !this.getInstitutionName_nonNull().equals(other.getInstitutionName_nonNull()) ){
            return this.getInstitutionName_nonNull().compareTo(other.getInstitutionName_nonNull());
        }
        else if( !this.getCourseIdCompareValue().equals(other.getCourseIdCompareValue()) ){
            return this.getCourseIdCompareValue().compareTo(other.getCourseIdCompareValue());
        }
        return this.getGsClassName_nonNull().compareTo(other.getGsClassName_nonNull());
    }

}
