package org.montereyinstitute.tagmodel;

import gs.hinkleworld.core.AppUtil;
import gs.hinkleworld.persistence.Hibernate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.Course;
import org.montereyinstitute.model.UnitStateForStudentClass;
import org.montereyinstitute.model.User;

/**
 *
 * <AUTHOR>
 */
public class ModalDialogForDevEnglishWritingCenterResetOptions extends Dialog {
    private static final Logger log = LogManager.getLogger(ModalDialogForDevEnglishWritingCenterResetOptions.class);

    private UnitStateForStudentClass unitState;

    @Override
    public void init(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        User user = User.getUserUseSession(request);
        if( log.isDebugEnabled() ) AppUtil.logRequestParams(request);
        Course course = Course.getCourse(request, Course.ID_DEV_ENGLISH);
        Long unitStateId = Long.parseLong(request.getParameter("unitStateId"));
        unitState = (UnitStateForStudentClass) Hibernate.get(UnitStateForStudentClass.class, unitStateId);
        unitState.initAppUnitNode(course);
        unitState.loadWritingCenterState();
        
        request.setAttribute("Unit", unitState.getAppUnitNode());
        request.setAttribute("UnitState", unitState);
        request.setAttribute("Student", unitState.getStudent());
        
        if( user.getIsAdministrator() ){
            destination = "/WEB-INF/jsp/dialog/devEnglishWritingCenterResetOptionsFull.jsp";
        }
        else{
            destination = "/WEB-INF/jsp/dialog/devEnglishWritingCenterResetOptionsByStep.jsp";
        }
    }
    
}
