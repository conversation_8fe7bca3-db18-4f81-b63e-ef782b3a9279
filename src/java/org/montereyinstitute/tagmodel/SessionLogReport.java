package org.montereyinstitute.tagmodel;

import org.montereyinstitute.util.SessionLogReportsUtil;
import com.hinkleworld.lib.DateUtil;
import java.util.*;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.GSClass.GSClassUserActivityCountComparator;
import static org.montereyinstitute.util.SessionLogReportsUtil.getUniqueAgentCountsForPeriod;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class SessionLogReport {
    private static Logger log = LogManager.getLogger(SessionLogReport.class);
    
    public static final Integer NUM_MOST_LOGGED = 3;
    public static final Integer NUM_MONTHS_SELECTABLE = 12; //set to null to ignore
    
    private User user;
    protected Institution institution; //null for admin
    protected Date beginDate; //null for forever
    protected Date endDate; //null for forever
    protected List<Date> selectableMonths;
    protected Date selectedMonth; //null for forever
    protected Integer totalStudentSessionsForPeriod;
    protected Integer totalTeacherSessionsForPeriod;
    protected Integer uniqueLoggedStudentsForPeriod;
    protected Integer uniqueLoggedClassesForPeriod;
    protected Integer uniqueLoggedTeachersForPeriod;
    protected Integer totalUniqueStudents;
    protected Integer totalUniqueClasses;
    protected Integer totalUniqueTeachers;
    protected Map<Teacher,Integer> mostLoggedTeachersForPeriod;
    protected Map<GSClass,Integer> mostLoggedClassesForPeriod;
    
    //Adding division of reporting by courseId, e.g. devEnglish, devMath (see constants Course.ID_DEV_MATH, etc.)
    protected List<String> courseIdsReportable;
    //Key=courseId
    protected Map<String,Integer> totalUniqueStudentsByCourseId;
    protected Map<String,Integer> totalUniqueClassesByCourseId;
    protected Map<String,Integer> totalUniqueTeachersByCourseId;
    protected Map<String,Integer> totalStudentSessionsForPeriodByCourseId;
    protected Map<String,Integer> totalTeacherSessionsForPeriodByCourseId;
    protected Map<String,Integer> uniqueLoggedStudentsForPeriodByCourseId;
    protected Map<String,Integer> uniqueLoggedClassesForPeriodByCourseId;
    protected Map<String,Integer> uniqueLoggedTeachersForPeriodByCourseId;
    protected Map<String,Map<Teacher,Integer>> mostLoggedTeachersForPeriodByCourseId;
    protected Map<String,Map<GSClass,Integer>> mostLoggedClassesForPeriodByCourseId;
    
    //For admins, create separate reports for each institution. Store initial queries for re-use.
    protected List<Student> students;
    protected List<GSClass> classes;
    protected List<Teacher> teachers;
    private List<Institution> institutions;
    protected Map<SessionLogEntry, Integer> uniqueAgentCounts;
    private List<SessionLogReportAdminForInstitution> institutionSubReports;
    
    public void init(User user) throws Throwable{
        this.user = user;
        institution = user.getInstitution(); //may be null if user is admin, SessionLogReportsUtil will handle null to provide full admin results 
        initSelectableMonths();
        if( selectableMonths != null && !selectableMonths.isEmpty() ){
            beginDate = selectableMonths.get(0);
            endDate = new Date();
        }
        selectedMonth = beginDate;
        
        initStudents();
        initTeachers();
        initClasses();
        
        initTotalUniqueStudentsByCourseId();
        initTotalUniqueClassesByCourseId();
        initTotalUniqueTeachersByCourseId();

        totalUniqueStudents = students != null ? students.size() : 0;
        totalUniqueClasses = classes != null ? classes.size() : 0;
        totalUniqueTeachers = teachers != null ? teachers.size() : 0;
        
        if( user.getIsAdministrator() ){
            institutions = Institution.getOrderedInstitutions(true); //true for active only
            institutionSubReports = new ArrayList<SessionLogReportAdminForInstitution>();
            if( institutions != null ){
                for( Institution i : institutions ){
                    SessionLogReportAdminForInstitution subReport = new SessionLogReportAdminForInstitution();
                    subReport.init(i, this);
                    institutionSubReports.add(subReport);
                }
            }
        }
    }
    
    public void initInternal() throws Throwable{
        initStudents();
        initTeachers();
        initClasses();
        
        initTotalUniqueStudentsByCourseId();
        initTotalUniqueClassesByCourseId();
        initTotalUniqueTeachersByCourseId();

        totalUniqueStudents = students != null ? students.size() : 0;
        totalUniqueClasses = classes != null ? classes.size() : 0;
        totalUniqueTeachers = teachers != null ? teachers.size() : 0;
        
        institutions = Institution.getOrderedInstitutions(true); //true for active only
        institutionSubReports = new ArrayList<SessionLogReportAdminForInstitution>();
        if( institutions != null ){
            for( Institution i : institutions ){
                SessionLogReportAdminForInstitution subReport = new SessionLogReportAdminForInstitution();
                subReport.init(i, this);
                institutionSubReports.add(subReport);
            }
        }
    }
    
    protected void initStudents() throws Throwable{
        students = SessionLogReportsUtil.getStudents(institution);
    }
    
    protected void initTeachers() throws Throwable{
        teachers = SessionLogReportsUtil.getTeachers(institution);
    }
    
    protected void initClasses() throws Throwable{
        classes = SessionLogReportsUtil.getGSClasses(true, institution);
    }
    
    protected void initUniqueAgentCounts() throws Throwable{
        
    }
    
    protected void initTotalUniqueStudentsByCourseId() throws Throwable{
        totalUniqueStudentsByCourseId = new HashMap<String, Integer>();
        if( students != null ){
            for( Student student : students ){
                Set<String> courseIdsForCurrentStudent = new HashSet<String>();
                Set<GSClass> classes = CollectionLoader.getClasses(student);
                if( classes == null || classes.isEmpty() ){
                    courseIdsForCurrentStudent.add("noClasses");
                }
                else{
                    for( GSClass gsClass : classes ){
                        String courseId = gsClass.getCourseId();
                        courseIdsForCurrentStudent.add(courseId);
                    }
                }
                for( String courseId : courseIdsForCurrentStudent ){
                    Integer studentsCount = totalUniqueStudentsByCourseId.get(courseId);
                    if( studentsCount == null ) studentsCount = 0;
                    totalUniqueStudentsByCourseId.put(courseId, studentsCount+1);
                }
            }
        }
    }
    
    protected void initTotalUniqueClassesByCourseId() throws Throwable{
        courseIdsReportable = new ArrayList<String>();
        totalUniqueClassesByCourseId = new HashMap<String, Integer>();
        if( classes != null ){
            for( GSClass gsClass : classes ){
                String courseId = gsClass.getCourseId();
                Integer classesCount = totalUniqueClassesByCourseId.get(courseId);
                if( classesCount == null ) classesCount = 0;
                totalUniqueClassesByCourseId.put(courseId, classesCount+1);
                if( !courseIdsReportable.contains(courseId) ) courseIdsReportable.add(courseId);
            }
        }
    }
    
    protected void initTotalUniqueTeachersByCourseId() throws Throwable{
        totalUniqueTeachersByCourseId = new HashMap<String, Integer>();
        if( teachers != null ){
            for( Teacher teacher : teachers ){
                Set<String> courseIdsForCurrentTeacher = new HashSet<String>();
                Set<GSClass> classes = CollectionLoader.getClasses(teacher);
                if( classes == null || classes.isEmpty() ){
                    courseIdsForCurrentTeacher.add("noClasses");
                }
                else{
                    for( GSClass gsClass : classes ){
                        String courseId = gsClass.getCourseId();
                        courseIdsForCurrentTeacher.add(courseId);
                    }
                }
                for( String courseId : courseIdsForCurrentTeacher ){
                    Integer teachersCount = totalUniqueTeachersByCourseId.get(courseId);
                    if( teachersCount == null ) teachersCount = 0;
                    totalUniqueTeachersByCourseId.put(courseId, teachersCount+1);
                }
                if( courseIdsForCurrentTeacher.size() > 1 ) log.debug("teaches more than 1 courseId: "+teacher.getFullName());
            }
        }
    }
    
    private void initSelectableMonths() throws Throwable{
        Date earliestSessionLogEntryDate = SessionLogReportsUtil.getEarliestSessionLogEntryDate();
        log.debug("earliestSessionLogEntryDate: "+earliestSessionLogEntryDate);
        if( earliestSessionLogEntryDate != null ){
            selectableMonths = DateUtil.getListOfBeginningsOfCurrentAndPriorMonths(earliestSessionLogEntryDate);
        }
        if( NUM_MONTHS_SELECTABLE != null ){
            if( selectableMonths == null || selectableMonths.size() > NUM_MONTHS_SELECTABLE ){
                selectableMonths = DateUtil.getListOfBeginningsOfCurrentAndPriorMonths(NUM_MONTHS_SELECTABLE);
            }
        }
    }
    
    public void setNewPeriod(Date newBeginDate, Date newEndDate) throws Throwable{
        log.debug("updateForPeriod");
        beginDate = newBeginDate;
        endDate = newEndDate;
        selectedMonth = beginDate;
        uniqueAgentCounts = getUniqueAgentCountsForPeriod(beginDate, endDate, null, null, institution);
    }
    
    public void updatePeriodTotals() throws Throwable{
        log.debug("updatePeriodTotals");
        
        totalStudentSessionsForPeriod = 0;
        totalTeacherSessionsForPeriod = 0;
        uniqueLoggedStudentsForPeriod = 0;
        uniqueLoggedTeachersForPeriod = 0;
        mostLoggedTeachersForPeriod = new LinkedHashMap<Teacher,Integer>();
        mostLoggedClassesForPeriod = new LinkedHashMap<GSClass,Integer>();
        
        //additions for courseId
        totalStudentSessionsForPeriodByCourseId = new HashMap<String,Integer>();
        totalTeacherSessionsForPeriodByCourseId = new HashMap<String,Integer>();
        uniqueLoggedStudentsForPeriodByCourseId = new HashMap<String,Integer>();
        uniqueLoggedClassesForPeriodByCourseId = new HashMap<String,Integer>();
        uniqueLoggedTeachersForPeriodByCourseId = new HashMap<String,Integer>();
        mostLoggedTeachersForPeriodByCourseId = new HashMap<String,Map<Teacher,Integer>>();
        mostLoggedClassesForPeriodByCourseId = new HashMap<String,Map<GSClass,Integer>>();
        //end additions for courseId
        
        Set<GSClass> uniqueClasses = new HashSet<GSClass>();
        log.debug("populating results");
        log.debug("uniqueAgentCounts size="+uniqueAgentCounts.size());
        //Results are ordered by count, desc, so mostLogged is easy to fill
        for( SessionLogEntry sle : uniqueAgentCounts.keySet() ){
            log.debug(sle);
            Integer countForSle = uniqueAgentCounts.get(sle);
            log.debug("count: "+countForSle);
            if( sle.getStudent() != null ){
                log.debug("sle student: "+sle.getStudent());
                uniqueLoggedStudentsForPeriod++;
                totalStudentSessionsForPeriod += countForSle;
                
                //additions for courseId
                if( sle.getGsClass() != null ){
                    String courseId = sle.getGsClass().getCourseId();
                    Integer priorTotalStudentSessionsForCourseId = totalStudentSessionsForPeriodByCourseId.get(courseId);
                    if( priorTotalStudentSessionsForCourseId == null ) priorTotalStudentSessionsForCourseId = 0;
                    totalStudentSessionsForPeriodByCourseId.put(courseId, priorTotalStudentSessionsForCourseId+countForSle);
                    Integer priorUniqueStudentsForCourseId = uniqueLoggedStudentsForPeriodByCourseId.get(courseId);
                    if( priorUniqueStudentsForCourseId == null ) priorUniqueStudentsForCourseId = 0;
                    uniqueLoggedStudentsForPeriodByCourseId.put(courseId, priorUniqueStudentsForCourseId+1);
                }
                //end additions for courseId
            }
            else if( sle.getUser() != null && sle.getUser().getIsTeacher() ){
                Teacher teacher = (Teacher)sle.getUser().getUserType();
                log.debug("sle teacher: "+sle.getUser());
                uniqueLoggedTeachersForPeriod++;
                totalTeacherSessionsForPeriod += countForSle;
                if( mostLoggedTeachersForPeriod.size() < NUM_MOST_LOGGED ){
                    mostLoggedTeachersForPeriod.put(teacher,countForSle);
                }
                
                //additions for courseId
                for( String courseId : teacher.getActiveCourseIds() ){
                    Integer priorTotalTeacherSessionsForCourseId = totalTeacherSessionsForPeriodByCourseId.get(courseId);
                    if( priorTotalTeacherSessionsForCourseId == null ) priorTotalTeacherSessionsForCourseId = 0;
                    totalTeacherSessionsForPeriodByCourseId.put(courseId, priorTotalTeacherSessionsForCourseId+countForSle);
                    Integer priorUniqueTeachersForCourseId = uniqueLoggedTeachersForPeriodByCourseId.get(courseId);
                    if( priorUniqueTeachersForCourseId == null ) priorUniqueTeachersForCourseId = 0;
                    uniqueLoggedTeachersForPeriodByCourseId.put(courseId, priorUniqueTeachersForCourseId+1);
                    
                    Map<Teacher,Integer> mostLoggedTeachersForPeriodForCourse = mostLoggedTeachersForPeriodByCourseId.get(courseId);
                    if( mostLoggedTeachersForPeriodForCourse == null ) mostLoggedTeachersForPeriodForCourse = new LinkedHashMap<Teacher,Integer>();
                    if( mostLoggedTeachersForPeriodForCourse.size() < NUM_MOST_LOGGED ){
                        mostLoggedTeachersForPeriodForCourse.put(teacher,countForSle);
                    }
                    mostLoggedTeachersForPeriodByCourseId.put(courseId, mostLoggedTeachersForPeriodForCourse);
                }
                //end additions for courseId
            }
            if( sle.getGsClass() != null ){
                //Class activity counts can span multiple results, so they must be accumulated.
                //The accumulated counts are stored temporarily in the GSClass object with a custom comparator,
                //the GSClasses and counts can then be popupulated in order in the final map
                log.debug("sle class: "+sle.getGsClass());
                uniqueClasses.add(sle.getGsClass());
                Integer previousCount = sle.getGsClass().getUserActivityCount();
                if( previousCount == null ) previousCount = 0;
                Integer countForClass = previousCount + countForSle;
                sle.getGsClass().setUserActivityCount(countForClass);
                log.debug("accumulation: "+sle.getGsClass().getUserActivityCount()+" for gsClass "+sle.getGsClass());
            }
        }
        //Accumulating counts for class can be out of order, so repopulate in order
        List<GSClass> sortableClasses = new ArrayList<GSClass>(uniqueClasses);
        Collections.sort(sortableClasses, new GSClassUserActivityCountComparator());
        Collections.reverse(sortableClasses);
        for( GSClass gsClass : sortableClasses ){
            log.debug("gsClass.getUserActivityCount(): "+gsClass.getUserActivityCount());
            if( mostLoggedClassesForPeriod.size() < NUM_MOST_LOGGED ){
                mostLoggedClassesForPeriod.put(gsClass, gsClass.getUserActivityCount());
            }
            //additions for courseId
            String courseId = gsClass.getCourseId();
            Map<GSClass,Integer> mostLoggedClassesForPeriodForCourse = mostLoggedClassesForPeriodByCourseId.get(courseId);
            if( mostLoggedClassesForPeriodForCourse == null ) mostLoggedClassesForPeriodForCourse = new LinkedHashMap<GSClass,Integer>();
            if( mostLoggedClassesForPeriodForCourse.size() < NUM_MOST_LOGGED ){
                mostLoggedClassesForPeriodForCourse.put(gsClass, gsClass.getUserActivityCount());
            }
            mostLoggedClassesForPeriodByCourseId.put(courseId, mostLoggedClassesForPeriodForCourse);
            //end additions for courseId
            //Clear the total in the class so it doesn't carry over to admin subReport
            gsClass.setUserActivityCount(0);
        }
        
        uniqueLoggedClassesForPeriod = uniqueClasses.size();
        //additions for courseId
        for( GSClass gsClass : uniqueClasses ){
            String courseId = gsClass.getCourseId();
            Integer uniqueClassesForCourseId = uniqueLoggedClassesForPeriodByCourseId.get(courseId);
            if( uniqueClassesForCourseId == null ) uniqueClassesForCourseId = 0;
            uniqueLoggedClassesForPeriodByCourseId.put(courseId, uniqueClassesForCourseId+1);
        }
        //end additions for courseId
        
        //Filling in the un-maxed maps with blanks makes JSP presentation easier.
        //Since this is a tagmodel, strictly prepping for view, it's reasonable to do this here.
        //Hashcodes are based on id, so use a nearly impossible value to fill maps.
        Long unconflictedId = Long.MAX_VALUE;
        while(mostLoggedTeachersForPeriod.size() < NUM_MOST_LOGGED){
            Teacher blank = new Teacher();
            blank.setUserId(unconflictedId);
            mostLoggedTeachersForPeriod.put(blank, null);
            unconflictedId--;
        }
        //additions for courseId
        if( mostLoggedTeachersForPeriodByCourseId.isEmpty() ){
            for( String courseId : courseIdsReportable ){
                mostLoggedTeachersForPeriodByCourseId.put(courseId, new HashMap<Teacher,Integer>());
            }
        }
        for( Map<Teacher,Integer> mostLoggedTeachersForPeriodForCourseId : mostLoggedTeachersForPeriodByCourseId.values() ){
            unconflictedId = Long.MAX_VALUE;
            while(mostLoggedTeachersForPeriodForCourseId.size() < NUM_MOST_LOGGED){
                Teacher blank = new Teacher();
                blank.setUserId(unconflictedId);
                mostLoggedTeachersForPeriodForCourseId.put(blank, null);
                unconflictedId--;
            }
        }
        //end additions for courseId
        unconflictedId = Long.MAX_VALUE;
        while(mostLoggedClassesForPeriod.size() < NUM_MOST_LOGGED){
            GSClass blank = new GSClass();
            blank.setClassId(unconflictedId);
            mostLoggedClassesForPeriod.put(blank, null);
            unconflictedId--;
        }
        //additions for courseId
        if( mostLoggedClassesForPeriodByCourseId.isEmpty() ){
            for( String courseId : courseIdsReportable ){
                mostLoggedClassesForPeriodByCourseId.put(courseId, new HashMap<GSClass,Integer>());
            }
        }
        for( Map<GSClass,Integer> mostLoggedClassesForPeriodForCourseId : mostLoggedClassesForPeriodByCourseId.values() ){
            unconflictedId = Long.MAX_VALUE;
            while(mostLoggedClassesForPeriodForCourseId.size() < NUM_MOST_LOGGED){
                GSClass blank = new GSClass();
                blank.setClassId(unconflictedId);
                mostLoggedClassesForPeriodForCourseId.put(blank, null);
                unconflictedId--;
            }
        }
        //end additions for courseId
        
        if( institutionSubReports != null ){
            for( SessionLogReportAdminForInstitution subReport : institutionSubReports ){
                subReport.setNewPeriod();
                subReport.updatePeriodTotals();
                subReport.debug();
            }
        }
    }
    
    public void debug(){
        if( !log.isDebugEnabled() ) return;
        log.debug("institution: "+institution);
        log.debug("beginDate: "+beginDate);
        log.debug("endDate: "+endDate);
        log.debug("selectableMonths: "+selectableMonths);
        log.debug("selectedMonth: "+selectedMonth);
        log.debug("students size="+students.size());
        log.debug("teachers size="+teachers.size());
        log.debug("classes size="+classes.size());
        log.debug("unqiueAgentCounts size="+uniqueAgentCounts.size());
        log.debug("totalStudentSessionsForPeriod: "+totalStudentSessionsForPeriod);
        log.debug("totalTeacherSessionsForPeriod: "+totalTeacherSessionsForPeriod);
        log.debug("uniqueLoggedStudentsForPeriod: "+uniqueLoggedStudentsForPeriod);
        log.debug("uniqueLoggedClassesForPeriod: "+uniqueLoggedClassesForPeriod);
        log.debug("uniqueLoggedTeachersForPeriod: "+uniqueLoggedTeachersForPeriod);
        log.debug("totalUniqueStudents: "+totalUniqueStudents);
        log.debug("totalUniqueClasses: "+totalUniqueClasses);
        log.debug("totalUniqueTeachers: "+totalUniqueTeachers);
        if( mostLoggedTeachersForPeriod != null ){
            log.debug("mostLoggedTeachersForPeriod: ");
            for( Teacher t : mostLoggedTeachersForPeriod.keySet() ){
                log.debug("teacher: "+t.getUserId()+", count="+mostLoggedTeachersForPeriod.get(t));
            }
        }
        else{
            log.debug("mostLoggedTeachersForPeriod: null");
        }
        if( mostLoggedClassesForPeriod != null ){
            log.debug("mostLoggedClassesForPeriod: ");
            for( GSClass c : mostLoggedClassesForPeriod.keySet() ){
                log.debug("class: "+c.getClassId()+", count="+mostLoggedClassesForPeriod.get(c));
            }
        }
        else{
            log.debug("mostLoggedClassesForPeriod: null");
        }
        //additions for courseId
        log.debug("courseIdsReportable: "+courseIdsReportable);
        log.debug("totalUniqueStudentsByCourseId: "+totalUniqueStudentsByCourseId);
        log.debug("totalUniqueClassesByCourseId: "+totalUniqueClassesByCourseId);
        log.debug("totalUniqueTeachersByCourseId: "+totalUniqueTeachersByCourseId);
        log.debug("totalStudentSessionsForPeriodByCourseId: "+totalStudentSessionsForPeriodByCourseId);
        log.debug("totalTeacherSessionsForPeriodByCourseId: "+totalTeacherSessionsForPeriodByCourseId);
        log.debug("uniqueLoggedStudentsForPeriodByCourseId: "+uniqueLoggedStudentsForPeriodByCourseId);
        log.debug("uniqueLoggedClassesForPeriodByCourseId: "+uniqueLoggedClassesForPeriodByCourseId);
        log.debug("uniqueLoggedTeachersForPeriodByCourseId: "+uniqueLoggedTeachersForPeriodByCourseId);
        log.debug("mostLoggedTeachersForPeriodByCourseId: "+mostLoggedTeachersForPeriodByCourseId);
        log.debug("mostLoggedClassesForPeriodByCourseId: "+mostLoggedClassesForPeriodByCourseId);
        //end additions for courseId
    }

    public List<Date> getSelectableMonths() {
        return selectableMonths;
    }

    public Date getSelectedMonth() {
        return selectedMonth;
    }

    public Integer getTotalStudentSessionsForPeriod() {
        return totalStudentSessionsForPeriod;
    }

    public Integer getTotalTeacherSessionsForPeriod() {
        return totalTeacherSessionsForPeriod;
    }

    public Integer getUniqueLoggedStudentsForPeriod() {
        return uniqueLoggedStudentsForPeriod;
    }

    public Integer getUniqueLoggedClassesForPeriod() {
        return uniqueLoggedClassesForPeriod;
    }

    public Integer getUniqueLoggedTeachersForPeriod() {
        return uniqueLoggedTeachersForPeriod;
    }

    public Integer getTotalUniqueStudents() {
        return totalUniqueStudents;
    }

    public Integer getTotalUniqueClasses() {
        return totalUniqueClasses;
    }

    public Integer getTotalUniqueTeachers() {
        return totalUniqueTeachers;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public Map<Teacher, Integer> getMostLoggedTeachersForPeriod() {
        return mostLoggedTeachersForPeriod;
    }

    public Map<GSClass, Integer> getMostLoggedClassesForPeriod() {
        return mostLoggedClassesForPeriod;
    }

    public static Integer getNUM_MOST_LOGGED() {
        return NUM_MOST_LOGGED;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<String> getCourseIdsReportable() {
        return courseIdsReportable;
    }

    public Map<String, Integer> getTotalUniqueStudentsByCourseId() {
        return totalUniqueStudentsByCourseId;
    }

    public Map<String, Integer> getTotalUniqueClassesByCourseId() {
        return totalUniqueClassesByCourseId;
    }

    public Map<String, Integer> getTotalUniqueTeachersByCourseId() {
        return totalUniqueTeachersByCourseId;
    }

    public Map<String, Integer> getTotalStudentSessionsForPeriodByCourseId() {
        return totalStudentSessionsForPeriodByCourseId;
    }

    public Map<String, Integer> getTotalTeacherSessionsForPeriodByCourseId() {
        return totalTeacherSessionsForPeriodByCourseId;
    }

    public Map<String, Integer> getUniqueLoggedStudentsForPeriodByCourseId() {
        return uniqueLoggedStudentsForPeriodByCourseId;
    }

    public Map<String, Integer> getUniqueLoggedClassesForPeriodByCourseId() {
        return uniqueLoggedClassesForPeriodByCourseId;
    }

    public Map<String, Integer> getUniqueLoggedTeachersForPeriodByCourseId() {
        return uniqueLoggedTeachersForPeriodByCourseId;
    }

    public Map<String, Map<Teacher, Integer>> getMostLoggedTeachersForPeriodByCourseId() {
        return mostLoggedTeachersForPeriodByCourseId;
    }

    public Map<String, Map<GSClass, Integer>> getMostLoggedClassesForPeriodByCourseId() {
        return mostLoggedClassesForPeriodByCourseId;
    }

    public Institution getInstitution() {
        return institution;
    }

    public List<Student> getStudents() {
        return students;
    }

    public List<GSClass> getClasses() {
        return classes;
    }

    public List<Teacher> getTeachers() {
        return teachers;
    }

    public List<Institution> getInstitutions() {
        return institutions;
    }

    public Map<SessionLogEntry, Integer> getUniqueAgentCounts() {
        return uniqueAgentCounts;
    }

    public List<SessionLogReportAdminForInstitution> getInstitutionSubReports() {
        return institutionSubReports;
    }

}
