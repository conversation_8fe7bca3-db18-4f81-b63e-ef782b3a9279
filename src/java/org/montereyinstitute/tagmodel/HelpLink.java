package org.montereyinstitute.tagmodel;

import gs.hinkleworld.core.SessionUtil;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.montereyinstitute.tagmodel.ViewSettings.CourseViewContext;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class HelpLink {
    
    private static final Map<String,String> urlMapForDevMath = new HashMap<String,String>(); //key=ServletPath, value=url
    static{
        urlMapForDevMath.put("/class", "https://support.nrocnetwork.org/hc/en-us/articles/202047253");
        urlMapForDevMath.put("/course_settings", "https://support.nrocnetwork.org/hc/en-us/articles/202047263");
        urlMapForDevMath.put("/topics", "https://support.nrocnetwork.org/hc/en-us/articles/202485688");
        urlMapForDevMath.put("/teacher", "https://support.nrocnetwork.org/hc/en-us/articles/202486388");
        urlMapForDevMath.put("/create_teacher", "https://support.nrocnetwork.org/hc/en-us/articles/202486388");
        urlMapForDevMath.put("/edit_teacher", "https://support.nrocnetwork.org/hc/en-us/articles/202486388");
        urlMapForDevMath.put("/student", "https://support.nrocnetwork.org/hc/en-us/articles/203101127");
        urlMapForDevMath.put("/report", "https://support.nrocnetwork.org/hc/en-us/articles/202047273");
        urlMapForDevMath.put("/activity", "https://support.nrocnetwork.org/hc/en-us/articles/202086456");
        urlMapForDevMath.put("/my_settings", "https://support.nrocnetwork.org/hc/en-us/articles/202492208");
    }

    private static final Map<String,String> urlMapForDevEnglish = new HashMap<String,String>(); //key=ServletPath, value=url
    static{
        urlMapForDevEnglish.put("/class", "https://support.nrocnetwork.org/hc/en-us/articles/204033856");
        urlMapForDevEnglish.put("/course_settings", "https://support.nrocnetwork.org/hc/en-us/articles/204033846");
        urlMapForDevEnglish.put("/units", "https://support.nrocnetwork.org/hc/en-us/articles/204333603-Unit-Content");
        urlMapForDevEnglish.put("/teacher", "https://support.nrocnetwork.org/hc/en-us/articles/204033866");
        urlMapForDevEnglish.put("/create_teacher", "https://support.nrocnetwork.org/hc/en-us/articles/202486388");
        urlMapForDevEnglish.put("/edit_teacher", "https://support.nrocnetwork.org/hc/en-us/articles/202486388");
        urlMapForDevEnglish.put("/student", "https://support.nrocnetwork.org/hc/en-us/articles/204033906");
        urlMapForDevEnglish.put("/report", "https://support.nrocnetwork.org/hc/en-us/articles/204333623");
        urlMapForDevEnglish.put("/activity", "https://support.nrocnetwork.org/hc/en-us/articles/202086456");
        urlMapForDevEnglish.put("/my_settings", "https://support.nrocnetwork.org/hc/en-us/articles/204333583");
        urlMapForDevEnglish.put("/reviews", "https://support.nrocnetwork.org/hc/en-us/articles/203993418");
    }

    private String currentHelpUrl;

    private HelpLink(){}

    public static HelpLink getHelpLinkUseSession(HttpServletRequest request){
        HelpLink helpLink = (HelpLink) SessionUtil.getSessionSingleton(HelpLink.class, request);
        if( helpLink == null ){
            helpLink = new HelpLink();
        }
        SessionUtil.setSessionSingleton(helpLink, request);
        return helpLink;
    }
    
    public String getCurrentHelpUrl() {
        return currentHelpUrl;
    }

    public void setCurrentHelpUrl(HttpServletRequest request, ViewSettings vs) {
        Map<String,String>  urlMap = getUrlMapForCourseViewContext(vs.getCurrentCourseViewContext());
        for( String key : urlMap.keySet() ){
            if( request.getServletPath().startsWith(key) ){
                currentHelpUrl = urlMap.get(key);
                return;
            }
        }
        currentHelpUrl = null;
    }
 
    private Map<String,String> getUrlMapForCourseViewContext(CourseViewContext context){
        switch(context){
            case devEnglish: return urlMapForDevEnglish;
            case devMath: return urlMapForDevMath;
        }
        return null;
    }
    
}
