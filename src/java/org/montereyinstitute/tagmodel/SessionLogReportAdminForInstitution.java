package org.montereyinstitute.tagmodel;

import java.util.*;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import static org.montereyinstitute.util.SessionLogReportsUtil.getUniqueAgentCountsForPeriod;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class SessionLogReportAdminForInstitution extends SessionLogReport {
    private static Logger log = LogManager.getLogger(SessionLogReportAdminForInstitution.class);
    
    SessionLogReport parentReport;
    
    protected SessionLogReportAdminForInstitution(){}
    
    protected void init(Institution institution, SessionLogReport parentReport) throws Throwable{
        this.institution = institution;
        this.parentReport = parentReport;
        selectableMonths = parentReport.getSelectableMonths();
        selectedMonth = parentReport.getSelectedMonth();
        beginDate = parentReport.getBeginDate();
        endDate = parentReport.getEndDate();
        
        initStudents();
        initTeachers();
        initClasses();
        initUniqueAgentCounts();
        
        initTotalUniqueStudentsByCourseId();
        initTotalUniqueClassesByCourseId();
        initTotalUniqueTeachersByCourseId();

        totalUniqueStudents = students != null ? students.size() : 0;
        totalUniqueClasses = classes != null ? classes.size() : 0;
        totalUniqueTeachers = teachers != null ? teachers.size() : 0;
    }
    
    @Override
    protected void initStudents() throws Throwable{
        students = new ArrayList<Student>();
        if( parentReport.getStudents() != null ){
            studentLoop: for( Student student : parentReport.getStudents() ){
                Set<GSClass> studentClasses = CollectionLoader.getClasses(student);
                if( studentClasses != null ){
                    for( GSClass gsClass : studentClasses ){
                        if( gsClass.getInstitution() != null && gsClass.getInstitution().equals(institution) ){
                            students.add(student);
                            continue studentLoop;
                        }
                    }
                }
            }
        }
    }
    
    @Override
    protected void initTeachers() throws Throwable{
        teachers = new ArrayList<Teacher>();
        if( parentReport.getTeachers() != null ){
            for( Teacher teacher : parentReport.getTeachers() ){
                if( teacher.getInstitution() != null && teacher.getInstitution().equals(institution) ){
                    teachers.add(teacher);
                }
            }
        }
    }
    
    @Override
    protected void initClasses() throws Throwable{
        classes = new ArrayList<GSClass>();
        if( parentReport.getClasses()!= null ){
            for( GSClass gsClass : parentReport.getClasses() ){
                if( gsClass.getInstitution() != null && gsClass.getInstitution().equals(institution) ){
                    classes.add(gsClass);
                }
            }
        }
    }
    
    @Override
    protected void initUniqueAgentCounts() throws Throwable{
        uniqueAgentCounts = new LinkedHashMap<SessionLogEntry,Integer>();
        if( parentReport.getUniqueAgentCounts() != null ){
            for( SessionLogEntry sle : parentReport.getUniqueAgentCounts().keySet() ){
                if( sle.getInstitution() != null && sle.getInstitution().equals(institution) ){
                    uniqueAgentCounts.put(sle, parentReport.getUniqueAgentCounts().get(sle));
                }
            }
        }
    }
    
    protected void setNewPeriod() throws Throwable{
        log.debug("setNewPeriod");
        beginDate = parentReport.getBeginDate();
        endDate = parentReport.getEndDate();
        selectedMonth = parentReport.getSelectedMonth();
        initUniqueAgentCounts();
    }

}
