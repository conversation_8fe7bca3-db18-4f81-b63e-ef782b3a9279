package org.montereyinstitute.tagmodel;

import org.apache.logging.log4j.*;

/**
 * <AUTHOR>
 */
public class SessionLogReportV2_Row_Summary extends SessionLogReportV2_Row_Base implements Comparable<SessionLogReportV2_Row_Summary> {
    private static final Logger log = LogManager.getLogger(SessionLogReportV2_Row_Summary.class);
    
    private Integer studentSessions;
    private Integer activeStudents;
    private Integer newStudents;
    private Integer registeredStudents;
    private Integer instructorSessions;
    private Integer activeInstructors;
    private Integer registeredInstructors;
    private Integer activeClasses;
    private Integer registeredClasses;

    public Integer getStudentSessions() {
        if( studentSessions == null ) return 0;
        return studentSessions;
    }

    public void setStudentSessions(Integer studentSessions) {
        this.studentSessions = studentSessions;
    }

    public Integer getActiveStudents() {
        if( activeStudents == null ) return 0;
        return activeStudents;
    }

    public void setActiveStudents(Integer activeStudents) {
        this.activeStudents = activeStudents;
    }

    public Integer getRegisteredStudents() {
        if( registeredStudents == null ) return 0;
        return registeredStudents;
    }

    public void setRegisteredStudents(Integer registeredStudents) {
        this.registeredStudents = registeredStudents;
    }

    public Integer getInstructorSessions() {
        if( instructorSessions == null ) return 0;
        return instructorSessions;
    }

    public void setInstructorSessions(Integer instructorSessions) {
        this.instructorSessions = instructorSessions;
    }

    public Integer getActiveInstructors() {
        if( activeInstructors == null ) return 0;
        return activeInstructors;
    }

    public void setActiveInstructors(Integer activeInstructors) {
        this.activeInstructors = activeInstructors;
    }

    public Integer getRegisteredInstructors() {
        if( registeredInstructors == null ) return 0;
        return registeredInstructors;
    }

    public void setRegisteredInstructors(Integer registeredInstructors) {
        this.registeredInstructors = registeredInstructors;
    }

    public Integer getActiveClasses() {
        if( activeClasses == null ) return 0;
        return activeClasses;
    }

    public void setActiveClasses(Integer activeClasses) {
        this.activeClasses = activeClasses;
    }

    public Integer getRegisteredClasses() {
        if( registeredClasses == null ) return 0;
        return registeredClasses;
    }

    public void setRegisteredClasses(Integer registeredClasses) {
        this.registeredClasses = registeredClasses;
    }

    public Integer getNewStudents() {
        if( newStudents == null ) return 0;
        return newStudents;
    }

    public void setNewStudents(Integer newStudents) {
        this.newStudents = newStudents;
    }

    @Override
    public int compareTo(SessionLogReportV2_Row_Summary other) {
        if( !this.getInstitutionName_nonNull().equals(other.getInstitutionName_nonNull()) ){
            return this.getInstitutionName_nonNull().compareTo(other.getInstitutionName_nonNull());
        }
        return this.getCourseIdCompareValue().compareTo(other.getCourseIdCompareValue());
    }

}
