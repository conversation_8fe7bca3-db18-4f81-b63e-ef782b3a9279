package org.montereyinstitute.tagmodel;

import gs.hinkleworld.core.AppUtil;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode;
import org.montereyinstitute.model.GSClass;
import org.montereyinstitute.model.Student;
import org.montereyinstitute.model.TopicStateForStudentClass;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class ModalDialogForClassSummaryGraphicalReport extends Dialog {
    private static Logger log = LogManager.getLogger(ModalDialogForClassSummaryGraphicalReport.class);
    
    private GSClass gsClass;
    private Student student;
    private CourseNode.Course course;
    private CourseNode.Topic topic;
    private CourseNode.Lesson lesson;
    private CourseNode.Unit unit;
    private TopicStateForStudentClass topicState;

    @Override
    public void init(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        if( log.isDebugEnabled() ) AppUtil.logRequestParams(request);
        gsClass = GSClass.getGSClassUseSession(request);
        student = Student.getStudentUseSession(request, gsClass);
        course = CourseNode.Course.getCourse(request, gsClass.getCourseId());
        topic = (CourseNode.Topic) course.getDescendantByTypeAndId(CourseNode.Topic.class, request.getParameter(CourseNode.Topic.ID_PARAM));
        lesson = (CourseNode.Lesson) topic.getParent();
        unit = (CourseNode.Unit) lesson.getParent();
        topicState = TopicStateForStudentClass.getFromDb(student, gsClass, unit.getNumber(), lesson.getNumber(), topic.getNumber());
        topicState.initAppCourseNodes(course);
        if( topicState == null ) log.debug("topicState null");
        else log.debug("topicState id="+topicState.getId());
        topicState.getReviewScore(); //triggers lazy loading
        topicState.getPreassessmentScore(); //triggers lazy loading
        request.setAttribute("TopicState", topicState);
        destination = "/WEB-INF/jsp/dialog/topicReviewScore.jsp";
    }
    
}
