package org.montereyinstitute.tagmodel;

import org.montereyinstitute.util.SessionLogReportsUtil;
import com.hinkleworld.lib.DateUtil;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import org.apache.logging.log4j.*;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.Course;
import static org.montereyinstitute.util.SessionLogReportsUtil.getUniqueAgentCountsForPeriod;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class SessionLogReportV2 {
    private static final Logger log = LogManager.getLogger(SessionLogReportV2.class);
    public static final DateFormat dateFormat = new SimpleDateFormat("MMMM yyyy");
    
    public static final Integer NUM_MONTHS_SELECTABLE = 12; //set to null to ignore
    public static final Integer ADMIN_LIMIT_INSTITUTIONS = null; //use for testing quick run. Set to null for production.
//    public static final Integer ADMIN_LIMIT_INSTITUTIONS = 4; //use for testing quick run. Set to null for production.
    
    private User user;
    protected Institution institution;
    protected Date beginDate; //null for forever
    protected Date endDate; //null for forever
    protected List<Date> selectableMonths;
    protected Date selectedMonth; //null for forever
    
    //Adding division of reporting by courseId, e.g. devEnglish, devMath (see constants Course.ID_DEV_MATH, etc.)
    protected Set<String> courseIdsReportable;
    //Key=courseId
    protected Map<String,Integer> totalUniqueStudentsByCourseId; // A-3
    protected Map<String,Integer> totalUniqueClassesByCourseId; // A-8
    protected Map<String,Integer> totalUniqueTeachersByCourseId; // A-6
    protected Map<String,Integer> totalStudentSessionsForPeriodByCourseId; // A-1
    protected Map<String,Integer> totalTeacherSessionsForPeriodByCourseId; // A-4
    protected Map<String,Integer> uniqueLoggedStudentsForPeriodByCourseId; // A-2
    protected Map<String,Integer> uniqueLoggedClassesForPeriodByCourseId; // A-7 -- moved to reflect student activity
    protected Map<String,Integer> uniqueLoggedTeachersForPeriodByCourseId; // A-5
    protected Map<String,Integer> newStudentsByCourseId;
    
    //Additions for V2
    protected Set<GSClass> gsClassesReportable;
    protected Map<GSClass,Integer> totalStudentSessionsForPeriodByClass; //B-1
    protected Map<GSClass,Integer> uniqueLoggedStudentsForPeriodByClass; //B-2
    protected Map<GSClass,Integer> totalUniqueStudentsByClass; //B-3 (does not include deactivated students)
    
    protected Set<Teacher> teachersReportable;
    //first key=courseId
    protected Map<String,Map<Teacher,Integer>> totalTeacherSessionsForPeriodByCourseIdByTeacher; //C-1
    protected Map<String,Map<Teacher,Integer>> uniqueLoggedClassesForPeriodByCourseIdByTeacher; //C-2
    protected Map<String,Map<Teacher,Integer>> totalUniqueClassesByCourseIdByTeacher; //C-3
    
    protected List<SessionLogReportV2_Row_Summary> summaryTableRows;
    protected List<SessionLogReportV2_Row_Classes> classesTableRows;
    protected List<SessionLogReportV2_Row_Teachers> teachersTableRows;
    //End Additions for V2
    
    protected List<Student> students;
    protected List<GSClass> classes;
    protected List<Teacher> teachers;
    protected Map<SessionLogEntry, Integer> uniqueAgentCounts;
    
    //For admins, create separate reports for each institution. Store initial queries for re-use.
    List<SessionLogReportV2> adminReports;

    private StudentStateForClassBatch sscBatch;
    
    public SessionLogReportV2(){}
    public SessionLogReportV2(StudentStateForClassBatch sscBatch){
        this.sscBatch = sscBatch;
    }
    
    public void init(User user) throws Throwable{
        if( user.getIsAdministrator() ){
            initAdminAllInstitutions();
        }
        else if( user.getIsInstitution() ){
            initSingleInstitution(user.getInstitution(), null);
        }
        else{
            throw new Exception("invalid user: "+user);
        }
    }
    
    public void initAdminAllInstitutions() throws Throwable{
        log.debug("initAdminAllInstitutions");
        if( sscBatch == null ){
            sscBatch = new StudentStateForClassBatch();
            sscBatch.initFullTableBatch();
        }
        
        summaryTableRows = new ArrayList<SessionLogReportV2_Row_Summary>();
        classesTableRows = new ArrayList<SessionLogReportV2_Row_Classes>();
        teachersTableRows = new ArrayList<SessionLogReportV2_Row_Teachers>();
        adminReports = new ArrayList<SessionLogReportV2>();

        initSelectableMonths();
        if( selectableMonths != null && !selectableMonths.isEmpty() ){
            beginDate = selectableMonths.get(0);
            endDate = new Date();
        }
        selectedMonth = beginDate;

        List<Institution> institutions = CollectionLoader.getInstitutions();
        if( institutions != null ){
            int count = 0;
            for( Institution curInstitution : institutions ){
                count ++;
                SessionLogReportV2 report = new SessionLogReportV2(sscBatch);
                report.initSingleInstitution(curInstitution, selectableMonths);
                adminReports.add(report);
                if( ADMIN_LIMIT_INSTITUTIONS != null && count >= ADMIN_LIMIT_INSTITUTIONS ) break;
            }
        }
    }
    
    private void initSingleInstitution(Institution institution, List<Date> selectableMonthsFromAdmin) throws Throwable{
        log.debug("initSingleInstitution: "+institution.getInstitutionName());
        this.institution = institution;
        this.user = institution.getUser();
        if( sscBatch == null ){
            sscBatch = new StudentStateForClassBatch();
            sscBatch.initBatchByInstitution(institution);
        }
        
        if( selectableMonthsFromAdmin != null ){
            selectableMonths = selectableMonthsFromAdmin;
        }
        else{
            initSelectableMonths();
        }
        if( selectableMonths != null && !selectableMonths.isEmpty() ){
            beginDate = selectableMonths.get(0);
            endDate = new Date();
        }
        selectedMonth = beginDate;
        
        initStudents();
        initTeachers();
        initClasses();
        
        initCourseIdsReportable();
        initTotalUniqueStudentsByCourseId();
        initTotalUniqueClassesByCourseId();
        initTotalUniqueTeachersByCourseId();

    }
    
    private void initCourseIdsReportable(){
        courseIdsReportable = new HashSet<String>();
        courseIdsReportable.add(Course.ID_DEV_ENGLISH);
        courseIdsReportable.add(Course.ID_DEV_MATH);
        courseIdsReportable.add(Course.ID_DEV_MATH_2);
    }
    
    private void initStudents() throws Throwable{
        students = SessionLogReportsUtil.getStudents(institution);
    }
    
    private void initTeachers() throws Throwable{
        teachers = SessionLogReportsUtil.getTeachers(institution);
    }
    
    private void initClasses() throws Throwable{
        classes = SessionLogReportsUtil.getGSClasses(true, institution);
    }
    
    private void initTotalUniqueStudentsByCourseId() throws Throwable{
        totalUniqueStudentsByCourseId = new HashMap<String, Integer>();
        newStudentsByCourseId = new HashMap<String, Integer>();
        totalUniqueStudentsByClass = new HashMap<GSClass, Integer>();
        if( students != null ){
            for( Student student : students ){
                Set<GSClass> classes = CollectionLoader.getClasses(student);

                for( GSClass gsClass : classes ){
                    if( !gsClass.getActive() ) continue;
//                    if( !student.getActive(gsClass) ) continue;
                    if( !sscBatch.getActive(gsClass, student) ) continue;
                    Integer studentsCountByClass = totalUniqueStudentsByClass.get(gsClass);
                    if( studentsCountByClass == null ) studentsCountByClass = 0;
                    totalUniqueStudentsByClass.put(gsClass, studentsCountByClass+1);
                    
                    String courseId = gsClass.getCourseId();
                    Integer studentsCountByCourseId = totalUniqueStudentsByCourseId.get(courseId);
                    if( studentsCountByCourseId == null ) studentsCountByCourseId = 0;
                    totalUniqueStudentsByCourseId.put(courseId, studentsCountByCourseId+1);

                    Integer newStudentsCount = newStudentsByCourseId.get(courseId);
                    if( newStudentsCount == null ) newStudentsCount = 0;
                    if( student.isCreatedWithinPeriod(beginDate, endDate)) newStudentsCount++;
                    newStudentsByCourseId.put(courseId, newStudentsCount);

                }

            }
        }
    }
    
    private void initTotalUniqueClassesByCourseId() throws Throwable{
        totalUniqueClassesByCourseId = new HashMap<String, Integer>();
        totalUniqueClassesByCourseIdByTeacher = new HashMap<String,Map<Teacher,Integer>>();
        gsClassesReportable = new HashSet<GSClass>();
        if( classes != null ){
            for( GSClass gsClass : classes ){
                if( !gsClass.getActive() ) continue;
                gsClassesReportable.add(gsClass);
                //Count by courseId
                String courseId = gsClass.getCourseId();
                Integer classesCount = totalUniqueClassesByCourseId.get(courseId);
                if( classesCount == null ) classesCount = 0;
                totalUniqueClassesByCourseId.put(courseId, classesCount+1);
                //Count by teacher
                Teacher teacher = gsClass.getTeacher();
                if( teacher != null ){
                    Map<Teacher,Integer> innerCountMapByTeacher = totalUniqueClassesByCourseIdByTeacher.get(gsClass.getCourseId());
                    if( innerCountMapByTeacher == null ){
                        innerCountMapByTeacher = new HashMap<Teacher,Integer>();
                        totalUniqueClassesByCourseIdByTeacher.put(gsClass.getCourseId(), innerCountMapByTeacher);
                    }
                    Integer  priorClassesCountByTeacher = innerCountMapByTeacher.get(teacher);
                    if( priorClassesCountByTeacher == null ) priorClassesCountByTeacher = 0;
                    innerCountMapByTeacher.put(teacher, priorClassesCountByTeacher+1);
                }
            }
        }
    }
    
    private void initTotalUniqueTeachersByCourseId() throws Throwable{
        totalUniqueTeachersByCourseId = new HashMap<String, Integer>();
        teachersReportable = new HashSet<Teacher>();
        if( teachers != null ){
            for( Teacher teacher : teachers ){
                teachersReportable.add(teacher);
                Set<String> courseIdsForCurrentTeacher = new HashSet<String>();
                Set<GSClass> classes = CollectionLoader.getClasses(teacher);
                if( classes == null || classes.isEmpty() ){
                    courseIdsForCurrentTeacher.add("noClasses");
                }
                else{
                    for( GSClass gsClass : classes ){
                        String courseId = gsClass.getCourseId();
                        courseIdsForCurrentTeacher.add(courseId);
                    }
                }
                for( String courseId : courseIdsForCurrentTeacher ){
                    Integer teachersCount = totalUniqueTeachersByCourseId.get(courseId);
                    if( teachersCount == null ) teachersCount = 0;
                    totalUniqueTeachersByCourseId.put(courseId, teachersCount+1);
                }
                if( courseIdsForCurrentTeacher.size() > 1 ) log.debug("teaches more than 1 courseId: "+teacher.getFullName());
            }
        }
    }
    
    private void initSummaryTableRows() {
        summaryTableRows = new ArrayList<SessionLogReportV2_Row_Summary>();
        for( String courseId : courseIdsReportable ){
            SessionLogReportV2_Row_Summary row = new SessionLogReportV2_Row_Summary();
            row.setInstitution(institution);
            if( beginDate == null ){
                row.setPeriod("All Time");
            }
            else{
                row.setPeriod(dateFormat.format(beginDate));
            }
            row.setCourseId(courseId);
            row.setStudentSessions(totalStudentSessionsForPeriodByCourseId.get(courseId));
            row.setActiveStudents(uniqueLoggedStudentsForPeriodByCourseId.get(courseId));
            row.setNewStudents(newStudentsByCourseId.get(courseId));
            row.setRegisteredStudents(totalUniqueStudentsByCourseId.get(courseId));
            row.setInstructorSessions(totalTeacherSessionsForPeriodByCourseId.get(courseId));
            row.setActiveInstructors(uniqueLoggedTeachersForPeriodByCourseId.get(courseId));
            row.setRegisteredInstructors(totalUniqueTeachersByCourseId.get(courseId));
            row.setActiveClasses(uniqueLoggedClassesForPeriodByCourseId.get(courseId));
            row.setRegisteredClasses(totalUniqueClassesByCourseId.get(courseId));
            summaryTableRows.add(row);
        }
        Collections.sort(summaryTableRows);
    }

    private void initClassesTableRows() {
        classesTableRows = new ArrayList<SessionLogReportV2_Row_Classes>();
        for( GSClass gsClass : gsClassesReportable ){
            SessionLogReportV2_Row_Classes row = new SessionLogReportV2_Row_Classes();
            row.setInstitution(institution);
            if( beginDate == null ){
                row.setPeriod("All Time");
            }
            else{
                row.setPeriod(dateFormat.format(beginDate));
            }
            row.setCourseId(gsClass.getCourseId());
            row.setGsClass(gsClass);
            row.setSessions(totalStudentSessionsForPeriodByClass.get(gsClass));
            row.setActiveStudents(uniqueLoggedStudentsForPeriodByClass.get(gsClass));
            row.setEnrolledStudents(totalUniqueStudentsByClass.get(gsClass));
            classesTableRows.add(row);
        }
        Collections.sort(classesTableRows);
    }

    private void initTeachersTableRows() {
        log.debug("initTeachersTableRows");
        teachersTableRows = new ArrayList<SessionLogReportV2_Row_Teachers>();
        Set<Teacher> reportedTeachers = new HashSet<Teacher>();
        for( String courseId : courseIdsReportable ){
            Map<Teacher,Integer> sessionsMap = totalTeacherSessionsForPeriodByCourseIdByTeacher.get(courseId);
            Map<Teacher,Integer> activeClassesMap = uniqueLoggedClassesForPeriodByCourseIdByTeacher.get(courseId);
            Map<Teacher,Integer> registeredClassesMap = totalUniqueClassesByCourseIdByTeacher.get(courseId);
            for( Teacher teacher : teachersReportable ){
                if( registeredClassesMap != null && registeredClassesMap.get(teacher) != null && registeredClassesMap.get(teacher) > 0 ){
                    SessionLogReportV2_Row_Teachers row = new SessionLogReportV2_Row_Teachers();
                    row.setInstitution(institution);
                    if( beginDate == null ){
                        row.setPeriod("All Time");
                    }
                    else{
                        row.setPeriod(dateFormat.format(beginDate));
                    }
                    row.setCourseId(courseId);
                    row.setTeacher(teacher);
                    if( sessionsMap != null ){
                        row.setSessions(sessionsMap.get(teacher));
                    }
                    if( activeClassesMap != null ){
                        row.setActiveClasses(activeClassesMap.get(teacher));
                    }
                    if( registeredClassesMap != null ){
                        row.setRegisteredClasses(registeredClassesMap.get(teacher));
                    }
                    teachersTableRows.add(row);
                    reportedTeachers.add(teacher);
                }
            }
        }
        //Add a row if the teacher isn't assigned any classes
        for( Teacher teacher : teachersReportable ){
            if( reportedTeachers.contains(teacher) ) continue;
            SessionLogReportV2_Row_Teachers row = new SessionLogReportV2_Row_Teachers();
            row.setInstitution(institution);
            if( beginDate == null ){
                row.setPeriod("All Time");
            }
            else{
                row.setPeriod(dateFormat.format(beginDate));
            }
            row.setTeacher(teacher);
            teachersTableRows.add(row);
        }
        Collections.sort(teachersTableRows);
    }
    
    private void initSelectableMonths() throws Throwable{
        Date earliestSessionLogEntryDate = SessionLogReportsUtil.getEarliestSessionLogEntryDate();
        log.debug("earliestSessionLogEntryDate: "+earliestSessionLogEntryDate);
        if( earliestSessionLogEntryDate != null ){
            selectableMonths = DateUtil.getListOfBeginningsOfCurrentAndPriorMonths(earliestSessionLogEntryDate);
        }
        if( NUM_MONTHS_SELECTABLE != null ){
            if( selectableMonths == null || selectableMonths.size() > NUM_MONTHS_SELECTABLE ){
                selectableMonths = DateUtil.getListOfBeginningsOfCurrentAndPriorMonths(NUM_MONTHS_SELECTABLE);
            }
        }
    }
    
    public void setNewPeriod(Date newBeginDate, Date newEndDate) throws Throwable{
        log.debug("setNewPeriod");
        beginDate = newBeginDate;
        endDate = newEndDate;
        selectedMonth = beginDate;
        
        //for admin report: aggregates individual instituion reports
        if( adminReports != null ){
            log.debug("admin");
            for( SessionLogReportV2 report : adminReports ){
                report.setNewPeriod(newBeginDate, newEndDate);
            }
            return;
        }

        //for individual instituion report
        log.debug("instituion: "+institution.getInstitutionName());
        uniqueAgentCounts = getUniqueAgentCountsForPeriod(beginDate, endDate, null, null, institution);
    }
    
    public void updatePeriodTotals() throws Throwable{
        log.debug("updatePeriodTotals");
        //for admin report: aggregates individual instituion reports
        if( adminReports != null ){
            log.debug("admin");
            summaryTableRows.clear();
            classesTableRows.clear();
            teachersTableRows.clear();
            for( SessionLogReportV2 report : adminReports ){
                report.updatePeriodTotals();
                summaryTableRows.addAll(report.getSummaryTableRows());
                classesTableRows.addAll(report.getClassesTableRows());
                teachersTableRows.addAll(report.getTeachersTableRows());
            }
            Collections.sort(summaryTableRows);
            Collections.sort(classesTableRows);
            Collections.sort(teachersTableRows);
            return;
        }
        
        //for individual instituion report
        log.debug("instituion: "+institution.getInstitutionName());
        
        //additions for courseId
        totalStudentSessionsForPeriodByCourseId = new HashMap<String,Integer>();
        totalTeacherSessionsForPeriodByCourseId = new HashMap<String,Integer>();
        uniqueLoggedStudentsForPeriodByCourseId = new HashMap<String,Integer>();
        uniqueLoggedClassesForPeriodByCourseId = new HashMap<String,Integer>();
        uniqueLoggedTeachersForPeriodByCourseId = new HashMap<String,Integer>();
        //end additions for courseId
        
        //additions for V2
        totalStudentSessionsForPeriodByClass = new HashMap<GSClass,Integer>();
        uniqueLoggedStudentsForPeriodByClass = new HashMap<GSClass,Integer>();
        totalTeacherSessionsForPeriodByCourseIdByTeacher = new HashMap<String,Map<Teacher,Integer>>();
        uniqueLoggedClassesForPeriodByCourseIdByTeacher = new HashMap<String,Map<Teacher,Integer>>();
        //end additions for V2
        
        Set<GSClass> uniqueClasses = new HashSet<GSClass>();
        log.debug("populating results");
        log.debug("uniqueAgentCounts size="+uniqueAgentCounts.size());
        //Results are ordered by count, desc, so mostLogged is easy to fill
        for( SessionLogEntry sle : uniqueAgentCounts.keySet() ){
            log.debug(sle);
            Integer countForSle = uniqueAgentCounts.get(sle);
            log.debug("count: "+countForSle);
            if( sle.getStudent() != null ){
                log.debug("sle student: "+sle.getStudent());
                
                //additions for courseId
                if( sle.getGsClass() != null ){
                    String courseId = sle.getGsClass().getCourseId();
                    Integer priorTotalStudentSessionsForCourseId = totalStudentSessionsForPeriodByCourseId.get(courseId);
                    if( priorTotalStudentSessionsForCourseId == null ) priorTotalStudentSessionsForCourseId = 0;
                    totalStudentSessionsForPeriodByCourseId.put(courseId, priorTotalStudentSessionsForCourseId+countForSle);
                    Integer priorUniqueStudentsForCourseId = uniqueLoggedStudentsForPeriodByCourseId.get(courseId);
                    if( priorUniqueStudentsForCourseId == null ) priorUniqueStudentsForCourseId = 0;
                    uniqueLoggedStudentsForPeriodByCourseId.put(courseId, priorUniqueStudentsForCourseId+1);
                }
                //end additions for courseId

                //additions for V2
                GSClass gsClass = sle.getGsClass();
                if( gsClass != null ){
                    Integer priorTotalStudentSessionsForClass = totalStudentSessionsForPeriodByClass.get(gsClass);
                    if( priorTotalStudentSessionsForClass == null ) priorTotalStudentSessionsForClass = 0;
                    totalStudentSessionsForPeriodByClass.put(gsClass, priorTotalStudentSessionsForClass+countForSle);
                    Integer priorUniqueStudentsForClass = uniqueLoggedStudentsForPeriodByClass.get(gsClass);
                    if( priorUniqueStudentsForClass == null ) priorUniqueStudentsForClass = 0;
                    uniqueLoggedStudentsForPeriodByClass.put(gsClass, priorUniqueStudentsForClass+1);
                    
                    if( gsClass.getTeacher() != null ){
                        if( !uniqueClasses.contains(gsClass) ){
                            uniqueClasses.add(gsClass);
                            
                            Map<Teacher,Integer> innerCountMap = uniqueLoggedClassesForPeriodByCourseIdByTeacher.get(gsClass.getCourseId());
                            if( innerCountMap == null ){
                                innerCountMap = new HashMap<Teacher,Integer>();
                                uniqueLoggedClassesForPeriodByCourseIdByTeacher.put(gsClass.getCourseId(), innerCountMap);
                            }
                            Integer priorUniqueClassesByTeahcerCount = innerCountMap.get(gsClass.getTeacher());
                            if( priorUniqueClassesByTeahcerCount == null ) priorUniqueClassesByTeahcerCount = 0;
                            innerCountMap.put(gsClass.getTeacher(), priorUniqueClassesByTeahcerCount+1);

                            String courseId = gsClass.getCourseId();
                            Integer uniqueClassesForCourseId = uniqueLoggedClassesForPeriodByCourseId.get(courseId);
                            if( uniqueClassesForCourseId == null ) uniqueClassesForCourseId = 0;
                            uniqueLoggedClassesForPeriodByCourseId.put(courseId, uniqueClassesForCourseId+1);
                        }
                    }
                }
                //end additions for V2
            }
            else if( sle.getUser() != null && sle.getUser().getIsTeacher() ){
                Teacher teacher = (Teacher)sle.getUser().getUserType();
                log.debug("sle teacher: "+sle.getUser());
                //additions for V2
                //teacher session in CM aren't related to courseIds, so put total in each courseId that teacher has classes in.
                for( String courseId : courseIdsReportable ){
                    if( teacher.getActiveCourseIds().contains(courseId) ){
                        Map<Teacher, Integer> teacherSessions = totalTeacherSessionsForPeriodByCourseIdByTeacher.get(courseId);
                        if( teacherSessions == null ){
                            teacherSessions = new HashMap<Teacher,Integer>();
                        }
                        teacherSessions.put(teacher, countForSle);
                        totalTeacherSessionsForPeriodByCourseIdByTeacher.put(courseId, teacherSessions);
                    }
                }
                //end additions for V2
                
                //additions for courseId
                for( String courseId : teacher.getActiveCourseIds() ){
                    Integer priorTotalTeacherSessionsForCourseId = totalTeacherSessionsForPeriodByCourseId.get(courseId);
                    if( priorTotalTeacherSessionsForCourseId == null ) priorTotalTeacherSessionsForCourseId = 0;
                    totalTeacherSessionsForPeriodByCourseId.put(courseId, priorTotalTeacherSessionsForCourseId+countForSle);
                    Integer priorUniqueTeachersForCourseId = uniqueLoggedTeachersForPeriodByCourseId.get(courseId);
                    if( priorUniqueTeachersForCourseId == null ) priorUniqueTeachersForCourseId = 0;
                    uniqueLoggedTeachersForPeriodByCourseId.put(courseId, priorUniqueTeachersForCourseId+1);
                }
                //end additions for courseId
            }
            if( sle.getGsClass() != null ){
                //Class activity counts can span multiple results, so they must be accumulated.
                //The accumulated counts are stored temporarily in the GSClass object with a custom comparator,
                //the GSClasses and counts can then be popupulated in order in the final map
                log.debug("sle class: "+sle.getGsClass());
                uniqueClasses.add(sle.getGsClass());
                Integer previousCount = sle.getGsClass().getUserActivityCount();
                if( previousCount == null ) previousCount = 0;
                Integer countForClass = previousCount + countForSle;
                sle.getGsClass().setUserActivityCount(countForClass);
                log.debug("accumulation: "+sle.getGsClass().getUserActivityCount()+" for gsClass "+sle.getGsClass());
            }
        }
        
        initSummaryTableRows();
        initClassesTableRows();
        initTeachersTableRows();

    }
    
    public void debug(){
        if( !log.isDebugEnabled() ) return;
        if( adminReports != null ){
            for( SessionLogReportV2 report : adminReports ){
                report.debug();
            }
            return;
        }
        
        //Institution report
        log.debug("***********");
        log.debug("institution: "+institution);
        log.debug("beginDate: "+beginDate);
        log.debug("endDate: "+endDate);
        log.debug("selectableMonths: "+selectableMonths);
        log.debug("selectedMonth: "+selectedMonth);
        log.debug("students size="+students.size());
        log.debug("teachers size="+teachers.size());
        log.debug("classes size="+classes.size());
        log.debug("unqiueAgentCounts size="+uniqueAgentCounts.size());
        //additions for courseId
        log.debug("courseIdsReportable: "+courseIdsReportable);
        log.debug("totalUniqueStudentsByCourseId: "+totalUniqueStudentsByCourseId);
        log.debug("totalUniqueClassesByCourseId: "+totalUniqueClassesByCourseId);
        log.debug("totalUniqueTeachersByCourseId: "+totalUniqueTeachersByCourseId);
        log.debug("totalStudentSessionsForPeriodByCourseId: "+totalStudentSessionsForPeriodByCourseId);
        log.debug("totalTeacherSessionsForPeriodByCourseId: "+totalTeacherSessionsForPeriodByCourseId);
        log.debug("uniqueLoggedStudentsForPeriodByCourseId: "+uniqueLoggedStudentsForPeriodByCourseId);
        log.debug("uniqueLoggedClassesForPeriodByCourseId: "+uniqueLoggedClassesForPeriodByCourseId);
        log.debug("uniqueLoggedTeachersForPeriodByCourseId: "+uniqueLoggedTeachersForPeriodByCourseId);
        //end additions for courseId
        //additions for V2
        log.debug("totalStudentSessionsForPeriodByClass: "+totalStudentSessionsForPeriodByClass);
        log.debug("uniqueLoggedStudentsForPeriodByClass: "+uniqueLoggedStudentsForPeriodByClass);
        log.debug("totalUniqueStudentsByClass: "+totalUniqueStudentsByClass);
        //end additions for V2
    }

    public List<Date> getSelectableMonths() {
        return selectableMonths;
    }

    public Date getSelectedMonth() {
        return selectedMonth;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<SessionLogReportV2_Row_Summary> getSummaryTableRows() {
        return summaryTableRows;
    }

    public List<SessionLogReportV2_Row_Classes> getClassesTableRows() {
        return classesTableRows;
    }

    public List<SessionLogReportV2_Row_Teachers> getTeachersTableRows() {
        return teachersTableRows;
    }

    public HSSFWorkbook createWorkbook(ResourceBundle resourceText){
        HSSFWorkbook workbook = new HSSFWorkbook();
        initSummarySheet(workbook, resourceText);
        initClassesSheet(workbook, resourceText);
        initInstructorsSheet(workbook, resourceText);
        return workbook;
    }
    
    private void initSummarySheet(HSSFWorkbook workbook, ResourceBundle resourceText){
        HSSFSheet sheet = workbook.createSheet("Summary");
        int rowNum = 0;
        HSSFRow dateRow = sheet.createRow(rowNum);
        HSSFCell dateCell = dateRow.createCell((short)0);
        dateCell.setCellValue(resourceText.getString("report-sessionActivityV2-period")+": "+dateFormat.format(beginDate));
        rowNum++;
        HSSFRow headerRow = sheet.createRow(rowNum);
        short colNum = 0;
        HSSFCell h0 = headerRow.createCell(colNum++);
        HSSFCell h2 = headerRow.createCell(colNum++);
        HSSFCell h3 = headerRow.createCell(colNum++);
        HSSFCell h4 = headerRow.createCell(colNum++);
        HSSFCell h5 = headerRow.createCell(colNum++);
        HSSFCell h6 = headerRow.createCell(colNum++);
        HSSFCell h7 = headerRow.createCell(colNum++);
        HSSFCell h8 = headerRow.createCell(colNum++);
        HSSFCell h9 = headerRow.createCell(colNum++);
        HSSFCell h10 = headerRow.createCell(colNum++);
        HSSFCell h11 = headerRow.createCell(colNum++);
        h0.setCellValue(resourceText.getString("report-sessionActivityV2-institution"));
        h2.setCellValue(resourceText.getString("report-sessionActivityV2-course-excel"));
        h3.setCellValue(resourceText.getString("report-sessionActivityV2-studentSessions"));
        h4.setCellValue(resourceText.getString("report-sessionActivityV2-activeStudents"));
        h5.setCellValue(resourceText.getString("report-sessionActivityV2-newStudents"));
        h6.setCellValue(resourceText.getString("report-sessionActivityV2-registeredStudents"));
        h7.setCellValue(resourceText.getString("report-sessionActivityV2-instructorSessions"));
        h8.setCellValue(resourceText.getString("report-sessionActivityV2-activeInstructors"));
        h9.setCellValue(resourceText.getString("report-sessionActivityV2-registeredInstructors"));
        h10.setCellValue(resourceText.getString("report-sessionActivityV2-activeClasses"));
        h11.setCellValue(resourceText.getString("report-sessionActivityV2-registeredClasses"));
        rowNum++;
        if( getSummaryTableRows() != null ){
            for( SessionLogReportV2_Row_Summary summaryRow : getSummaryTableRows() ){
                HSSFRow r = sheet.createRow(rowNum);
                colNum = 0;
                HSSFCell c0 = r.createCell(colNum++);
                HSSFCell c2 = r.createCell(colNum++);
                HSSFCell c3 = r.createCell(colNum++);
                HSSFCell c4 = r.createCell(colNum++);
                HSSFCell c5 = r.createCell(colNum++);
                HSSFCell c6 = r.createCell(colNum++);
                HSSFCell c7 = r.createCell(colNum++);
                HSSFCell c8 = r.createCell(colNum++);
                HSSFCell c9 = r.createCell(colNum++);
                HSSFCell c10 = r.createCell(colNum++);
                HSSFCell c11 = r.createCell(colNum++);

                c0.setCellValue(summaryRow.getInstitutionName_nonNull());
                c2.setCellValue(summaryRow.getCourseId());
                c3.setCellValue(summaryRow.getStudentSessions());
                c4.setCellValue(summaryRow.getActiveStudents());
                c5.setCellValue(summaryRow.getNewStudents());
                c6.setCellValue(summaryRow.getRegisteredStudents());
                c7.setCellValue(summaryRow.getInstructorSessions());
                c8.setCellValue(summaryRow.getActiveInstructors());
                c9.setCellValue(summaryRow.getRegisteredInstructors());
                c10.setCellValue(summaryRow.getActiveClasses());
                c11.setCellValue(summaryRow.getRegisteredClasses());

                rowNum++;
            }
        }
    }

    private void initClassesSheet(HSSFWorkbook workbook, ResourceBundle resourceText){
        HSSFSheet sheet = workbook.createSheet("Classes");
        int rowNum = 0;
        HSSFRow dateRow = sheet.createRow(rowNum);
        HSSFCell dateCell = dateRow.createCell((short)0);
        dateCell.setCellValue(resourceText.getString("report-sessionActivityV2-period")+": "+dateFormat.format(beginDate));
        rowNum++;
        HSSFRow headerRow = sheet.createRow(rowNum);
        short colNum = 0;
        HSSFCell h0 = headerRow.createCell(colNum++);
        HSSFCell h2 = headerRow.createCell(colNum++);
        HSSFCell h3 = headerRow.createCell(colNum++);
        HSSFCell h4 = headerRow.createCell(colNum++);
        HSSFCell h5 = headerRow.createCell(colNum++);
        HSSFCell h6 = headerRow.createCell(colNum++);
        h0.setCellValue(resourceText.getString("report-sessionActivityV2-institution"));
        h2.setCellValue(resourceText.getString("report-sessionActivityV2-course-excel"));
        h3.setCellValue(resourceText.getString("report-sessionActivityV2-name"));
        h4.setCellValue(resourceText.getString("report-sessionActivityV2-sessions"));
        h5.setCellValue(resourceText.getString("report-sessionActivityV2-activeStudents"));
        h6.setCellValue(resourceText.getString("report-sessionActivityV2-registeredStudents"));
        rowNum++;
        if( getClassesTableRows() != null ){
            for( SessionLogReportV2_Row_Classes reportRow : getClassesTableRows() ){
                HSSFRow r = sheet.createRow(rowNum);
                colNum = 0;
                HSSFCell c0 = r.createCell(colNum++);
                HSSFCell c2 = r.createCell(colNum++);
                HSSFCell c3 = r.createCell(colNum++);
                HSSFCell c4 = r.createCell(colNum++);
                HSSFCell c5 = r.createCell(colNum++);
                HSSFCell c6 = r.createCell(colNum++);

                c0.setCellValue(reportRow.getInstitutionName_nonNull());
                c2.setCellValue(reportRow.getCourseId());
                c3.setCellValue(reportRow.getGsClassName_nonNull());
                c4.setCellValue(reportRow.getSessions());
                c5.setCellValue(reportRow.getActiveStudents());
                c6.setCellValue(reportRow.getEnrolledStudents());

                rowNum++;
            }
        }
    }
    
    private void initInstructorsSheet(HSSFWorkbook workbook, ResourceBundle resourceText){
        HSSFSheet sheet = workbook.createSheet("Instructors");
        int rowNum = 0;
        HSSFRow dateRow = sheet.createRow(rowNum);
        HSSFCell dateCell = dateRow.createCell((short)0);
        dateCell.setCellValue(resourceText.getString("report-sessionActivityV2-period")+": "+dateFormat.format(beginDate));
        rowNum++;
        HSSFRow headerRow = sheet.createRow(rowNum);
        short colNum = 0;
        HSSFCell h0 = headerRow.createCell(colNum++);
        HSSFCell h2 = headerRow.createCell(colNum++);
        HSSFCell h3 = headerRow.createCell(colNum++);
        HSSFCell h4 = headerRow.createCell(colNum++);
        HSSFCell h5 = headerRow.createCell(colNum++);
        HSSFCell h6 = headerRow.createCell(colNum++);
        HSSFCell h7 = headerRow.createCell(colNum++);
        h0.setCellValue(resourceText.getString("report-sessionActivityV2-institution"));
        h2.setCellValue(resourceText.getString("report-sessionActivityV2-course-excel"));
        h3.setCellValue(resourceText.getString("report-sessionActivityV2-name"));
        h4.setCellValue(resourceText.getString("report-sessionActivityV2-email"));
        h5.setCellValue(resourceText.getString("report-sessionActivityV2-sessions"));
        h6.setCellValue(resourceText.getString("report-sessionActivityV2-activeClasses"));
        h7.setCellValue(resourceText.getString("report-sessionActivityV2-registeredClasses"));
        rowNum++;
        if( getTeachersTableRows() != null ){
            for( SessionLogReportV2_Row_Teachers reportRow : getTeachersTableRows() ){
                HSSFRow r = sheet.createRow(rowNum);
                colNum = 0;
                HSSFCell c0 = r.createCell(colNum++);
                HSSFCell c2 = r.createCell(colNum++);
                HSSFCell c3 = r.createCell(colNum++);
                HSSFCell c4 = r.createCell(colNum++);
                HSSFCell c5 = r.createCell(colNum++);
                HSSFCell c6 = r.createCell(colNum++);
                HSSFCell c7 = r.createCell(colNum++);

                c0.setCellValue(reportRow.getInstitutionName_nonNull());
                c2.setCellValue(reportRow.getCourseId());
                c3.setCellValue(reportRow.getTeacherName_nonNull());
                if( reportRow.getTeacher() != null ){
                    c4.setCellValue(reportRow.getTeacher().getEmail());
                }
                c5.setCellValue(reportRow.getSessions());
                c6.setCellValue(reportRow.getActiveClasses());
                c7.setCellValue(reportRow.getRegisteredClasses());

                rowNum++;
            }
        }
    }

}
