package org.montereyinstitute.tagmodel;

import gs.hinkleworld.core.AppUtil;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.*;
import org.montereyinstitute.model.CourseNode.*;
import org.montereyinstitute.view.devMath.TopicTimesView;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class ModalDialogForUnitProgressReport extends Dialog {
    private static Logger log = LogManager.getLogger(ModalDialogForUnitProgressReport.class);
    
    public static final String OPTION_PARAM = "option";
    public static final String OPTION_VALUE_PREASSESSMENT = Activity.DEV_MATH_LABEL_PREASSESSMENT;
    public static final String OPTION_VALUE_WARMUP = Activity.DEV_MATH_LABEL_WARMUP;
    public static final String OPTION_VALUE_PRACTICE = Activity.DEV_MATH_LABEL_PRACTICE;
    public static final String OPTION_VALUE_REVIEW = Activity.DEV_MATH_LABEL_REVIEW;
    public static final String OPTION_VALUE_TIME = "time";

    private GSClass gsClass;
    private Student student;
    private Course course;
    private Topic topic;
    private Lesson lesson;
    private Unit unit;
    private TopicStateForStudentClass topicState;

    @Override
    public void init(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        if( log.isDebugEnabled() ) AppUtil.logRequestParams(request);
        gsClass = GSClass.getGSClassUseSession(request);
        student = Student.getStudentUseSession(request, gsClass);
        course = Course.getCourse(request, gsClass.getCourseId());
        topic = (Topic) course.getDescendantByTypeAndId(Topic.class, request.getParameter(Topic.ID_PARAM));
        lesson = (Lesson) topic.getParent();
        unit = (Unit) lesson.getParent();
        topicState = TopicStateForStudentClass.getFromDb(student, gsClass, unit.getNumber(), lesson.getNumber(), topic.getNumber());
        topicState.initAppCourseNodes(course);
        topicState.loadParentUnitStateFromDb();
        if( topicState == null ) log.debug("topicState null");
        else{
            log.debug("topicState id="+topicState.getId());
            if( topicState.getParentUnitState() != null ){
                log.debug("parent unitState id="+topicState.getParentUnitState().getId());
            }
            else{
                log.debug("parent unitState is null");
            }
        }
        request.setAttribute("TopicState", topicState);
        
        if( request.getParameter(OPTION_PARAM).equals(OPTION_VALUE_TIME) ){
            TopicTimesView topicTimesView = new TopicTimesView(topicState);
            request.setAttribute("TopicTimesView", topicTimesView);
            destination = "/WEB-INF/jsp/dialog/topicTimes.jsp";
        }
        else{
            Activity activity = null;
            if( request.getParameter(OPTION_PARAM).equals(OPTION_VALUE_PREASSESSMENT) ){
                activity = unit.getPreassessment();
            }
            else{
                activity = topic.getActivity(request.getParameter(OPTION_PARAM));
            }
            log.debug("activity: "+activity.getId()+", "+activity.getLabel());
            Map<Objective, Integer> objectiveScoreMap = topicState.getObjectiveScoreMapForActivity(activity);
            Boolean allScoresNull = true;
            for( Integer score : objectiveScoreMap.values() ){
                if( score != null ) allScoresNull = false; 
            }
            request.setAttribute("HeaderKey", "topic-"+activity.getLabel()+"-objectiveScores");
            request.setAttribute("ObjectiveScoreMap",objectiveScoreMap);
            request.setAttribute("allScoresNull",allScoresNull);
            destination = "/WEB-INF/jsp/dialog/topicObjectiveScores.jsp";
        }
        
    }
    
}
