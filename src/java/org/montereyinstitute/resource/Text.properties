#GENERAL/GLOBAL
global-title=Course Management and Reporting
global-locale=default
global-locale-description=English (default)
global-author-default=
global-keywords-default=
global-description-default=
global-copyright=<a href="https://content.nroc.org/license/license.html" target="_blank" id="globalFooterAnchor">Copyright \u00a92021</a> The NROC Project
global-copyright-topicPlayer=<a href="https://content.nroc.org/license/topicplayer.html" target="_blank" id="globalFooterAnchor">Copyright \u00a92021</a> The NROC Project
global-version=version
global-dev-math=Developmental Math
global-dev-eng=Developmental English
error-pageNotFound=Page Not Found
error-accessDenied=Access Denied
error-default=Error
home-title=Home
login-title=Log In
login-securityError=An error has occurred. Please try again.
login-retryWarning=The username and/or password is incorrect.<br/>Please try again.
login-retryHelpLink=See <a href="https://support.nroc.org/hc/en-us/articles/201236466" target="_blank">this article</a> for additional help.
logControl-loggedInAs=Logged in as
logControl-logOut=Log Out
form-requiredFields=Required Fields
form-submit=Save Changes
form-proceed=Proceed
form-updateDisplay=Update Display
form-downloadAsExcel=Download as Excel
form-unlock=Unlock
form-saveError=A error occurred. Data was not saved.
form-delete=Delete
form-remove=Remove
form-activate=Activate
form-inactivate=Inactivate
form-add=Add
form-yes=Yes
form-no=No
form-on=On
form-off=Off
na=N/A
page=Page
or=OR
emailSent=An email notification has been sent.
emailFail=There was a problem sending your email notification.
noClassesPermission=There are no classes that you are currently permitted to edit.

#NAVIGATION
navigation-userGuide=User Guide
navigation-help=More Information
navigation-editProfile=Edit Profile
navigation-editClass=Class Settings
navigation-editClassContent=Unit Content
navigation-editWritingCenterContent=Unit Content
navigation-viewReport=Reports
navigation-viewReviews=Reviews
navigation-viewSessionReport=Activity
navigation-viewClasses=My Classes
navigation-viewTeachers=Instructors
navigation-viewStudents=Students
navigation-viewInstitutions=Institutions
navigation-siteManagement=Site Management
navigation-report-preassessment=Pre-assessment Report
navigation-report-unitProgress=Unit Progress Report
navigation-report-classSummary=Class Summary Report
navigation-report-institutionSummary=Institution Summary
navigation-report-peerReviews=Peer Reviews
navigation-report-sessionActivity-summary=Summary
navigation-report-sessionActivity-classes=Classes
navigation-report-sessionActivity-instructors=Instructors
navigation-adminMessage=Site Banner
navigation-changePassword=Change your password here.

#VALIDATOR
invalid-default={0} is invalid
invalid-integer={0} must be an integer
invalid-decimal={0} must be a number
invalid-boole={0} be selected
invalid-creditCard=Invalid credit card number
invalid-email=Invalid email address
invalid-uniqueEmail=Email already exists
invalid-url=Invalid URL
invalid-required={0} is a required field
invalid-repeatPassword=Repeat Password does not match
invalid-min={0} cannot be below {1}
invalid-max={0} cannot be above {1}
invalid-minLength={0} cannot have fewer than {1} characters
invalid-maxLength={0} cannot have more than {1} characters
invalid-date={0} is not a valid date
invalid-regex={0} is invalid
invalid-uniqueId={0} already exists
invalid-uniqueUserName={0} already exists
invalid-instructor=Instructor is required
emailConflict-username=This email address is already associated with an instructor account (username {0}).
emailConflict-register=You can register your class by selecting your name in the dropdown list of instructors.
#CUSTOM VALIDATION
customInvalid-oldPassword=Old Password is a required field
customInvalid-newPassword=New Password is a required field
customInvalid-repeatPassword=Repeat Password is a required field
customInvalid-pwResetBadUsername=The username you entered was not correct. Please return to the password reset email you received, note the username for your account, and click the password reset link again.

#SITE VIEWS
lostPassword-title=Reset Password
lostPassword-link=Reset Password
lostPassword-instruction=Enter your username or email address to reset your password.<br/>Click "Proceed" then check your email for instructions to create a new password.
lostPassword-noAccount=Your account information was not found.
lostPassword-adminWarning=Administrative accounts cannot be sent a reset password request. Your password was not changed. Please contact another administrator for assistance.
lostPassword-emailFailed=A problem occurred with email. Please try again.
lostPasswordReturn-enterUsername=Enter username to continue:
lostPasswordReturn-title=Continue to your new password
lostPassword-error-title=Reset Password Error
lostPassword-error-notProcessed=Your request could not be processed.
lostPassword-error-tryAgain=Please try again in a moment.
lostPassword-emailRequested=Reset Password Email Has Been Requested
lostPassword-receiveEmailShortly=If the email address was located, you should received an email shortly.
lostPassword-title-complete=Reset Password Complete
lostPassword-complete-1=Check your email.
lostPassword-complete-2=If you entered a valid username or email address, we sent password reset instructions to the email address associated with your account. 
lostPassword-complete-3=The email <NAME_EMAIL> and the subject is "Password reset for NROC Course Manager". 
lostPassword-complete-4=If you do not receive the email within 5 minutes, check your spam folder.
lostPassword-complete-5-1=You may
lostPassword-complete-5-2=start a new request
lostPassword-complete-5-3=which will override the previous one.
lostPassword-complete-6=If you need assistance, contact us via the HELP button at the bottom of the screen.
editProfile-title=Edit Profile
editProfile-success=Your profile has been updated
editClass-title=Edit Class Settings
editClass-current=Currently Editing
editClass-institutionName=Institution Name
editClass-courseId=Class ID
editClass-courseLocation=Class Location
editClass-masteryScore=Mastery Score
editClass-current=Currently Editing
editClass-change=Select a class to edit
editClass-success=Class settings have been updated
editClass-className=Class Name
editClass-status=Active Status
editClass-quickPreassessment=Pre-Assessment Type
editClass-quickPreassessment-true=Quick
editClass-quickPreassessment-false=Detailed
editClass-quickPreassessment-none=None
editClass-retakePreassessment=Retake Pre-Assessment
editClass-retakePreassessment-true=Allowed
editClass-retakePreassessment-false=Not Allowed
editClass-retakeTopicReview=Retake Topic Review
editClass-retakeTopicReview-true=Allowed
editClass-retakeTopicReview-false=Not Allowed
editClass-allowEmailHelp=Student Request Help via Email
editClass-allowEmailHelp-true=Allowed
editClass-allowEmailHelp-false=Not Allowed
editClass-teacherId=Primary Instructor
editClass-secondaryTeacherId=Secondary Instructors
editClass-teacherName=Instructor Name
editClass-teacherEmail=Instructor Email
editClass-assignNewTeacher=Create a new primary instructor
editClass-assignNewSecondaryTeacher=Create a new secondary instructor
editClass-teacherCreated=The new instructor has been assigned this class
editClass-subheading-review=Review
editClass-subheading-writingCenter=Writing Center
editClass-reviewAllowRetakes=Allow Retakes
editClass-reviewMasteryLevel=Mastery Level
editClass-allowPeerReviews=Allow Peer Reviews
editClass-allowPeerReviews-allowed=Allowed
editClass-allowPeerReviews-notAllowed=Not Allowed
editClass-numberOfReviewers=Number of Reviewers
editClass-daysAllowed=Days Allowed to Complete a Review
editClass-assignedRandomly=Reviewers are assigned randomly
editClass-allowInstructorEmail=E-mail Notifications for Instructor Reviews
editClass-allowInstructorEmail-true=Allowed
editClass-allowInstructorEmail-false=Not Allowed
editClass-peerReviewsOnUnitContentTab=Peer Reviews are assigned<br/>on the Unit Content tab
unitMap-instructions-0=You can start by selecting the Pre-Assessment and answering some questions to identify what you already know about the math in this Unit. After you finish all of the questions, you will be given a custom "My Learning Path" that shows only the material you need to study.
unitMap-instructions-1=The Unit Map shows all of the Topics in this Unit. Click on a Topic name to begin studying. Topics you have attempted have an outlined checkmark next to them. Topics you have mastered have a solid checkmark. You can master a Topic by answering question in the Pre-Assessment or by opening a Topic and completing the Review activity.
unitMap-instructions-2=My Learning Path shows only the Topics you need to study based on your answers in the Pre-Assessment. Click on a Topic to begin studying. We suggest you work through the Topics in the order listed. As you master additional Topics they will be removed from your Learning Path.
unitMap-alert-startedNotCompleted=You have started but not completed this topic.
unitMap-alert-completedNotMastered=You have attempted but not mastered this topic.
unitMap-alert-completedAndMastered=You have mastered this topic.
unitMap-alert-notStarted=You have not started this topic.
unitMap-myLearningPathCompleted=Congratulations, you have mastered all of the topics in this unit.
unitMap-alert-home-startedNotCompleted=You have started but not completed this activity.
unitMap-alert-home-completedAndMastered=You have completed this activity.
unitMap-alert-home-completedNotMastered=You have completed but not mastered this activity.
unitMap-alert-home-notStarted=
unitMap-keyboardFocusSelector=Show keyboard focus:
unitMap-screenReaderSelector=Enable screen reader support:
changePassword-default=Change your password:
changePassword-weakPassword=Please update your password now to a strong password:
changePassword-fromLostPasswordEmailLink=Please change your password now:
changePassword-notStrongEnough=Your new password must be at least 12 characters and contain the following:
changePassword-notStrongEnough-list=<li>at least one uppercase letter</li><li>at least one lowercase letter</li><li>at least one number</li><li>at least one non-alphanumeric character (! @ # $ % ^ &amp; * ( ) _ + - = [ ] { } | &apos;)</li>

viewClasses-title=Classes
viewClasses-status=Status
viewClasses-className=Class Name
viewClasses-courseId=Class ID
viewClasses-options=Options
viewClasses-rename=Rename
viewClasses-activate=Activate
viewClasses-deactivate=Deactivate
viewClasses-delete=Delete
viewClasses-deleteConfirm=Are you sure you want to delete this class?
viewClasses-editSettings=Edit Settings
viewClasses-editContent=Edit Content
viewClasses-report=View Report
viewClasses-secondaryInstructor=(Secondary Instructor)
editTeacher-title=Edit Instructor
editTeacher-emailNeeded=This instructor needs an email address in order to receive student messages
editTeacher-success=Instructor information has been updated
viewTeachers-title=Instructors
viewTeachers-options=Options
viewTeachers-edit=Edit
viewTeachers-delete=Delete
viewTeachers-deleteConfirm=Are you sure you want to delete this instructor?
viewTeachers-create=Add New Instructor
createTeacher-title=Add New Instructor
createTeacher-submit=Add This Instructor
createTeacher-success=Instructor has been added
viewInstitutions-title=Institutions
viewInstitutions-options=Options
viewInstitutions-edit=Edit
viewInstitutions-delete=Delete
viewInstitutions-deleteConfirm=Are you sure you want to delete this institution?
viewInstitutions-create=Add New Institution
viewInstitutions-activate=Activate
viewInstitutions-deactivate=Deactivate
viewInstitutions-loginAs=Login As
createInstitution-title=Add New Institution
createInstitution-submit=Add This Institution
createInstitution-success=Institution has been added
editInstitution-title=Edit Institution
editInstitution-status=Active Status
editInstitution-success=Institution settings have been updated
editInstitution-allowMultiURLforLTI=Allow Multiple URLs for LTI
editClassContent-title=Edit Unit Content
editClassContent-instuction=Unselect any Topics you wish to remove from the Unit Map and Pre-assessment
editClassContent-changeUnit=Select a Unit to edit
editClassContent-success=Visibility has been set
editWritingCenterContent-editAssignmentInstructions=Additional Assignment Instructions
editWritingCenterContent-scaffoldTips=Scaffolding Tips
editWritingCenterContent-customScaffoldTips=Custom Scaffolding Tips
editWritingCenterContent-writingProcess=Writing Process
editWritingCenterContent-peerReview=Peer Review
editWritingCenterContent-instructorReview=Instructor Review
editWritingCenterContent-firstDraft=First Draft
editWritingCenterContent-secondDraft=Second Draft
editWritingCenterContent-success=Unit settings have been saved
editWritingCenterContent-editAssignmentInstructionsInvalid=Assignment Instructions cannot be empty if selected
checkClassStatus-title=Class Status
checkClassStatus-registered=Status: Registered
checkClassStatus-unregistered=Status: Unregistered
checkClassStatus-create=Create this class
checkClassStatus-finalCreate=You must save settings to create this class
checkClassStatus-alreadyExists=This class already exists in the database
checkClassStatus-edit=Login
checkClassStatus-selectInstructor=Select Instructor
checkClassStatus-newInstructor=New Instructor
checkClassStatus-instructorRequired=New or existing instructor is required
checkClassStatus-mustLogin=(You must login first.)
checkClassStatus-institutionId=Institution Id
checkClassStatus-institutionName=Institution Name
checkClassStatus-instructorName=Instructor Name
checkClassStatus-courseId=Class Id
checkClassStatus-courseName=Class Name
checkClassStatus-uniqueCourseName=Class Name is already in use
checkClassStatus-instructorsFirstName=Instructor's First Name
checkClassStatus-instructorsLastName=Instructor's Last Name
checkClassStatus-instructorsEmail=Instructor's Email
checkClassStatus-instructorsLoginId=Username
checkClassStatus-password=Password
checkClassStatus-verifyPassword=Verify Password
checkClassStatus-allFieldsRequired=All fields above are required.
checkClassStatus-inheritCourseSettings=Inherit Class Settings
checkClassStatus-fromExistingClass=from an existing class
checkClassStatus-selectClassToInheritFrom=Select Class from which to inherit
checkClassStatus-confirm-dm1-1=<span style="font-weight:bold; color:red">WARNING:</span> You are attempting to register an unsupported version of the NROC Developmental Math course.
checkClassStatus-confirm-dm1-2=NROC no longer supports Flash versions of this course. A newer version is available that does not use Flash. For more information, click <a href="https://support.nrocnetwork.org/hc/en-us/articles/360003346753-Support-for-Flash-based-Math-Courses-Ends-in-August-2018" target="_blank">here</a>.
checkClassStatus-alert-uniqueCourseName=The Class Name you have entered is in use.<br/>Class Names must be unique.<br/>Please enter a different Class Name.
viewStudentsByClass-title=Students
viewStudentsByClass-current=Currently viewing
viewStudentsByClass-change=Select a class to view
viewStudentsByClass-options=Options
dialog-noDataPresent=Detailed tracking in not available since this work was completed before this feature was implemented.
dialog-activityTimeDetail=Activity Time Detail
dialog-objectiveScores=Objective Scores
dialog-playerLink=Open Summary in Player
dialog-activeReaderTimeDetail=Active Reader Time Detail
dialog-writingCenterTimeDetail=Writing Center Time Detail
dialog-writingCenterResetOptions=Reset Writing Center to Previous Stage
dialog-writingCenterResetOptions-instructions=You can reset this student's Writing Center to a previous stage of completion. The student's work for that step will be preserved, while any peer or teacher reviews for this step or after will be removed. Reviews for prior steps will remain.
dialog-writingCenterResetOptions-warning=WARNING: These changes are permanent!
dialog-writingCenterResetOptions-warning2=The student should log out of their current session before proceeding.
dialog-writingCenterResetOptions-confirm=WARNING: These changes are permanent!\\nAre you sure you want to reset this Writing Center?
dialog-writingCenterResetOptions-full=Full Reset (all work is lost)
dialog-writingCenterResetOptions-first=First Draft (draft preserved, reviews removed)
dialog-writingCenterResetOptions-second=Second Draft (draft preserved, reviews removed)
dialog-writingCenterResetOptions-final=Final Draft (draft preserved, reviews removed)
dialog-writingCenterResetOptions-grade=Instructor Grade and Comments (no student work removed)
dialog-writingCenterResetOptions-instructions-byStep-first=The student has completed and submitted their First Draft. You can have the student re-submit their First Draft by clicking <a href="{0}">here</a>. Any peer, instructor, or self-review of the First Draft will be lost.
dialog-writingCenterResetOptions-instructions-byStep-second=The student has completed and submitted their Second Draft. You can have the student re-submit their Second Draft by clicking <a href="{0}">here</a>. Any peer, instructor, or self-review of the Second Draft will be lost.
dialog-writingCenterResetOptions-instructions-byStep-final=The student has completed all of the work in the Writing, Center. You can have the student re-submit their Final Draft by clicking <a href="{0}">here</a>.
dialog-writingCenterResetOptions-instructions-byStep-grade=The student has completed all of the work in the Writing, Center. You can have the student re-submit their Final Draft by clicking <a href="{0}">here</a>. Your comments on their original final draft will be lost. If you want to change the student\u2019s final grade and comments click <a href="{1}">here</a>.

dialog-ileTimeDetail=Interactive Learning Exercise Time Detail
dialog-peerReviews-originator=Draft Writer
dialog-peerReviews-reviewer=Reviewer
dialog-peerReviews-date=Date Assigned
dialog-peerReviews-change=Change this Peer Review Assignment
dialog-peerReviews-cancel=Cancel this Peer Review
dialog-peerReviews-noChange=No Change Selected
dialog-peerReviews-confirmChange=Are you sure you want to change this Peer Review Assignment?
dialog-peerReviews-confirmCancel=Are you sure you want to cancel this Peer Review Assignment?
dialog-peerReviews-draftStep=Completed Draft
viewReviews-title=Reviews
viewReviews-actionPending=You have Writing Center drafts ready for review
viewReviews-reviewType=Review Type
viewReviews-dateSubmitted=Date Submitted
viewReviews-reviewCompleted=Your review has been received
adminMessage-instruction-siteBannerOn=Site Banner On
adminMessage-instruction-siteBannerOff=Site Banner Off
adminMessage-instruction-new=Add a new message (HTML can be used):
adminMessage-instruction-active=Edit the current message:
adminMessage-instruction-chooseExisting=Choose a previous message:
adminMessage-title=Site Banner Settings
adminMessage-success=Update Successful
adminMessage-preview=Preview Message
adminMessage-previewExplained=The preview of the message banner is at the top of this page.<br/>You can accept or cancel the preview here.
adminMessage-statusUpdate=Change On/Off Status
adminMessage-apply=Apply to Live Site
adminMessage-cancel=Cancel Update
adminMessage-notice=NOTICE: 
adminMessage-activityReportStarted=The Admin Activity Report has been started.

#MODEL OBJECTS
user-userName=Username
user-password=Password
user-email=Email
user-change-password=Change Password
user-old-password=Old Password
user-new-password=New Password
user-repeat-password=Repeat Password
teacher-instructor=Instructor's
teacher-firstName= First Name
teacher-lastName=Last Name
teacher-email=Email
teacher-institutionName=Institution Name
teacher-teacher=Instructor
teacher-teachers=Instructors
institution-institutionName=Institution Name
institution-administrator=Administrator's
institution-firstName=First Name
institution-lastName=Last Name
institution-email=Institution Email
institution-key=Key
institution-keySecret=Secret
institution-status=Status
institution-active=Active
institution-inactive=Inactive
institution-siteurl=Site URL
institution-allowed=Allowed
institution-notAllowed=Not Allowed
gsClass-class=Class Name
gsClass-classes=Classes
gsClass-active=Active
gsClass-inactive=Inactive
gsClass-inactiveStudents=Inactivated Students in Averages
course-id=Class ID
unit-unit=Unit
unit-attempts=Attempts
unit-review=Review Score
unit-score=Review Score<br/>(Average)
unit-score-nobr=Review Score (Average)
unit-prescore=Pre-assessment Score<br/>(Average)
unit-preAScore=PreA Score
unit-preattempts=PreA Attempts
unit-preAAttempts=PreA Attempts
unit-preADateCompleted=PreA Date Completed
unit-time=Total Unit Time<br/>(HH:MM:SS)
unit-time-nobr=Total Unit Time (HH:MM:SS)
unit-simScore=Sim Score
unit-simTime=Sim Time
unit-simDateCompleted=Sim Date Completed
unit-launch=Launch Unit
topic-topic=Topic
topic-id=Topic ID
topic-studentStatus=Student Status
topic-attempts=Attempts
topic-preassessmentState=PreA State
topic-preAState=PreA State
topic-time=Total Topic Time (HH:MM:SS)
topic-times=Topic Times
topic-preassessment-objectiveScores=Topic Pre-assessment
topic-warmup-objectiveScores=Topic Warm-up
topic-practice-objectiveScores=Topic Practice
topic-review-objectiveScores=Topic Review
topic-time-short=Total Time
topic-activity=Activity
topic-dateCompleted=Date Completed
topic-time-home=Home
topic-time-warmup=Warm-up
topic-time-presentation=Presentation
topic-time-workedExmples=Worked Examples
topic-time-practice=Practice
topic-time-review=Review
topic-prescore=PreA Score
topic-preAScore=PreA Score
topic-warmup=Warm-up Score
topic-practice=Practice Score
topic-review=Review Score
topic-for-objective=for Objective
topic-inactiveStudents=Show Inactivated Students
topic-reviewScores=Review Scores
topic-mastered=Topics Mastered
topic-notMastered=Topics Not Mastered
student-id=Student ID
student-name=Student Name
student-firstName= First Name
student-lastName=Last Name
student-courseScore=Review Score<br/>(Average)
student-preAssessmentScore=Pre-assessment Score<br/>(Average)
student-courseTime=Total Class Time<br/>(HH:MM:SS)
student-status=Inactivated
student-topicsAttempted=Topics Attempted
student-topicsMastered=Topics Mastered
student-unitsCompleted=Units Completed
student-unitsAttempted=Units Attempted
student-student=Student
student-students=Students
course-devMath=Dev Math
course-devMath2=Dev Math 2
course-devEnglish=Dev English
course-combined=Combined
confirm-ok=OK
confirm-cancel=Cancel

#REPORTS
report-home-title=Reports
report-empty=This report has no data
report-options=Options
report-options-student=Student Options
report-options-class=Class Options
report-preassessment-title=Pre-Assessment Report
report-classSummary-title=Class Summary Report
report-classSummary-displayOptions=Display Options
report-institutionSummary=Institution Summary
report-unitProgress-forClass=for Class
report-unitProgress-title=Unit Progress Report
report-unitProgress-changeUnit=Select a Unit to view
report-unitProgress-displayColumns=Display Columns
report-unitProgress-deleteConfirm=Are you sure you want to delete this student?
report-unitProgress-deleteWarning=Warning: If you delete a student, all records of the student work will be deleted and there is no way of recovering the data.
report-unitProgress-deleteConfirm-class=Are you sure you want to delete this class?
report-key-black=black scores
report-key-black-def=at or above mastery level
report-key-red=red scores
report-key-red-def=below mastery level
report-key-blue=blue scores
report-key-bluePound=blue #
report-key-blue-def=test that has not been completed
report-classAverages=Class Averages
report-sessionActivity-title=Activity Report
report-sessionActivity-changeDate=Select a month to view
report-sessionActivity-dateRange=Date Range
report-sessionActivity-totalSessions=Total Sessions
report-sessionActivity-uniqueAgents=Users (for period selected)
report-sessionActivity-uniqueAgentsAllTime=Users (all time)
report-sessionActivity-totals=Totals
report-sessionActivity-mostActive=Most Active
report-sessionActivity-rank=Rank
report-sessionActivity-count=Sessions
report-sessionActivity-name=Name
report-sessionActivity-allTime=All Time
report-sessionActivity-allInstitutions=All Institutions
report-export-all=Export All Units
report-totalTime=Total Time
report-score=Score
report-lastFirst=Last, First
report-status=Status
report-statusCode-0=--
report-statusCode-1=In Progress
report-statusCode-2=Completed
report-statusCode-3=Completed
report-link=Link
report-view=View
report-peerReviews-title=Peer Reviews
report-peerReviews-draftNotCompleted=draft not completed
report-peerReviews-empty=empty
report-peerReviews-colorKey=Status Key: <span class="reviewCompleted">Review Completed</span>, <span class="reviewNotCompleted">Review Pending</span>, <span class="reviewExpired">Review Overdue</span>, <span class="reviewCancelled">Review Cancelled</span>
report-loadAllDetails=Load All Details
report-load-pause=Pause Loading
report-load-continue=Continue Loading
report-loadDetails=Load Details
report-refreshData=Refresh Data
report-resetOptions=Reset
report-dateCompleted=Date Completed
report-sessionActivityV2-institution=Institution
report-sessionActivityV2-period=Period
report-sessionActivityV2-course=Course<br/>Type
report-sessionActivityV2-course-excel=Course Type
report-sessionActivityV2-name=Name
report-sessionActivityV2-sessions=Sessions
report-sessionActivityV2-email=Email
report-sessionActivityV2-studentSessions=Student Sessions
report-sessionActivityV2-activeStudents=Active Students
report-sessionActivityV2-newStudents=New Students
report-sessionActivityV2-registeredStudents=Registered Students
report-sessionActivityV2-instructorSessions=Instructor Sessions
report-sessionActivityV2-activeInstructors=Active Instructors
report-sessionActivityV2-registeredInstructors=Registered Instructors
report-sessionActivityV2-activeClasses=Active Classes
report-sessionActivityV2-registeredClasses=Registered Classes

#ADMIN
admin-site-management=Site Management
admin-data-migration=V1 Data Migration
admin-merge-dup-students=Merge Duplicate Students
admin-force-unique-classnames=Force Unique Classnames

#EMAILS
email-test-subject=This the subject for the Email Test
email-test-header=This is an Email Test
email-shared-closing=Thank you,
email-shared-closingSender=NROC Support

email-lostPassword-subject=Grade Service Password Reset
email-lostPassword-body1=You have been assigned a new password.
email-lostPassword-editSettingsLink=Click here to log into the gradeservice website where you can change this new password.
email-lostPassword-body2=If you have received this e-mail and you did not request that your password be reset, please contact your administrator immediately.
email-lostPassword-userName=User Name
email-lostPassword-password=New Password

email-lostPassword2-subject=Password reset for NROC Course Manager
email-lostPassword2-body0=Hello
email-lostPassword2-body1=We've received a request to reset the password on your NROC Course Manager account.
email-lostPassword2-body2=Username
email-lostPassword2-secureLink=Click here to securely reset your password.
email-lostPassword2-body3=This link is only valid for 12 hours. After that time, you will need to
email-lostPassword2-homeLink=request a new reset
email-lostPassword2-body4=If you did not initiate this request, we recommend that you complete the reset now or make a new request if this one is expired.
email-lostPassword2-body5=You may contact your local admin,
email-lostPassword2-body6=who can change your password manually. 
email-lostPassword2-body7=If you need assistance, feel free to
email-lostPassword2-ticketLink=contact us
email-lostPassword2-body8=Thank you,<br/>NROC Support

email-studentQuestion-subject=NROC Student Submitted Question
email-studentQuestion-body1=this is a question submitted by your student
email-studentQuestion-additionalInfo=Additional class, topic, and/or question information:
email-pendingInstructorReview-subject=Instructor Review(s) Pending
email-pendingInstructorReview-body1=You have been assigned an Instructor Review.
email-pendingInstructorReview-body2=to navigate to the Reviews tab to complete the assigned review.
email-pendingInstructorReview-loginLink=Login here
email-pendingInstructorReview-studentName=Student Name
email-pendingInstructorReview-unit=Unit
email-pendingInstructorReview-reviewType=Review Type
email-pendingInstructorReview-dateSubmitted=Date Submitted
email-courseRegistered-body=A class has been registered at NROC Course Manager.
email-courseRegistered-institution=Institution Name:
email-courseRegistered-location=Class Location:
email-courseRegistered-classType=Class Type:
email-courseRegistered-className=Class Name:
email-courseRegistered-courseId=Class ID:
email-courseRegistered-instructorName=Primary Instructor: 
email-courseRegistered-instructorEmail=Instructor's email:
email-courseRegistered-subject=Class Registered Notification

#MESSAGE PAGE
message-invalidKey-lti=Please edit your LTI settings for this type of object.  A valid consumer key and secret should have been provided.<br/><br/>Make sure to set your LTI Consumer (LMS) to use this key and secret for all objects of this type.<br/><br/>If you do not know your LTI credentials, please contact <a href="mailto:<EMAIL>?Subject=Registration%20Support" target="_blank"><EMAIL></a>
message-invalidKey-nonLti=Please edit the settings for this item and all Units in this class.<br/><br/>Change the URL to include the Key for your institution. Replace <b>{0}</b> with the Key provided for your Institution.<br/><br/>For example:<br/>If your key is <b>12r6Y4y770</b>, you should change the URL for this content item from:<br/>https://gradeservice.montereyinstitute.org/gradeservice/check_status.php?institutionKey=<b>{0}</b><br/>to:<br/>https://gradeservice.montereyinstitute.org/gradeservice/check_status.php?institutionKey=<b>12r6Y4y770</b><br/><br/>If you do not know your Registration Key, please contact <a href="mailto:<EMAIL>?Subject=Registration%20Support" target="_blank"><EMAIL></a>

#WEBSERVICE
webservice-success=success
webservice-failure=failure
webservice-status-1=Server Error
webservice-status-2=Invalid Query Parameters
webservice-status-3=Email Error
webservice-status-4=Invalid Student
webservice-status-5=Invalid Nonce
webservice-status-6=Invalid Writing Center

#LTI
ltiMessage-title=LTI Response
ltiMessage-invalidContext=The LTI context could not be established. The LMS administrator needs to verify the LTI settings.
ltiMessage-invalidCommand=An invalid command was sent: {0}
ltiMessage-invalidResource=A resource for this id does not exist: {0}
ltiMessage-noCommand=No command was sent.
ltiMessage-accessDenied=The reporting tool is only available to instructors.
ltiInvalidUnitLaunch-title=LTI Response - Invalid Unit Launch
ltiInvalidUnitLaunch-mainMessage=We could not gather all of the required launch data for the unit.  Consult with your LMS administrator to properly configure this LTI module.  The data we received was:
ltiFailedStudentRegistration-title=LTI Response - Student Registration Failed
ltiFailedStudentRegistration-mainMessage=This class is not registered. The instructor must go to <b>Instructor Resources &gt;&gt; Course Management and Reporting</b> to register this class.
ltiFailedStudentRegistration-inactiveStudent=This student has been marked inactive.
ltiFailedStudentRegistration-inactiveClass=This class has been marked inactive.

loginHelp-title=NROC Student Login Help
loginHelp-warning=We are unable to gather all of the required information for this session.
loginHelp-instructions=Please refer to this article for the proper browser settings:

#Dev English Activities
devEnglish-timeIn=Time in
devEnglish-home=Home
devEnglish-intro=Intro
devEnglish-introduction=Introduction
devEnglish-preReading=Pre-Reading
devEnglish-activeReading=Active Reader
devEnglish-postReading=Post-Reading
devEnglish-review=Review
devEnglish-preWriting=Pre-Writing
devEnglish-writingCenter=Writing Center
devEnglish-writingCenterGrade=Writing Center
devEnglish-journal=Journal
devEnglish-myJournal=My Journal
devEnglish-resources=Resources
devEnglish-ile=Exercise
devEnglish-lens-reading=Reading Lens
devEnglish-lens-audio=Audio Lens
devEnglish-lens-vocabulary=Vocabulary Lens
devEnglish-lens-grammar=Grammar Lens
devEnglish-step=Step
devEnglish-finalGrade=Final Grade
devEnglish-finalComment=Comment
devEnglish-invalid-wc-1=This is invalid WC placeholder text.
