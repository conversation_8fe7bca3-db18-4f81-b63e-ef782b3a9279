package org.montereyinstitute.model.writingcenter;

import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.*;

/**
 *
 * <AUTHOR>
 */
public class WritingCenterValidator {
    private static final Logger log = LogManager.getLogger(WritingCenterValidator.class);
    public static final int VALID = 0;
    public static final int INVALID_SCAFFOLD1 = -1;
    public static final int INVALID_SCAFFOLD2 = -2;
    public static final int INVALID_DOCUMENT_CONTENT = -3;
    public static final int INVALID_FINAL_DOCUMENT = -4;
    public static final int INVALID_STEP8 = -5;
    public static final int INVALID_INCOMING_DOCUMENT_ID = -6;
    public static final int INVALID_INCOMING_COMPLETED_DRAFT_STEP = -7;
    
    private HttpServletRequest request;
    private WritingCenterState wc;
    private int result = VALID;

    public WritingCenterValidator(HttpServletRequest request, WritingCenterState writingCenterState) {
        this.request = request;
        this.wc = writingCenterState;
    }
    
    public int preUpdateValidityTest(){
        if( (result = validateIncomingCompletedDraftStep()) < 0 ) return result;
        if( (result = validateIncomingDocumentId()) < 0 ) return result;
        return result;
    }
    
    public int postUpdateValidityTest(){
        if( (result = validateWritingCenter()) < 0 ) return result;
        return result;
    }
    
    private int validateWritingCenter(){
        if( wc.getCurrentStep() > 1 ){
            if( validateScaffold1() < 0 ){
                log.error("validateScaffold1 validation failed");
                return INVALID_SCAFFOLD1;
            }
            if( wc.getCurrentStep() >= 6 ){
                if( wc.getDocumentContent() == null || wc.getDocumentContent().isEmpty() ){
                    log.error("documentContent validation failed");
                    return INVALID_DOCUMENT_CONTENT;
                }

                if( wc.getCurrentStep() > 7 ){
                    if( wc.getFinalDocument() == null || wc.getFinalDocument().isEmpty() ){
                        log.error("finalDocument validation failed");
                        return INVALID_FINAL_DOCUMENT;
                    }
                    if( wc.getCurrentStep() > 8 ){
                        log.error("step > 8 validation failed");
                        return INVALID_STEP8;
                    }
                }
            }
        }
        return VALID;
    }
    
    private int validateScaffold1(){
        if( wc.getScaffold1State() == null || wc.getScaffold1State().isEmpty() ) return INVALID_SCAFFOLD1;
        for( ScaffoldState ss : wc.getScaffold1State() ){
            if( ss.getText() == null || ss.getText().isEmpty() ) return INVALID_SCAFFOLD1;
        }
        return VALID;
    }
    
    private int validateIncomingCompletedDraftStep(){
        //wcCompletedDraftStep was somehow submitted once when the user had advance beyond that.
        //I could not recreate that in the player, so I'm going to throw an excpeption here to ensure state doesn't get corrupted.
        String completedDraftStepString = request.getParameter("wcCompletedDraftStep");
        int completedDraftStep = -1;
        if( completedDraftStepString != null && !completedDraftStepString.isEmpty() && !completedDraftStepString.equals("final") ){
            try{
                completedDraftStep = Integer.parseInt(completedDraftStepString);
            }
            catch(Exception e){
                completedDraftStep = -1;
            }
            //These tests occur before state update, so currentStep has not yet been advanced.
            if( completedDraftStep < wc.getCurrentStep() ){
                log.error("INVALID WC: wcCompletedDraftStep: "+completedDraftStepString+", wc.getCurrentStep(): "+wc.getCurrentStep());
                return INVALID_INCOMING_COMPLETED_DRAFT_STEP;
            }
        }
        return VALID;
    }

    private int validateIncomingDocumentId(){
        //incomingDocument may have no id if the player has just constructed it from scaffolds.
        String document = request.getParameter("wcDocumentContent");
        if( document != null ){
            if( document.contains(TaglessDocumentProcessorIn.WCSTATE_ID_PREFIX)){
                if( !document.contains(TaglessDocumentProcessorIn.WCSTATE_ID_PREFIX+wc.getId())){
                    log.error("INVALID WC: WritingCenterState ID mismatch: "+wc.getId());
                    return INVALID_INCOMING_DOCUMENT_ID;
                }
            }
        }
        return VALID;
    }

    public int getResult() {
        return result;
    }
    
}
