package org.montereyinstitute.model;

import gs.hinkleworld.persistence.Hibernate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.*;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class StudentStatisticsForClassOfDevMath extends StudentStatisticsForClass {
    private static Logger log = LogManager.getLogger(StudentStatisticsForClassOfDevMath.class);

    private Float averageUnitPreassessmentScore;
    private Float averageTopicPreassessmentScore;
    private Float averageTopicWarmupScore;
    private Float averageTopicPracticeScore;
    private Float averageTopicReviewScore;
    private Integer topicsAttempted = 0;
    private Integer topicsMastered = 0;

    //For ClassSummaryReport it is more efficient to load only topicStates
    private List<TopicStateForStudentClass> topicStates;
    private Map<String,TopicStateForStudentClass> topicStatesMap; //key=topic id (e.g. U1L1T1, not topicState db id)


    public StudentStatisticsForClassOfDevMath(Student student, GSClass gsClass, CourseNode.Course appCourseNode) throws Throwable {
        super(student, gsClass, appCourseNode);
    }
    
    @Override
    public void init() throws Throwable{
        log.debug("init");
        Hibernate.clearSessionCache();
        initUnitStates(student.loadAllUnitStatesForOneClass(gsClass, appCourseNode));
        initTopicStates(student.loadAllTopicStatesForOneClass(gsClass, appCourseNode));
        unitStatisticsMap = new HashMap<String,StudentStatisticsForUnit>();
        for( UnitStateForStudentClass us : unitStates ){
            us.initTopicStatesFromListWithUnitCheck(appCourseNode, topicStates);
            if( us.getAppUnitNode() != null ){
                unitStatesMap.put(us.getAppUnitNode().getId(), us);
                StudentStatisticsForUnit unitStatistics = StudentStatisticsForUnit.getNewInstance(student, gsClass, appCourseNode, us);
                unitStatistics.init();
                unitStatisticsMap.put(us.getAppUnitNode().getId(), unitStatistics);
                us.setUnitStatistics(unitStatistics);
            }
        }
        initStatistics();
    }    
    
    public void initTopicStates(List<TopicStateForStudentClass> topicStates) throws Throwable{
        log.debug("initTopicStates");
        this.topicStates = topicStates;
        if( topicStates == null ) this.topicStates = new ArrayList<TopicStateForStudentClass>();
        topicStatesMap = new HashMap<String,TopicStateForStudentClass>();
        for( TopicStateForStudentClass ts : this.topicStates ){
            ts.initAppCourseNodes(appCourseNode);
            if( ts.getAppTopicNode() != null ) topicStatesMap.put(ts.getAppTopicNode().getId(), ts);
        }
    }    

    private void loadTopicStates() throws Throwable{
        log.debug("loadTopicStates");
        String hql = "from TopicStateForStudentClass ts"
                    +" where ts.student.studentId = '"+student.getStudentId().replaceAll("'", "''").replaceAll(":", "\\:")+"'"
                    +" and ts.gsClass.classId = "+gsClass.getClassId()
                    +" order by ts.unit";
        topicStates = Hibernate.getListHQL(hql);
        if( topicStates == null ) topicStates = new ArrayList<TopicStateForStudentClass>();
        topicStatesMap = new HashMap<String,TopicStateForStudentClass>();
        for( TopicStateForStudentClass ts : topicStates ){
            ts.initAppCourseNodes(appCourseNode);
            if( ts.getAppTopicNode() != null ) topicStatesMap.put(ts.getAppTopicNode().getId(), ts);
        }
    }    

    private void initStatistics() throws Throwable{
        log.debug("initStatisics");
        totalTime = 0F;
        
        Integer unitPreassessmentScoreTotal = 0;
        Integer unitPreassessmentScoreCount = 0;
        Integer preassessmentScoreTotal = 0;
        Integer preassessmentScoreCount = 0;
        Integer warmupScoreTotal = 0;
        Integer warmupScoreCount = 0;
        Integer practiceScoreTotal = 0;
        Integer practiceScoreCount = 0;
        Integer reviewScoreTotal = 0;
        Integer reviewScoreCount = 0;
        for( UnitStateForStudentClass us : unitStates ){
            if( us.getIsCompleted(appCourseNode) ) unitsCompleted++;
            
            Score unitPrescore = us.getScoreByDescription(Score.UnitScoreType.PREASSESSMENT.getDescription());
            if( unitPrescore != null ){
                unitPreassessmentScoreTotal += unitPrescore.getValue();
                unitPreassessmentScoreCount++;
            }
            for( TopicStateForStudentClass ts : us.getTopicStates() ){
                if( ts.getAttempts() != null && ts.getAttempts().intValue() > 0 ) topicsAttempted++;
                if( ts.getIsMastered() ) topicsMastered++;
                    
                Score preassessmentScore = ts.getScoreByDescription(Score.TopicScoreType.PREASSESSMENT.getDescription());
                if( preassessmentScore != null ){
                    preassessmentScoreTotal += preassessmentScore.getValue();
                    preassessmentScoreCount++;
                }
                Score warmupScore = ts.getScoreByDescription(Score.TopicScoreType.WARMUP.getDescription());
                if( warmupScore != null ){
                    warmupScoreTotal += warmupScore.getValue();
                    warmupScoreCount++;
                }
                Score practiceScore = ts.getScoreByDescription(Score.TopicScoreType.PRACTICE.getDescription());
                if( practiceScore != null ){
                    practiceScoreTotal += practiceScore.getValue();
                    practiceScoreCount++;
                }
                Score reviewScore = ts.getScoreByDescription(Score.TopicScoreType.REVIEW.getDescription());
                if( reviewScore != null ){
                    reviewScoreTotal += reviewScore.getValue();
                    reviewScoreCount++;
                }
                if( ts.getTotalTime() != null ){
                    totalTime += ts.getTotalTime();
                }
            }
        }
        if( unitPreassessmentScoreCount > 0 ) averageUnitPreassessmentScore = unitPreassessmentScoreTotal.floatValue() / unitPreassessmentScoreCount.floatValue();
        if( preassessmentScoreCount > 0 ) averageTopicPreassessmentScore = preassessmentScoreTotal.floatValue() / preassessmentScoreCount.floatValue();
        if( warmupScoreCount > 0 ) averageTopicWarmupScore = warmupScoreTotal.floatValue() / warmupScoreCount.floatValue();
        if( practiceScoreCount > 0 ) averageTopicPracticeScore = practiceScoreTotal.floatValue() / practiceScoreCount.floatValue();
        if( reviewScoreCount > 0 ) averageTopicReviewScore = reviewScoreTotal.floatValue() / reviewScoreCount.floatValue();
        log.debug("scores for student "+student.getStudentId());
        log.debug("averageUnitPreassessmentScore: "+averageUnitPreassessmentScore);
        log.debug("averageTopicPreassessmentScore: "+averageTopicPreassessmentScore);
        log.debug("averageTopicWarmupScore: "+averageTopicWarmupScore);
        log.debug("averageTopicPracticeScore: "+averageTopicPracticeScore);
        log.debug("averageTopicReviewScore: "+averageTopicReviewScore);
        log.debug("totalTime: "+totalTime);
    }
    
    public Float getAverageTopicReviewScore() {
        return averageTopicReviewScore;
    }

    public Float getAverageTopicPreassessmentScore() {
        return averageTopicPreassessmentScore;
    }

    public Float getTotalTime() {
        return totalTime;
    }

    public Float getAverageUnitPreassessmentScore() {
        return averageUnitPreassessmentScore;
    }

    public Float getAverageTopicWarmupScore() {
        return averageTopicWarmupScore;
    }

    public Float getAverageTopicPracticeScore() {
        return averageTopicPracticeScore;
    }

    public List<TopicStateForStudentClass> getTopicStates() {
        return topicStates;
    }

    public Map<String, TopicStateForStudentClass> getTopicStatesMap() {
        return topicStatesMap;
    }

    public Integer getTopicsAttempted() {
        return topicsAttempted;
    }

    public Integer getTopicsMastered() {
        return topicsMastered;
    }

}
