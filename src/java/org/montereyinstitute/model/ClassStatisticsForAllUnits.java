package org.montereyinstitute.model;

import java.util.*;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.Course;
import org.montereyinstitute.model.Score.TopicScoreType;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class ClassStatisticsForAllUnits {
    private static Logger log = LogManager.getLogger(ClassStatisticsForAllUnits.class);
 
    private Course appCourseNode;
    private GSClass gsClass;
    private Set<Student> students;
    private Map<Student, StudentStatisticsForClassOfDevMath> studentStatisticsMap = new LinkedHashMap<Student, StudentStatisticsForClassOfDevMath>();

    private Map<String, Float> unitReviewScoresMap = new HashMap<String, Float>(); //key=unit id

    public ClassStatisticsForAllUnits(GSClass gsClass, Set<Student> students, Course appCourseNode, Boolean includeInactiveStudents) throws Throwable {
        this.appCourseNode = appCourseNode;
        this.gsClass = gsClass;
        this.students = students;
        Map<Student, List<TopicStateForStudentClass>> topicStatesMap = gsClass.loadTopicStatesForStudentClassMapForAllUnits(appCourseNode);
        if( students != null ){
            for( Student s : students ){
                if( !s.getActive(gsClass) && !includeInactiveStudents ) continue;
                StudentStatisticsForClassOfDevMath studStats = (StudentStatisticsForClassOfDevMath) StudentStatisticsForClass.getNewInstance(s, gsClass, appCourseNode);
                studStats.initTopicStates(topicStatesMap.get(s)); //handles null with new empty list
                studentStatisticsMap.put(s, studStats);
            }
        }
        initStatistics();
    }

    private void initStatistics() {
        for( CourseNode appUnitNode : appCourseNode.getChildren() ){
            Integer reviewScoreTotal = 0;
            Integer reviewScoreCount = 0;
            for( StudentStatisticsForClassOfDevMath studStats : studentStatisticsMap.values() ){
                for( TopicStateForStudentClass ts : studStats.getTopicStates() ){
                    if( ts.getAppUnitNode() == null || !ts.getAppUnitNode().equals(appUnitNode) ) continue;
                    Score reviewScore = ts.getScoreByDescription(TopicScoreType.REVIEW.getDescription());
                    if( reviewScore != null ){
                        log.debug("reviewScore: "+reviewScore.getValue());
                        reviewScoreTotal += reviewScore.getValue();
                        reviewScoreCount++;
                    }
                }
            }
            Float averageTopicReviewScore = 0F;
            if( reviewScoreCount > 0 ) averageTopicReviewScore = reviewScoreTotal.floatValue() / reviewScoreCount.floatValue();
            log.debug("unit: "+appUnitNode.getId()+" -- averageTopicReviewScore: "+averageTopicReviewScore);
            unitReviewScoresMap.put(appUnitNode.getId(), averageTopicReviewScore);
        }
    }

    public GSClass getGsClass() {
        return gsClass;
    }

    public Set<Student> getStudents() {
        return students;
    }

    public Map<Student, StudentStatisticsForClassOfDevMath> getStudentStatisticsMap() {
        return studentStatisticsMap;
    }

    public Map<String, Float> getUnitReviewScoresMap() {
        return unitReviewScoresMap;
    }

}
