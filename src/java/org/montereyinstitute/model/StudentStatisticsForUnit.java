package org.montereyinstitute.model;

import org.apache.commons.lang3.time.DurationFormatUtils;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.CourseNode.Course;
import org.montereyinstitute.model.CourseNode.Unit;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public abstract class StudentStatisticsForUnit {
    private static Logger log = LogManager.getLogger(StudentStatisticsForUnit.class);

    protected GSClass gsClass;
    protected Student student;
    protected String courseId; //Key for AppModel.courses
    protected Unit appUnitNode;
    
    protected UnitStateForStudentClass unitState;
    protected Float totalTime;

    public static StudentStatisticsForUnit getNewInstance(Student student, GSClass gsClass, Course appCourseNode, UnitStateForStudentClass unitState) throws Throwable{
        if(gsClass.getCourseId().equals(Course.ID_DEV_MATH) || gsClass.getCourseId().equals(Course.ID_DEV_MATH_2)){
            return new StudentStatisticsForUnitOfDevMath(student, gsClass, appCourseNode, unitState);
        }
        if(gsClass.getCourseId().equals(Course.ID_DEV_ENGLISH)){
            return new StudentStatisticsForUnitOfDevEnglish(student, gsClass, appCourseNode, unitState);
        }
        throw new Exception("StudentStatisticsForUnit.getNewInstance: gsClass.getCourseId() did not match valid Course id constant: "+gsClass.getCourseId());
    }
    
    public StudentStatisticsForUnit(Student student, GSClass gsClass, Course appCourseNode, UnitStateForStudentClass unitState) throws Throwable{
        this.student = student;
        this.gsClass = gsClass;
        this.courseId = appCourseNode.getId();
        this.unitState = unitState;
        this.appUnitNode = (Unit) appCourseNode.getChildByNumber(unitState.getUnit());
    }

    public abstract void init();

    public String getTotalTimeFormatted() {
        Float millis = totalTime*60F*1000F;
        return DurationFormatUtils.formatDuration(millis.longValue(), "H:mm:ss");
    }

    public GSClass getGsClass() {
        return gsClass;
    }

    public Student getStudent() {
        return student;
    }

    public String getCourseType() {
        return courseId;
    }

    public Unit getAppUnitNode() {
        return appUnitNode;
    }

    public UnitStateForStudentClass getUnitState() {
        return unitState;
    }

    public Float getTotalTime() {
        return totalTime;
    }

}
