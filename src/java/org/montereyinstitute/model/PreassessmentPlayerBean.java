package org.montereyinstitute.model;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class PreassessmentPlayerBean {
    
    private String urlRoot;
    private String mathJaxUrl;
    private String playerLoadScriptSrc;
    private String playerUrl;
    private final List<String> styleHrefs = new ArrayList<String>();
    private final List<String> otherScriptSrcs = new ArrayList<String>();
    private String nrocMathScript;
    private String nrocMathAccessibleScript;

    private PreassessmentPlayerBean(){}
    public PreassessmentPlayerBean(String urlRoot, String mathJaxUrl){
        this.urlRoot = urlRoot.endsWith("/") ? urlRoot : urlRoot+"/";
        this.playerLoadScriptSrc = this.urlRoot+"NROCPreAssessment/NROCPreAssessment.nocache.js";
        this.playerUrl = this.urlRoot+"NROCPreAssessment.html";
        this.nrocMathScript = this.urlRoot+"scripts/NROCMath.js";
        this.nrocMathAccessibleScript = this.urlRoot+"scripts/NROCMathAccessible.js";
        this.mathJaxUrl = mathJaxUrl;
        if( mathJaxUrl != null ) otherScriptSrcs.add(mathJaxUrl);
        initStyleHrefs();
        initOtherScriptSrcs();
    }

    private void initStyleHrefs() {
        styleHrefs.add(urlRoot+"style/matching.css");
        styleHrefs.add(urlRoot+"style/graphs.css");
    }

    private void initOtherScriptSrcs() {
        otherScriptSrcs.add(urlRoot+"scripts/ios-drag-drop.js");
        otherScriptSrcs.add(urlRoot+"scripts/MatchingDragDrop.js");
        otherScriptSrcs.add(urlRoot+"scripts/MatchingDragDropAccessibility.js");
        otherScriptSrcs.add(urlRoot+"scripts/fabric.min.js");
        otherScriptSrcs.add(urlRoot+"scripts/FabGraph.js");
        otherScriptSrcs.add(urlRoot+"scripts/FabGraphTools.js");
        otherScriptSrcs.add(urlRoot+"scripts/FabGraphAccessibility.js");
        otherScriptSrcs.add(urlRoot+"scripts/FabGraphConfigDefaults.js");
    }

    public String getPlayerLoadScriptSrc() {
        return playerLoadScriptSrc;
    }

    public List<String> getStyleHrefs() {
        return styleHrefs;
    }

    public List<String> getOtherScriptSrcs() {
        return otherScriptSrcs;
    }

    public String getPlayerUrl() {
        return playerUrl;
    }

    public String getNrocMathScript() {
        return nrocMathScript;
    }

    public String getNrocMathAccessibleScript() {
        return nrocMathAccessibleScript;
    }

    @Override
    public String toString() {
        return "PreassessmentPlayerBean{" + "playerLoadScriptSrc=" + playerLoadScriptSrc + '}';
    }

    public String getMathJaxUrl() {
        return mathJaxUrl;
    }

    public void setMathJaxUrl(String mathJaxUrl) {
        this.mathJaxUrl = mathJaxUrl;
    }
    
}
