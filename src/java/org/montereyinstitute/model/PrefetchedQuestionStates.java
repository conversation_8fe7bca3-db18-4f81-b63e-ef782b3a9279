package org.montereyinstitute.model;

import gs.hinkleworld.persistence.Hibernate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.*;
import org.hibernate.SQLQuery;
import org.montereyinstitute.action.user.ViewStudentsAction;

/**
 *
 * <AUTHOR>
 */
public class PrefetchedQuestionStates{
    private static final Logger log = LogManager.getLogger(PrefetchedQuestionStates.class);
    
    protected final Map<Long, QuestionState> initialStatesByNAS = new HashMap<Long, QuestionState>();
    protected final Map<Long, List<QuestionState>> modifiedStatesByNAS = new HashMap<Long, List<QuestionState>>();

    public PrefetchedQuestionStates(Long classId) throws Throwable {
        log.debug("constructor");
        String template = "select {%s.*}, {qs.*} "
                + "from QuestionState as qs, UnitStateForStudentClass as us, NestingActivityState as nas "
                + "left join NestingActivityState as nas_c on nas.id = nas_c.parentStateTrackerId "
                + "left join NestingActivityState as nas_g on nas_c.id = nas_g.parentStateTrackerId "
                + "where nas.parentStateTrackerId = us.id and us.class_id = %d "
                + "and qs.contextStateId = %s.id "
                + "and qs.assessmentType = \"%s\" "
                + "order by qs.%s";
        String[] activityStates = {
            "nas", "nas_c", "nas_g"
        };
        
        executePrefetches(template, activityStates, classId, QuestionState.DevEnglishResponseType.DEV_ENGLISH_RESPONSE_INITIAL.name(), "id", false);
        executePrefetches(template, activityStates, classId, QuestionState.DevEnglishResponseType.DEV_ENGLISH_RESPONSE_MODIFIED.name(), "indexOfSet", false);
        log.debug("initial values: " + initialStatesByNAS.values().size());
        int modifiedValuesSize = 0;
        for(List<QuestionState> value: modifiedStatesByNAS.values()){
            modifiedValuesSize += value.size();
        }
        log.debug("modified value: " + modifiedValuesSize);
    }
    
    private void executePrefetches(String template, String[] activityStates, long classId, String assessmentType, String orderBy, boolean deleteDups) throws Throwable{
        log.debug("executePrefetches");
        Map map = deleteDups? initialStatesByNAS: modifiedStatesByNAS;
        List<QuestionState> dups = new ArrayList<QuestionState>();
        
        for(String activityState: activityStates){
            String statement = String.format(template, activityState, classId, activityState, assessmentType, orderBy);
            SQLQuery query = Hibernate.getNativeQuery(statement)
                    .addEntity(activityState, NestingActivityState.class)
                    .addEntity("qs", QuestionState.class);
            
            for(Object o: query.list()){
                Object[] result = (Object[])o;
                NestingActivityState nas = (NestingActivityState)result[0];
                Long nasId = nas.getId();
                QuestionState qs = (QuestionState)result[1];
                
                Object value = map.get(nasId);
                if(deleteDups){
                    if(value == null){
                        map.put(nasId, qs);
                    }
                    else{
                        dups.add(qs);
                    }
                }
                else{
                    List<QuestionState> list = (List<QuestionState>)value;
                    
                    if(list == null){
                        list = new ArrayList<QuestionState>();
                        map.put(nasId, list);
                    }
                    list.add(qs);
                }
            }
        }
        if(deleteDups){
            for(QuestionState qs: dups){
                log.info("DELETING DUP: " + qs);
                Hibernate.deleteHQL("QuestionState qs where qs.id = " + qs);
            }
        }
    }

}
