package org.montereyinstitute.model;

import org.apache.logging.log4j.*;
import static org.montereyinstitute.model.TopicStateForStudentClass.CSS_CLASS_BELOW_MASTERY;
import static org.montereyinstitute.model.TopicStateForStudentClass.CSS_CLASS_MASTERY;

/**
 *
 * <AUTHOR> Hinkleworld LLC
 */
public class StudentStatisticsForUnitOfDevMath extends StudentStatisticsForUnit {
    private static Logger log = LogManager.getLogger(StudentStatisticsForUnitOfDevMath.class);

    private Float averageTopicPreassessmentScore;
    private Float averageTopicWarmupScore;
    private Float averageTopicPracticeScore;
    private Float averageTopicReviewScore;
    
    public StudentStatisticsForUnitOfDevMath(Student student, GSClass gsClass, CourseNode.Course appCourseNode, UnitStateForStudentClass unitState) throws Throwable {
        super(student, gsClass, appCourseNode, unitState);
    }

    @Override
    public void init(){
        log.debug("init");
        totalTime = 0F;
        Integer preassessmentScoreTotal = 0;
        Integer preassessmentScoreCount = 0;
        Integer warmupScoreTotal = 0;
        Integer warmupScoreCount = 0;
        Integer practiceScoreTotal = 0;
        Integer practiceScoreCount = 0;
        Integer reviewScoreTotal = 0;
        Integer reviewScoreCount = 0;
        for( TopicStateForStudentClass ts : unitState.getTopicStates() ){
            Score preassessmentScore = ts.getScoreByDescription(Score.TopicScoreType.PREASSESSMENT.getDescription());
            if( preassessmentScore != null ){
                preassessmentScoreTotal += preassessmentScore.getValue();
                preassessmentScoreCount++;
            }
            Score warmupScore = ts.getScoreByDescription(Score.TopicScoreType.WARMUP.getDescription());
            if( warmupScore != null ){
                warmupScoreTotal += warmupScore.getValue();
                warmupScoreCount++;
            }
            Score practiceScore = ts.getScoreByDescription(Score.TopicScoreType.PRACTICE.getDescription());
            if( practiceScore != null ){
                practiceScoreTotal += practiceScore.getValue();
                practiceScoreCount++;
            }
            Score reviewScore = ts.getScoreByDescription(Score.TopicScoreType.REVIEW.getDescription());
            if( reviewScore != null ){
                reviewScoreTotal += reviewScore.getValue();
                reviewScoreCount++;
            }
            if( ts.getTotalTime() != null ){
                totalTime += ts.getTotalTime();
            }
        }
        if( preassessmentScoreCount > 0 ) averageTopicPreassessmentScore = preassessmentScoreTotal.floatValue() / preassessmentScoreCount.floatValue();
        if( warmupScoreCount > 0 ) averageTopicWarmupScore = warmupScoreTotal.floatValue() / warmupScoreCount.floatValue();
        if( practiceScoreCount > 0 ) averageTopicPracticeScore = practiceScoreTotal.floatValue() / practiceScoreCount.floatValue();
        if( reviewScoreCount > 0 ) averageTopicReviewScore = reviewScoreTotal.floatValue() / reviewScoreCount.floatValue();
        log.debug("averageTopicPreassessmentScore: "+averageTopicPreassessmentScore);
        log.debug("averageTopicWarmupScore: "+averageTopicWarmupScore);
        log.debug("averageTopicPracticeScore: "+averageTopicPracticeScore);
        log.debug("averageTopicReviewScore: "+averageTopicReviewScore);
        log.debug("totalTime: "+totalTime);
    }
    
    public Float getAverageTopicPreassessmentScore() {
        return averageTopicPreassessmentScore;
    }

    public Float getAverageTopicWarmupScore() {
        return averageTopicWarmupScore;
    }

    public Float getAverageTopicPracticeScore() {
        return averageTopicPracticeScore;
    }

    public Float getAverageTopicReviewScore() {
        return averageTopicReviewScore;
    }

    public String getAverageTopicReviewScoreCssClass(){
        if( gsClass.getThreshold() != null && getAverageTopicReviewScore() != null ){
            if( getAverageTopicReviewScore() < gsClass.getThreshold() ){
                return CSS_CLASS_BELOW_MASTERY;
            }
            return CSS_CLASS_MASTERY;
        }
        return null;
    }
    
    
}
