package org.montereyinstitute.model;

import com.hinkleworld.lib.DateUtil;
import gs.hinkleworld.core.AppUtil;
import org.montereyinstitute.util.DevEnglishReportUtil;
import gs.hinkleworld.persistence.Hibernate;
import gs.hinkleworld.core.SessionUtil;
import java.text.DecimalFormat;
import java.util.*;
import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.logging.log4j.*;
import org.montereyinstitute.model.Bookmark.UnitBookmarkType;
import org.montereyinstitute.model.CourseNode.*;
import org.montereyinstitute.model.Score.TopicScoreType;
import org.montereyinstitute.model.Score.UnitScoreType;
import static org.montereyinstitute.model.TopicStateForStudentClass.CSS_CLASS_BELOW_MASTERY;
import static org.montereyinstitute.model.TopicStateForStudentClass.CSS_CLASS_IN_PROGRESS;
import static org.montereyinstitute.model.TopicStateForStudentClass.CSS_CLASS_MASTERY;
import org.montereyinstitute.model.writingcenter.WritingCenterState;
import org.montereyinstitute.util.DevEnglishEmail;
import org.montereyinstitute.util.DevEnglishUtil;

/**
 *
 * <AUTHOR> Hinkle, Hinkleworld LLC
 */
@Entity
public class UnitStateForStudentClass extends StateTracker implements Comparable {
    private static final Logger log = LogManager.getLogger(UnitStateForStudentClass.class);
    private static final DecimalFormat decFormat = new DecimalFormat("0.#");

    public static enum ActionPending {
        //Student activity triggers a pending action...
        DevEnglishWritingCenterTeacherReviewNeeded(1), //value in actionPendingCodes is written as "wcReviewStep=3"
        DevEnglishWritingCenterPeerReviewsNeeded(2), //value in actionPendingCodes is written as "wcReviewStep=3"
        DevEnglishWritingCenterTeacherFinalGradeNeeded(3), //value in actionPendingCodes is written as "wcFinal"
        DevEnglishWritingCenterPeerReviewAssignedToThisStudent(4), //could be one or more assignments. used as simple flag to avoid unnecessary queries. value is not useful: "peerReviewAssignment".
        //Completion of action is coded as pending code+100...
        DevEnglishWritingCenterTeacherReviewComplete(101), //value in actionPendingCodes is written as "wcReviewStep=3"
        DevEnglishWritingCenterPeerReviewsComplete(102), //value in actionPendingCodes is written as "wcReviewStep=3"
        DevEnglishWritingCenterTeacherFinalGradeComplete(103); //value in actionPendingCodes is written as "wcFinal"
        
        private final int intCode;
        private ActionPending(int value){ intCode = value; }
        public int getIntCode(){ return intCode; }
    }
    
    public static final String ID_PARAM = "unitStateId";
    
    private static final Integer defaultPreattempts = 0;
    private static final Boolean defaultPreassessmentComplete = false;
    private static final Boolean defaultPreassessmentInProgress = false;
    private static final Boolean defaultActive = true;
    
    @ManyToOne(cascade={CascadeType.PERSIST, CascadeType.MERGE})
    @JoinColumn(name="student_id")
    private Student student;
    @ManyToOne(cascade={CascadeType.PERSIST, CascadeType.MERGE})
    @JoinColumn(name="class_id")
    private GSClass gsClass;

    @ElementCollection(fetch=FetchType.EAGER)
    private Map<Integer,String> actionPendingCodes; //Key=ActionPending enum intCode, Value=String useful in code context (e.g. 1(TeacherReview)=>"wcReviewStep=3", where reviewStep=3 contains the step to launch teacher review in player
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date teacherActionDateSubmitted;
    @Transient String actionUrl; //Optional link to action for JSP
    @Transient Integer actionValueInt; //Optional int value to prep for JSP. (e.g. the wcReviewStep)
    
    private Integer unit;
    private String nonce; //used to invalidate repeat connections from player
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date nonceTime;
    @Column(name="userAgent")
    private String userAgent;
    
    @Transient
    private Unit appUnitNode;
    
    //Dev Math Data
    private Integer preattempts;
    private Boolean preassessmentInProgress;
    private Boolean preassessmentComplete;
    private Boolean preassessmentLocked;
    private String randomCommaSep; 
   //Dev Math Transients
    @Transient
    private List<TopicStateForStudentClass> topicStates;
    @Transient
    private Map<String,TopicStateForStudentClass> topicStatesMap; //key=topic id (e.g. U1L2T3, not topicState db id)
    @Transient 
    private StudentStatisticsForUnit unitStatistics;
    //DevMath2 Preassessment additions, exactly parallel to those in TopicStateForStudentClass for TopicPlayer, used in webservice
    @Transient
    private List<QuestionState> questionStates;
    @Transient 
    private Map<Long,QuestionState> questionStatesMapByDbId;
    @Transient 
    private Map<String,QuestionState> questionStatesMapByConfigId;

    //Dev English Data
    private Integer attempts;
    private String formCode; //Currently forms are coded A or B. This specifies the question set used in a review (set A or set B)

    //Dev English Transients
    @Transient
    private List<NestingActivityState> unitActivityStates;
    @Transient
    private List<NestingActivityState> unitActivityStatesFlatList; //includes all descendants
    @Transient
    private UnitStateForClass unitStateForClass;
    @Transient
    private WritingCenterState writingCenterState;
    @Transient
    private Boolean hasLoadedDevEnglishTransients = false; //Some transients may still be null after loading, so I need to set this flag instead of checking for nulls.
    
    @Transient
    private PrefetchedQuestionStates prefetchedQuestionStates = null;

    public UnitStateForStudentClass() {}
    
    @Override
    public Activity getAssessmentActivityCourseNode(String assessmentType) {
        if( appUnitNode != null ) return appUnitNode.getPreassessment();
        return null;
    }

    public NestingActivityState getOrCreateUnitActivityStateByConfigId(Course appCourseNode, String configId) throws Throwable{
        if( unitActivityStatesFlatList == null ) loadOrCreateDevEnglishTransients(appCourseNode);
        if( unitActivityStatesFlatList != null ){
            for( NestingActivityState nas : unitActivityStatesFlatList ){
                if( nas.getConfigId() != null && nas.getConfigId().equals(configId) ) return nas;
            }
        }
        return null;
    }
    
    public NestingActivityState getOrLoadUnitActivityStateByConfigId(String configId) throws Throwable{
        if( unitActivityStatesFlatList == null ) loadDevEnglishTransients();
        if( unitActivityStatesFlatList != null ){
            for( NestingActivityState nas : unitActivityStatesFlatList ){
                if( nas.getConfigId() != null && nas.getConfigId().equals(configId) ) return nas;
            }
        }
        return null;
    }
    
    public NestingActivityState getUnitActivityStateByConfigId(String configId) throws Throwable{
        log.debug("getUnitActivityStateByConfigId: "+configId);
        if( unitActivityStatesFlatList != null ){
            for( NestingActivityState nas : unitActivityStatesFlatList ){
                log.debug("nas: "+nas.getId()+", "+nas.getConfigId());
                if( nas.getConfigId() != null && nas.getConfigId().equals(configId) ) return nas;
            }
        }
        else{
            log.debug("unitActivityStatesFlatList is null");        
        }
        return null;
    }
    
    public void generateNonce() throws Throwable{
        log.debug("generating nonce");
        nonce = RandomStringUtils.randomAlphanumeric(32);
        nonceTime = new Date();
        Hibernate.saveOrMerge(this);
    }
    
    public String getNonce(){
        return nonce;
    }
    
    public boolean validateNonce(String incomingNonce) throws Throwable{
        boolean valid = false;
        log.debug("nonce="+nonce+", incomingNonce="+incomingNonce+", nonceTime="+nonceTime);
        if( nonce != null && nonceTime != null && incomingNonce != null ){
            if( !DateUtil.hasPeriodOfDaysExpiredStrict(nonceTime, 1) ){
                if( nonce.equals(incomingNonce) ){
                    valid = true;
                }
                else{
                    log.warn("nonce mismatch");
                }
            }
            else{
                log.warn("nonce expired");
            }
        }
        nonce = null;
        nonceTime = null;
        Hibernate.saveOrMerge(this);
        return valid;
    }
    
    public void loadDevEnglishTransients()throws Throwable{
        loadDevEnglishTransients(false);
    }

    public void loadDevEnglishTransients(Boolean refresh)throws Throwable{
        log.debug("loadDevEnglishTransients: refresh="+refresh);
        if( unitActivityStatesFlatList == null || refresh){
            loadUnitActivityStates();
            if( unitActivityStates != null ){
                Collections.sort(unitActivityStates);
                unitActivityStatesFlatList = new ArrayList<NestingActivityState>();
                addToUnitActivityStatesFlatList(unitActivityStates, this);
            }
        }
        if( unitStateForClass == null || refresh ){
            loadUnitStateForClass();
        }
        if( writingCenterState == null || refresh ){
            loadWritingCenterState();
        }
        hasLoadedDevEnglishTransients = true;
    }
    
    public Boolean getHasLoadedDevEnglishTransients() {
        return hasLoadedDevEnglishTransients;
    }
    
    public void loadOrCreateDevEnglishTransients(Course appCourseNode)throws Throwable{
        loadOrCreateDevEnglishTransients(appCourseNode, false);
    }
    
    public void loadOrCreateDevEnglishTransients(Course appCourseNode, Boolean refresh)throws Throwable{
        log.debug("loadOrCreateDevEnglishTransients: refresh="+refresh);
        if( appUnitNode == null ) initAppUnitNode(appCourseNode);
        if( unitActivityStatesFlatList == null || refresh){
            if( !loadUnitActivityStates() ) createAllUnitActivityStates(appCourseNode);
            Collections.sort(unitActivityStates);
            unitActivityStatesFlatList = new ArrayList<NestingActivityState>();
            addToUnitActivityStatesFlatList(unitActivityStates, this);
        } 
        verifyLoadOrCreateUnitStateForClass(appCourseNode, false);
        verifyLoadOrCreateWritingCenterState(false);
        hasLoadedDevEnglishTransients = true;
    }
    
    public Boolean loadUnitStateForClass() throws Throwable{
        log.debug("loadUnitStateForClass");
        String hql = "from UnitStateForClass as usfc"
            +" where usfc.gsClass.id="+gsClass.getClassId()
            +" and usfc.unit="+unit;
        unitStateForClass = (UnitStateForClass) Hibernate.getObjectHQL(hql);
        log.debug("unitStateForClass: "+unitStateForClass);
        if( unitStateForClass == null ) return false;
        return true;
    }
    
    public Boolean loadUnitActivityStates() throws Throwable{
        log.debug("loadUnitActivityStates");
        String hql = "from NestingActivityState z"
                    +" where z.parent.id = "+this.getId();
        unitActivityStates = Hibernate.getListHQL(hql);
        if( unitActivityStates == null || unitActivityStates.isEmpty() ) return false;
        return true;
    }    
    
    public void verifyAndInsertMissingTransients(Course appCourseNode) throws Throwable{
        Boolean hasMissing = insertMissingUnitActivityStates(appCourseNode);
        if( appUnitNode == null ) initAppUnitNode(appCourseNode);
        if( hasMissing ){
            Collections.sort(unitActivityStates);
            unitActivityStatesFlatList = new ArrayList<NestingActivityState>();
            addToUnitActivityStatesFlatList(unitActivityStates, this);
        }
        verifyLoadOrCreateUnitStateForClass(appCourseNode, false);
        verifyLoadOrCreateWritingCenterState(false);
    }
    
    public void verifyLoadOrCreateUnitStateForClass(Course appCourseNode, Boolean refresh) throws Throwable{
        if( appUnitNode == null ) initAppUnitNode(appCourseNode);
        if( unitStateForClass == null || refresh ){
            unitStateForClass = UnitStateForClass.loadOrCreateUnitStateForClass(gsClass, unit, appCourseNode);
        }
    }
    
    public void verifyLoadOrCreateWritingCenterState(Boolean refresh) throws Throwable{
        if( writingCenterState == null || refresh ){
            //activityState for DEV_ENGLISH_WRITING_CENTER must be verified to exist prior to using this method.
            NestingActivityState wcActivityState = getOrLoadUnitActivityStateByConfigId(NestingActivityStateType.DEV_ENGLISH_WRITING_CENTER.name());
            if( wcActivityState != null ){
                writingCenterState = WritingCenterState.getFromDb(wcActivityState);
                if( writingCenterState == null ){
                    writingCenterState = WritingCenterState.createNewState(wcActivityState);
                }
            }
        }
        log.debug("writingCenterState: "+writingCenterState);
    }
    
    public void loadWritingCenterState() throws Throwable{
        NestingActivityState wcActivityState = getOrLoadUnitActivityStateByConfigId(NestingActivityStateType.DEV_ENGLISH_WRITING_CENTER.name());
        if( wcActivityState != null ){
            writingCenterState = WritingCenterState.getFromDb(wcActivityState);
            if( writingCenterState != null ) writingCenterState.initPeerReviewAssignmentMaps();
        }
        log.debug("writingCenterState: "+writingCenterState);
    }
    
    public void quickLoadWritingCenterState() throws Throwable{
        if( unitActivityStatesFlatList == null ) unitActivityStatesFlatList = new ArrayList<NestingActivityState>();
        NestingActivityState wcActivityState = NestingActivityState.getFromDb(this, NestingActivityStateType.DEV_ENGLISH_WRITING_CENTER, 1);
        if( wcActivityState != null ){
            wcActivityState.setParent(this);
            unitActivityStatesFlatList.add(wcActivityState);
            writingCenterState = WritingCenterState.getFromDb(wcActivityState);
            if( writingCenterState != null ) writingCenterState.initPeerReviewAssignmentMaps();
        }
        log.debug("writingCenterState: "+writingCenterState);
    }
    
    private void addToUnitActivityStatesFlatList(List<NestingActivityState> activityStates, StateTracker parent) throws Throwable{
        if( activityStates == null || activityStates.isEmpty() ){
            return;
        }
        for( NestingActivityState activityState : activityStates ){
            activityState.setParent(parent);
            if( activityState.getActivityType().equals(NestingActivityStateType.DEV_ENGLISH_QUESTION.name()) ||
                activityState.getActivityType().equals(NestingActivityStateType.DEV_ENGLISH_JOURNAL_MY_NOTES.name())
            ){
                if(prefetchedQuestionStates != null){
                    activityState.initialQuestionState = prefetchedQuestionStates.initialStatesByNAS.get(activityState.getId());
                    activityState.modifiedQuestionStates = prefetchedQuestionStates.modifiedStatesByNAS.get(activityState.getId());
                }
                else{
                    activityState.loadQuestionStates();
                }
            }
            unitActivityStatesFlatList.add(activityState);
            addToUnitActivityStatesFlatList(activityState.getOrderedChildren(), activityState);
        }
    }

    private Boolean insertMissingUnitActivityStates(Course appCourseNode) throws Throwable{
        log.debug("insertMissingUnitActivityStates");
        Boolean hasMissing = false;
        initAppUnitNode(appCourseNode);
        if( unitActivityStates == null ) loadOrCreateDevEnglishTransients(appCourseNode);
        if( appUnitNode == null ){
            log.warn("appUnitNode is null");
            return hasMissing;
        } 
        List<CourseNode> children = appUnitNode.getChildren();
        if( children == null ){
            log.warn("appUnitNode.getChildren is null");
            return hasMissing;
        }
        checkNode: for( CourseNode childNode : appUnitNode.getChildren() ){
            log.debug("Checking CourseNode: "+childNode);
            if( !(childNode instanceof Activity) ) continue;
            
            for( NestingActivityState existingState : unitActivityStates ){
                if( existingState.getConfigId().equals(childNode.getId())){
                    log.debug("Activity exists: "+existingState);
                    hasMissing = insertMissingNextLevelActivityStates(childNode, existingState);
                    continue checkNode;
                }
            }
            
            hasMissing = true;
            NestingActivityState newActivityState = new NestingActivityState();
            newActivityState.setActivityType(((Activity)childNode).getStateType().name());
            newActivityState.setConfigId(childNode.getId());
            newActivityState.setSiblingNumberForType(childNode.getNumber());
            newActivityState.setParent(this);
            log.debug("Creating Missing Activity: "+newActivityState);
            Hibernate.save(newActivityState);
            unitActivityStates.add(newActivityState);
            createNextLevelActivityStates(childNode, newActivityState);
        }
        return hasMissing;
    }
    
    private Boolean insertMissingNextLevelActivityStates(CourseNode parentNode, NestingActivityState existingParentState) throws Throwable{
        log.debug("insertMissingNextLevelActivityStates");
        Boolean hasMissing = false;
        if( parentNode.getChildren() == null || parentNode.getChildren().isEmpty() ) return hasMissing;
        checkNode: for( CourseNode childNode : parentNode.getChildren() ){
            log.debug("Checking CourseNode: "+childNode);
            if( !(childNode instanceof Activity) ) continue;
            
            Set<NestingActivityState> existingChildren = existingParentState.getChildren();
            if( existingChildren != null ){
                for( NestingActivityState existingState : existingChildren ){
                    if( existingState.getConfigId().equals(childNode.getId())){
                        log.debug("Activity exists: "+existingState);
                        continue checkNode;
                    }
                }
            }
            
            hasMissing = true;
            NestingActivityState newActivityState = new NestingActivityState();
            newActivityState.setActivityType(((Activity)childNode).getStateType().name());
            newActivityState.setConfigId(childNode.getId());
            newActivityState.setSiblingNumberForType(childNode.getNumber());
            log.debug("Creating Missing Activity: "+newActivityState);
            Hibernate.save(newActivityState);
            existingParentState.addChild(newActivityState);
            createNextLevelActivityStates(childNode, newActivityState);
        }
        return hasMissing;
    }
    
    private void createAllUnitActivityStates(Course appCourseNode) throws Throwable{
        log.debug("createAllUnitActivityStates");
        initAppUnitNode(appCourseNode);
        unitActivityStates = new ArrayList<NestingActivityState>();
        if( appUnitNode == null ){
            log.warn("appUnitNode is null");
            return;
        } 
        List<CourseNode> children = appUnitNode.getChildren();
        if( children == null ){
            log.warn("appUnitNode.getChildren is null");
            return;
        }
        for( CourseNode childNode : appUnitNode.getChildren() ){
            log.debug("Checking CourseNode: "+childNode);
            if( !(childNode instanceof Activity) ) continue;
            NestingActivityState activityState = new NestingActivityState();
            activityState.setActivityType(((Activity)childNode).getStateType().name());
            activityState.setConfigId(childNode.getId());
            activityState.setSiblingNumberForType(childNode.getNumber());
            activityState.setParent(this);
            log.debug("Creating Activity: "+activityState);
            Hibernate.save(activityState);
            unitActivityStates.add(activityState);
            createNextLevelActivityStates(childNode, activityState);
        }
    }
    
    private void createNextLevelActivityStates(CourseNode parentNode, NestingActivityState parentState) throws Throwable{
        log.debug("createNextLevelActivityStates");
        if( parentNode.getChildren() == null || parentNode.getChildren().isEmpty() ) return;
        for( CourseNode childNode : parentNode.getChildren() ){
            log.debug("Checking CourseNode: "+childNode);
            if( !(childNode instanceof Activity) ) continue;
            NestingActivityState activityState = new NestingActivityState();
            activityState.setActivityType(((Activity)childNode).getStateType().name());
            activityState.setConfigId(childNode.getId());
            activityState.setSiblingNumberForType(childNode.getNumber());
            log.debug("Creating Activity: "+activityState);
            Hibernate.save(activityState);
            parentState.addChild(activityState);
            createNextLevelActivityStates(childNode, activityState);
        }
    }
    
    public void loadTopicStates(Course appCourseNode) throws Throwable{
        log.debug("loadTopicStates");
        initAppUnitNode(appCourseNode);
        String hql = "from TopicStateForStudentClass ts"
                    +" where ts.student.studentId = '"+student.getStudentId().replaceAll("'", "''").replaceAll(":", "\\:")+"'"
                    +" and ts.gsClass.classId = "+gsClass.getClassId()
                    +" and ts.unit = "+unit
                    +" order by ts.unit, ts.lesson, ts.topic";
        topicStates = Hibernate.getListHQL(hql);
        if( topicStates == null ) topicStates = new ArrayList<TopicStateForStudentClass>();
        topicStatesMap = new HashMap<String,TopicStateForStudentClass>();
        for( TopicStateForStudentClass ts: topicStates ){
            ts.setParentUnitState(this);
            ts.initAppCourseNodes(appCourseNode);
            if( ts.getAppTopicNode() != null ){
                topicStatesMap.put(ts.getAppTopicNode().getId(), ts);
            }
        }
    }    

    public void initTopicStatesFromListWithUnitCheck(Course appCourseNode, List<TopicStateForStudentClass> possibleTopicStates) throws Throwable{
        initAppUnitNode(appCourseNode);
        topicStates = new ArrayList<TopicStateForStudentClass>();
        topicStatesMap = new HashMap<String,TopicStateForStudentClass>();
        for( TopicStateForStudentClass ts: possibleTopicStates ){
            if( ts.getUnit() == null || !ts.getUnit().equals(unit) ) continue;
            topicStates.add(ts);
            ts.setParentUnitState(this);
            ts.initAppCourseNodes(appCourseNode);
            if( ts.getAppTopicNode() != null ){
                topicStatesMap.put(ts.getAppTopicNode().getId(), ts);
                log.debug("topicState id="+ts.getId());
            }
        }
    }    

    public void loadNewOrExistingTopicStates(UnitStateForStudentClass unitState) throws Throwable{
        loadNewOrExistingTopicStates(unitState.getStudent(), unitState.getGsClass(), unitState.getAppUnitNode().getCourse(), unitState.getAppUnitNode());
    }
    
    public void loadNewOrExistingTopicStates(Student student, GSClass gsClass, Course appCourseNode, Unit appUnitNode) throws Throwable{
        topicStates = new ArrayList<TopicStateForStudentClass>();
        topicStatesMap = new HashMap<String,TopicStateForStudentClass>();
        for( CourseNode lesson : appUnitNode.getChildren() ){
            for( CourseNode topic : lesson.getChildren() ){
                TopicStateForStudentClass tssc = TopicStateForStudentClass.getNewOrExisting(student, gsClass, appUnitNode.getNumber(), lesson.getNumber(), topic.getNumber());
                tssc.setParentUnitState(this);
                tssc.initAppCourseNodes(appCourseNode);
                topicStates.add(tssc);
                topicStatesMap.put(tssc.getAppTopicNode().getId(), tssc);
            }
        }
    }
    
    public void verifyPreattemptsWithTopicsStates() throws Throwable{
        log.debug("verifyPreattemptsWithTopicsStates");
        if( preattempts == null || preattempts.equals(0) ){
            if( topicStates == null ) return;
            for( TopicStateForStudentClass ts : topicStates ){
                if( ts.getPreassessmentState() != null ){
                    log.debug("forcing preattempts to 1");
                    setPreattempts(1);
                    Hibernate.saveOrMerge(this);
                    return;
                }
            }
        }
    }
    
    public void initAppUnitNode(Course appCourseNode){
        appUnitNode = (Unit) appCourseNode.getChildByNumber(unit);
    }
    
    public static UnitStateForStudentClass getNewOrExisting(Student student, GSClass gsClass, Integer unit) throws Throwable{
        UnitStateForStudentClass state = loadFromDb(student, gsClass, unit);
        if( state == null ){
            log.debug("New state");
            state = new UnitStateForStudentClass();
            state.setGsClass(gsClass);
            state.setStudent(student);
            state.setUnit(unit);
            Random r = new Random();
            String randoms = r.nextFloat()+","+r.nextFloat()+","+r.nextFloat();
            state.setRandomCommaSep(randoms);
        }
        else{
            log.debug("Existing state: "+state.getId());
        }
        return state;
    }

    public static UnitStateForStudentClass loadFromDbWithRequestParam(HttpServletRequest request) throws Throwable{
        Long loadId = null;
        try{
            if( request.getParameter(ID_PARAM) != null ) loadId = Long.parseLong(request.getParameter(ID_PARAM));
        }
        catch(Exception e){
            log.warn(e, e);
        }
        return loadFromDb(loadId);
    }
    
    public static UnitStateForStudentClass loadFromDb(String idString) throws Throwable{
        Long id = Long.parseLong(idString);
        return loadFromDb(id);
    }
    
    public static UnitStateForStudentClass loadFromDb(Long id) throws Throwable{
        return (UnitStateForStudentClass) Hibernate.get(UnitStateForStudentClass.class, id);
    }
    
    public static UnitStateForStudentClass loadFromDb(Student student, GSClass gsClass, Integer unit) throws Throwable{
        String hql = "from UnitStateForStudentClass as us"
                    +" where us.gsClass.id="+gsClass.getClassId()
                    +" and us.student.id='"+student.getStudentId().replaceAll("'", "''").replaceAll(":", "\\:")+"'"
                    +" and us.unit="+unit;
        return (UnitStateForStudentClass) Hibernate.getObjectHQL(hql);
    }
    
    public Boolean getIsCompleted(Course appCourseNode) throws Throwable{
        if( appUnitNode == null ) initAppUnitNode(appCourseNode);
        if( appUnitNode == null ){
            log.warn("App Unit Node (external content) does not exist for this unit state. Unit Number = "+unit);
            return false;
        }
        if( gsClass != null ){
            if( gsClass.getIsDevMath() ){
                return getIsCompletedForDevMath(appCourseNode);
            }
            if( gsClass.getIsDevEnglish()){
                return getIsCompletedForDevEnglish(appCourseNode);
            }
        }
        throw new Exception("GSClass is not set for this UnitStateForStudentClass: "+this);
    }
    
    private Boolean getIsCompletedForDevMath(Course appCourseNode) throws Throwable{
        if( topicStates == null ) loadTopicStates(appCourseNode);
        Integer topicsTotal = 0;
        Integer topicsCompleted = 0;
        for( CourseNode lessonNode : appUnitNode.getChildren() ){
            for( CourseNode topicNode : lessonNode.getChildren() ){
                topicsTotal++;
                TopicStateForStudentClass topicState = topicStatesMap.get(topicNode.getId());
                if( topicState != null && topicState.getIsCompleted() ) topicsCompleted++;
            }
        }
        log.debug("topicsCompleted: "+topicsCompleted);
        log.debug("topicsTotal: "+topicsTotal);
        if( topicsCompleted.equals(topicsTotal) ) return true;
        return false;
    }
    
    private Boolean getIsCompletedForDevEnglish(Course appCourseNode) throws Throwable{
        if( unitStateForClass == null ) loadUnitStateForClass();
        if( unitStateForClass != null ){
            //Check other required acivities first
            if( !DevEnglishReportUtil.getActivityStatus(this, NestingActivityStateType.DEV_ENGLISH_ACTIVE_READING.name(), appUnitNode).equals(DevEnglishReportUtil.STATUS_COMPLETE) ){
                return false;
            }
            if( !DevEnglishReportUtil.getActivityStatus(this, NestingActivityStateType.DEV_ENGLISH_WRITING_CENTER.name(), appUnitNode).equals(DevEnglishReportUtil.STATUS_COMPLETE) ){
                return false;
            }
            //Check review
            Integer masteryLevel = unitStateForClass.getReviewMasteryLevel();
            Integer reviewScore = DevEnglishReportUtil.getReviewScore(this);
            log.debug("reviewScore: "+reviewScore);
            if( reviewScore != null && reviewScore >= masteryLevel ) return true;
        }
        return false;
    }

    public void loadAllQuestionStates() throws Throwable{
        questionStates = getQuestionStatesForThisContext();
    }
    
    public void initQuestionStatesMap(boolean refresh) throws Throwable{
        if( questionStates == null || refresh ) loadAllQuestionStates();
        if( questionStatesMapByDbId == null || questionStatesMapByConfigId == null || refresh ){
            questionStatesMapByDbId = new HashMap<Long,QuestionState>();
            questionStatesMapByConfigId = new HashMap<String,QuestionState>();
            for( QuestionState qs : questionStates ){
                questionStatesMapByDbId.put(qs.getId(), qs);
                questionStatesMapByConfigId.put(qs.getQuestionId(), qs);
            }
        }
    }
    
    public Float getPreassessmentScore(){
        if( topicStates != null ){
            Integer accumulatedScore = 0;
            Integer numberOfScores = 0;
            for( TopicStateForStudentClass topicState : topicStates ){
                if( topicState.getPreassessmentScore() != null ){
                    accumulatedScore += topicState.getPreassessmentScore();
                    numberOfScores++;
                }
            }
            if( numberOfScores > 0 ){
                return accumulatedScore.floatValue()/numberOfScores.floatValue();
            }
        }
        return null;
    }

    public String getPreassessmentScoreFormatted(){
        if( getPreassessmentScore() != null ){
            return decFormat.format(getPreassessmentScore());
        }
        if( getPreassessmentInProgress() ){
            return "#";
        }
        return "--";
    }

    public String getPreassessmentDateCompleted(){
        if( getPreassessmentComplete() ){
            TimeOnTask time = getTimeByDescription(TimeOnTask.DevMathUnitTimeType.PREASSESSMENT.getDescription());
            if( time != null ){
                return time.getModifiedTimeFormattedForDate();
            }
        }
        return "--";
    }
    
    public String getPreassessmentCssClass(){
        if( getPreassessmentInProgress() != null && getPreassessmentInProgress() ) return CSS_CLASS_IN_PROGRESS;
        if( gsClass.getThreshold() != null && getPreassessmentScore() != null ){
            if( getPreassessmentScore() < gsClass.getThreshold() ){
                return CSS_CLASS_BELOW_MASTERY;
            }
            return CSS_CLASS_MASTERY;
        }
        return null;
    }
    
    public Integer getSimScore(){
        if( getScoreByDescription(UnitScoreType.SIM.getDescription()) != null ){
            return getScoreByDescription(UnitScoreType.SIM.getDescription()).getValue();
        }
        return null;
    }

    public String getSimScoreFormatted(){
        if( getScoreByDescription(UnitScoreType.SIM.getDescription()) != null ){
            return getScoreByDescription(UnitScoreType.SIM.getDescription()).getValue().toString();
        }
        return "--";
    }

    public String getCurrentTabBookmarkFormatted(){
        if( getBookmarkByDescription(UnitBookmarkType.CURRENT_TAB.getDescription()) != null ){
            return getBookmarkByDescription(UnitBookmarkType.CURRENT_TAB.getDescription()).getValue().toString();
        }
        return "--";
    }
    
    public String getCurrentTopicBookmarkFormatted(){
        if( getBookmarkByDescription(UnitBookmarkType.CURRENT_TOPIC.getDescription()) != null ){
            return getBookmarkByDescription(UnitBookmarkType.CURRENT_TOPIC.getDescription()).getValue().toString();
        }
        return "--";
    }

    public Float getSimTime() {
        if( getTimeByDescription(TimeOnTask.DevMathUnitTimeType.SIM.getDescription()) != null ){
            return getTimeByDescription(TimeOnTask.DevMathUnitTimeType.SIM.getDescription()).getValue();
        }
        return null;
    }

    public String getSimTimeFormatted() {
        if( getTimeByDescription(TimeOnTask.DevMathUnitTimeType.SIM.getDescription()) != null ){
            return getTimeByDescriptionFormatted(TimeOnTask.DevMathUnitTimeType.SIM.getDescription());
        }
        return "--";
    }

    public String getSimDateCompleted(){
        if( getSimScore() != null ){
            TimeOnTask time = getTimeByDescription(TimeOnTask.DevMathUnitTimeType.SIM.getDescription());
            if( time != null ){
                return time.getModifiedTimeFormattedForDate();
            }
        }
        return "--";
    }
    
    public Double calculateScoreForLTI(Course appCourseNode) throws Throwable{
        if( gsClass.getCourseId().equals(Course.ID_DEV_MATH) || gsClass.getCourseId().equals(Course.ID_DEV_MATH_2) ){
            return calculateScoreForLTIForDevMath(appCourseNode);
        }
        if( gsClass.getCourseId().equals(Course.ID_DEV_ENGLISH) ){
            return calculateScoreForLTIForDevEnglish();
        }
        throw new Exception("UnitStateForStudentClass.calculateScoreForLTI: gsClass.getCourseId() did not match valid Course id constant: "+gsClass.getCourseId());
    }
    
    private Double calculateScoreForLTIForDevEnglish() throws Throwable{
        Integer reviewScore = DevEnglishReportUtil.getReviewScore(this);
        if( reviewScore != null ){
            return reviewScore.doubleValue()/100;
        }
        return 0D;
    }
    
    private Double calculateScoreForLTIForDevMath(Course appCourseNode) throws Throwable{
        if( topicStates == null ) loadTopicStates(appCourseNode);
        if( topicStates == null ) return 0D;
        Double total = 0D;
        Integer count = 0;
        for( TopicStateForStudentClass topicState : topicStates ){
            Score preassessmentScore = topicState.getScoreByDescription(TopicScoreType.PREASSESSMENT.getDescription());
            Score reviewScore = topicState.getScoreByDescription(TopicScoreType.REVIEW.getDescription());
            Double bestScore = 0D;
            if( preassessmentScore != null && preassessmentScore.getValue().doubleValue() > bestScore ) bestScore = preassessmentScore.getValue().doubleValue();
            if( reviewScore != null && reviewScore.getValue().doubleValue() > bestScore ) bestScore = reviewScore.getValue().doubleValue();
            total += bestScore;
            count++;
        }
        if( count > 0 ){
            return total/count/100;
        }
        return 0D;
    }
    
    //This was originally getUnitStateUseSession, but session caused big problems, so I renamed to "Never" as a reminder
    public static UnitStateForStudentClass getUnitStateNeverUseSession(HttpServletRequest request) throws Throwable{
        SessionUtil.purgeSessionSingleton(UnitStateForStudentClass.class, request);
        Long loadId = null;
        UnitStateForStudentClass unitState = null;
        try{
            if( request.getParameter(ID_PARAM) != null ) loadId = Long.parseLong(request.getParameter(ID_PARAM));
            unitState = (UnitStateForStudentClass) Hibernate.get(UnitStateForStudentClass.class, loadId);
        }
        catch(Exception e){
            log.warn(e, e);
        }
        return unitState;
    }

    public static final String ACTION_PENDING_SESSION_PARAM = "UnitStatesForActionPending"; 
    public static List<UnitStateForStudentClass> getUnitStatesForActionPendingUseSession(HttpServletRequest request, HttpServletResponse response, Set<GSClass> gsClasses, List<Integer> actionPendingCodes) throws Throwable{
        Course course = Course.getCourse(request, Course.ID_DEV_ENGLISH);
        List<UnitStateForStudentClass> unitStates = new ArrayList<UnitStateForStudentClass>();
        log.debug("gsClasses: "+gsClasses);
        if( gsClasses != null && !gsClasses.isEmpty() ){
            StringBuilder commaSepClassIds = new StringBuilder();
            boolean hasAtLeastOneDevEnglishClass = false;
            for( GSClass gsClass : gsClasses ){
                //Check only devEnglish classes
                if( gsClass.getCourseId() == null || !gsClass.getCourseId().equals(Course.ID_DEV_ENGLISH) ) continue;
                if( commaSepClassIds.length() > 0 ) commaSepClassIds.append(",");
                commaSepClassIds.append(gsClass.getClassId());
                hasAtLeastOneDevEnglishClass = true;
            }
            if( hasAtLeastOneDevEnglishClass ){
                StringBuilder commaSepActionPendingCodes = new StringBuilder();
                for( Integer code : actionPendingCodes ){
                    if( commaSepActionPendingCodes.length() > 0 ) commaSepActionPendingCodes.append(",");
                    commaSepActionPendingCodes.append(code);
                }
                String hql = "Select us from UnitStateForStudentClass as us"
                            +" join us.actionPendingCodes as apCode"
                            +" where us.gsClass.id in ("+commaSepClassIds+")"
                            +" and index(apCode) in ("+commaSepActionPendingCodes+")"
                            +" order by us.gsClass, us.unit";
                log.debug("hql: "+hql);
                unitStates = Hibernate.getListHQL(hql);
                log.debug("retrieved: "+unitStates);
//                List<UnitStateForStudentClass> removals = new ArrayList<UnitStateForStudentClass>();
                if( unitStates != null ){
                    for( UnitStateForStudentClass unitState : unitStates ){
                        unitState.initAppUnitNode(course);
                        Unit unit = unitState.getAppUnitNode();
//                        unitState.loadWritingCenterState();
                        //Perform additional init or removal on individual actionPendingCodes
                        if( unitState.getActionPendingCodes().containsKey(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode())){
                            String actionValue = unitState.getActionPendingCodes().get(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode());
                            //A teacher review actionValue is in the form "wcReviewStep=3"
                            String[] actionValueSplit = actionValue.split("=");
                            Integer actionValueInt = Integer.parseInt(actionValueSplit[1]);
                            Integer nextDraftStep = unitState.getAppUnitNode().getWritingCenterDraftStepFollowingReviewStep(actionValueInt);
/*
                            //Check to make sure student hasn't already moved beyond the draft step following the action step.
                            //This is an additional auditing check to clear any actions which are no longer meaningful
                            WritingCenterState wcState = unitState.getWritingCenterState();
                            if( wcState != null ){
                                if( wcState.isCompleteWithFinalDocument() || (nextDraftStep != null && wcState.getCurrentStep() > nextDraftStep) ){
                                    unitState.removeActionPendingCode(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode());
                                    Hibernate.save(unitState);
                                    removals.add(unitState);
                                    continue;
                                }
                            }
*/                          
                            unitState.setActionValueInt(actionValueInt);
                            log.debug("actionValueInt: "+unitState.getActionValueInt());
                            String actionUrl = DevEnglishUtil.getUnitLaunchRedirectUrlWithQueryString(request, response, unitState)+"&teacherMode=wcReview&wcReviewStep="+actionValueInt;
                            log.debug("actionUrl: "+actionUrl);
                            unitState.setActionUrl(actionUrl);
                        }
                        if( unitState.getActionPendingCodes().containsKey(ActionPending.DevEnglishWritingCenterTeacherFinalGradeNeeded.getIntCode())){
/*
                            //Remove any review steps. They are no longer meaningful.
                            if( unitState.getActionPendingCodes().containsKey(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode())){
                                unitState.removeActionPendingCode(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode());
                                Hibernate.save(unitState);
                                removals.add(unitState);
                                continue;
                            }
*/        
                            unitState.setActionValueInt(unit.getWritingCenterStepLabels().size()-2); //an extra step for "Complete" was added to step labels, so size-2 refers to last player step label
                            log.debug("actionValueInt: "+unitState.getActionValueInt());
                            String actionUrl = DevEnglishUtil.getUnitLaunchRedirectUrlWithQueryString(request, response, unitState)+"&teacherMode=wcFinal";
                            log.debug("actionUrl: "+actionUrl);
                            unitState.setActionUrl(actionUrl);
                        }
                    }
//                    log.debug("remove: "+removals);
//                    unitStates.removeAll(removals);
                }
            }
        }
        request.getSession().setAttribute(ACTION_PENDING_SESSION_PARAM, unitStates);
        log.debug("return: "+unitStates);
        return unitStates;
    }

    public Map<Student,UnitStateForStudentClass> getMapOfUnitStatesForSameUnitForAllStudentsInClass() throws Throwable{
        Map<Student,UnitStateForStudentClass> map = new HashMap<Student,UnitStateForStudentClass>();
        map.put(student, this);
        String hql = "from UnitStateForStudentClass as us"
                    +" where us.gsClass.id="+gsClass.getClassId()
                    +" and us.unit="+unit
                    +" and us.id != "+getId();
        log.debug("hql: "+hql);
        List<UnitStateForStudentClass> unitStates = Hibernate.getListHQL(hql);
        if( unitStates != null ){
            for( UnitStateForStudentClass us : unitStates ){
                map.put(us.getStudent(), us);
            }
        }
        return map;
    }
    
    public void checkForCompletedWritingCenterReviews(HttpServletRequest request) throws Throwable {
        //Check teacher reviews completed
        String actionValue = null;
        if( getActionPendingCodes() != null ){
            actionValue = getActionPendingCodes().get(ActionPending.DevEnglishWritingCenterTeacherReviewComplete.getIntCode());
        }
        if( actionValue != null ){
            removeActionPendingCode(ActionPending.DevEnglishWritingCenterTeacherReviewComplete.getIntCode());
            //Player must continue to block progress if peer reviews are still pending. Don't check for complete, because they may not have been required.
            String peerReviewPending = getActionPendingCodes().get(ActionPending.DevEnglishWritingCenterPeerReviewsNeeded.getIntCode());
            if( peerReviewPending == null ){
                //actionValue is in the form "wcReviewStep=3"
                String[] actionValueSplit = actionValue.split("=");
                Integer actionValueInt = Integer.parseInt(actionValueSplit[1]);
                request.setAttribute("writingCenterTeacherReviewComplete", writingCenterState.getCurrentStep());
            }
        }
        //Check peer reviews completed
        actionValue = getActionPendingCodes().get(ActionPending.DevEnglishWritingCenterPeerReviewsComplete.getIntCode());
        if( actionValue != null ){
            removeActionPendingCode(ActionPending.DevEnglishWritingCenterPeerReviewsComplete.getIntCode());
            //Player must continue to block progress if teacher review is still pending. Don't check for complete, because they may not have been required.
            String teacherReviewPending = getActionPendingCodes().get(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode());
            if( teacherReviewPending == null ){
                //actionValue is in the form "wcReviewStep=3"
                String[] actionValueSplit = actionValue.split("=");
                Integer actionValueInt = Integer.parseInt(actionValueSplit[1]);
                request.setAttribute("writingCenterPeerReviewComplete", writingCenterState.getCurrentStep());
            }
        }
        
    }
    
    public void checkForWritingCenterPeerReviewAssignments(HttpServletRequest request) throws Throwable {
        String actionValue = getActionPendingCodes().get(ActionPending.DevEnglishWritingCenterPeerReviewAssignedToThisStudent.getIntCode());
        if( actionValue != null ){
            request.setAttribute("writingCenterPeerReviewAssignedToThisStudent", true);
        }
    }
    
    public void addAttempts(Integer newAttempts){
        if( attempts == null ) attempts = newAttempts;
        else attempts += newAttempts;
        log.debug("new attempts total: "+attempts+" on "+this);
    }
    
    @Override
    public int compareTo(Object t) {
        UnitStateForStudentClass other = (UnitStateForStudentClass) t;
        return unit.compareTo(other.unit);
    }
    
    public Integer getUnit() {
        return unit;
    }

    public void setUnit(Integer unit) {
        this.unit = unit;
    }

    public Student getStudent() {
        return student;
    }

    public void setStudent(Student student) {
        this.student = student;
    }

    public GSClass getGsClass() {
        return gsClass;
    }

    public void setGsClass(GSClass gsClass) {
        this.gsClass = gsClass;
    }

    public Integer getPreattempts() {
        if( preattempts == null ) return defaultPreattempts;
        return preattempts;
    }

    public void setPreattempts(Integer preattempts) {
        this.preattempts = preattempts;
    }

    public Boolean getPreassessmentInProgress() {
        if( preassessmentInProgress == null ) return defaultPreassessmentInProgress;
        return preassessmentInProgress;
    }

    public void setPreassessmentInProgress(Boolean preassessmentInProgress) {
        this.preassessmentInProgress = preassessmentInProgress;
    }

    public Boolean getPreassessmentComplete() {
        if( preassessmentComplete == null ) return defaultPreassessmentComplete;
        return preassessmentComplete;
    }

    public void setPreassessmentComplete(Boolean preassessmentComplete) {
        this.preassessmentComplete = preassessmentComplete;
    }

    public List<TopicStateForStudentClass> getTopicStates() {
        return topicStates;
    }

    public Unit getAppUnitNode() {
        return appUnitNode;
    }

    public Map<String, TopicStateForStudentClass> getTopicStatesMap() {
        return topicStatesMap;
    }

    public void setTopicStatesMap(Map<String, TopicStateForStudentClass> topicStatesMap) {
        this.topicStatesMap = topicStatesMap;
    }

    public StudentStatisticsForUnit getUnitStatistics() {
        return unitStatistics;
    }

    public void setUnitStatistics(StudentStatisticsForUnit unitStatistics) {
        this.unitStatistics = unitStatistics;
    }

    public void setAppUnitNode(Unit appUnitNode) {
        this.appUnitNode = appUnitNode;
    }

    public Integer getAttempts() {
        if( attempts == null ) return 0;
        return attempts;
    }

    public void setAttempts(Integer attempts) {
        this.attempts = attempts;
    }

    public List<NestingActivityState> getUnitActivityStates() {
        return unitActivityStates;
    }

    public List<NestingActivityState> getUnitActivityStatesFlatList() {
        return unitActivityStatesFlatList;
    }

    public UnitStateForClass getUnitStateForClass() {
        return unitStateForClass;
    }

    public WritingCenterState getWritingCenterState() {
        return writingCenterState;
    }

    public String getFormCode() {
        return formCode;
    }

    public void setFormCode(String formCode) {
        this.formCode = formCode;
    }

    public Map<Integer, String> getActionPendingCodes() {
        return actionPendingCodes;
    }

    public void setActionPendingCodes(Map<Integer, String> actionPendingCodes) {
        this.actionPendingCodes = actionPendingCodes;
    }

    public void addActionPendingCode(Integer actionPendingCode, String actionValue, HttpServletRequest request, HttpServletResponse response){
        log.debug("addActionPendingCode: "+actionPendingCode+", "+actionValue);
        if( actionPendingCodes == null ) actionPendingCodes = new HashMap<Integer,String>();
        actionPendingCodes.put(actionPendingCode, actionValue);
        if(actionPendingCode == ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.intCode || 
           actionPendingCode == ActionPending.DevEnglishWritingCenterTeacherFinalGradeNeeded.intCode
        ){
            writingCenterState.updateDateSubmitted();
            if(actionPendingCode.equals(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode()) || actionPendingCode.equals(ActionPending.DevEnglishWritingCenterTeacherFinalGradeNeeded.getIntCode())){
                setTeacherActionDateSubmitted(new Date());
            }
            if( getGsClass() != null && getGsClass().getAllowEmailForInstructorReview() ){
                if( getGsClass().getTeacher() != null ){
                    try {
                        DevEnglishEmail.sendPendingInstructorReviewNotification(getGsClass().getTeacher(), request, response, this);
                    } catch (Throwable ex) {
                        log.error(ex, ex);
                    }
                }
            }
        }
    }
    
    private static final String REVIEWER_TEACHER = "teacher";
    private static final String REVIEWER_PEER = "peer";
    private static final String REVIEW_ORDER_1 = "r1";
    private static final String REVIEW_ORDER_N = "rN";
    public Boolean getHasDevEnglishTeacherReview1Pending(){
        return getHasReviewPending(REVIEWER_TEACHER, REVIEW_ORDER_1);
    }
    public Boolean getHasDevEnglishTeacherReviewNPending(){
        return getHasReviewPending(REVIEWER_TEACHER, REVIEW_ORDER_N);
    }
    public Boolean getHasDevEnglishPeerReview1Pending(){
        return getHasReviewPending(REVIEWER_PEER, REVIEW_ORDER_1);
    }
    public Boolean getHasDevEnglishPeerReviewNPending(){
        return getHasReviewPending(REVIEWER_PEER, REVIEW_ORDER_N);
    }
    private Boolean getHasReviewPending(String reviewer, String order){
        if( actionPendingCodes != null && getUnitStateForClass() != null ){
            Map<Integer, Integer> reviewSteps = null;
            if( reviewer.equals(REVIEWER_TEACHER) ) reviewSteps = getUnitStateForClass().getWritingCenterTeacherReviewSteps();
            else if( reviewer.equals(REVIEWER_PEER) ) reviewSteps = getUnitStateForClass().getWritingCenterPeerReviewSteps();
            if( reviewSteps != null ){
                String actionValue = null;
                if( reviewer.equals(REVIEWER_TEACHER) ) actionValue = actionPendingCodes.get(ActionPending.DevEnglishWritingCenterTeacherReviewNeeded.getIntCode());
                else if( reviewer.equals(REVIEWER_PEER) ) actionValue = actionPendingCodes.get(ActionPending.DevEnglishWritingCenterPeerReviewsNeeded.getIntCode());
                if( actionValue != null ){
                    String[] actionValueSplit = actionValue.split("=");
                    try{
                        Integer actionStepTarget = Integer.parseInt(actionValueSplit[1]);
                        Integer draftOneStep = appUnitNode.getWritingCenterDraftSteps().get(0);
                        for( Integer triggerStep : reviewSteps.keySet() ){
                            if( order.equals(REVIEW_ORDER_1) && !triggerStep.equals(draftOneStep) ) continue;
                            if( order.equals(REVIEW_ORDER_N) && triggerStep.equals(draftOneStep) ) continue;
                            if( reviewSteps.get(triggerStep).equals(actionStepTarget) ){
                                if( reviewer.equals(REVIEWER_PEER) ){
                                    return getWritingCenterState().areAnyOpenPeerReviewAssignmentsForDraftStepStillInValidTimePeriod(triggerStep, getUnitStateForClass());
                                }
                                else return true;
                            }
                        }
                    }
                    catch(Exception e){
                        log.warn("invalid actionValue: "+actionValue);
                    }
                }
            }
        }
        return false;
    }
    
    public Boolean getIsDevEnglishWritingCenterFinalGradeComplete(){
        if( actionPendingCodes != null ){
            if( actionPendingCodes.get(ActionPending.DevEnglishWritingCenterTeacherFinalGradeComplete.intCode) != null ) return true;
        }
        return false;
    }
    
    public void removeActionPendingCode(Integer actionPendingCode){
        log.debug("removeActionPendingCode: "+actionPendingCode);
        if( actionPendingCodes == null ){
            log.warn("WARNING: attempt to remove item from actionPendingCodes when collection is null.");
            return;
        }
        if( actionPendingCode.equals(ActionPending.DevEnglishWritingCenterTeacherFinalGradeComplete.intCode) ){
            log.warn("WARNING: cannot remove ActionPending.DevEnglishWritingCenterTeacherFinalGradeComplete.");
            return;
        }
        actionPendingCodes.remove(actionPendingCode);
    }

    public Map<Integer,Boolean> getWcDraftsCompletedMap(){
        Map<Integer,Boolean> draftsCompleted = new HashMap<Integer, Boolean>();
        if( appUnitNode != null && appUnitNode.getWritingCenterDraftSteps() != null ){
            for( Integer draftStep : appUnitNode.getWritingCenterDraftSteps() ){
                if( getWritingCenterState() != null && getWritingCenterState().getLastCompletedStep() >= draftStep ){
                    draftsCompleted.put(draftStep, true);
                }
                else{
                    draftsCompleted.put(draftStep, false);
                }
            }
        }
        log.debug("getWcDraftsCompletedMap: "+draftsCompleted);
        return draftsCompleted;
    }
    
    public String getActionUrl() {
        return actionUrl;
    }

    public void setActionUrl(String actionUrl) {
        log.debug("setActionUrl: "+actionUrl);
        this.actionUrl = actionUrl;
    }

    public Integer getActionValueInt() {
        return actionValueInt;
    }

    public void setActionValueInt(Integer actionValueInt) {
        this.actionValueInt = actionValueInt;
    }

    public PrefetchedQuestionStates getPrefetchedQuestionStates() {
        return prefetchedQuestionStates;
    }

    public void setPrefetchedQuestionStates(PrefetchedQuestionStates prefetchedQuestionStates) {
        this.prefetchedQuestionStates = prefetchedQuestionStates;
    }

    public Boolean getPreassessmentLocked() {
        if( preassessmentLocked == null ) return false;
        return preassessmentLocked;
    }

    public void setPreassessmentLocked(Boolean preassessmentLocked) {
        this.preassessmentLocked = preassessmentLocked;
    }

    public List<QuestionState> getQuestionStates() {
        return questionStates;
    }

    public Map<Long, QuestionState> getQuestionStatesMapByDbId() {
        return questionStatesMapByDbId;
    }

    public Map<String, QuestionState> getQuestionStatesMapByConfigId() {
        return questionStatesMapByConfigId;
    }

    public String getRandomCommaSep() {
        return randomCommaSep;
    }

    public void setRandomCommaSep(String randomCommaSep) {
        this.randomCommaSep = randomCommaSep;
    }

    public Date getTeacherActionDateSubmitted() {
        return teacherActionDateSubmitted;
    }

    public void setTeacherActionDateSubmitted(Date teacherActionDateSubmitted) {
        this.teacherActionDateSubmitted = teacherActionDateSubmitted;
    }

    public String getTeacherActionDateSubmittedFormatted() {
        if( getTeacherActionDateSubmitted() == null ) return "";
        return AppUtil.getDateOnlyFormat().format(getTeacherActionDateSubmitted());
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

}
