<?xml version="1.0" encoding="UTF-8"?>
<%@page contentType="application/xml" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gs" tagdir="/WEB-INF/tags/gs/"%>
<hw:locale />
<NROCGradeServiceResponse
    <c:choose>
        <c:when test="${WebServiceWorker.status == 0}">
            status="<hw:text key="webservice-success"/>"
        </c:when>
        <c:otherwise>
            status="<hw:text key="webservice-failure"/>"
            desc="<hw:text key="webservice-status-${WebServiceWorker.status}"/>"
        </c:otherwise>
    </c:choose>
>
    <c:if test="${WebServiceWorker.status == 0}">
        <c:if test="${not empty WebServiceWorker.student}">
            <student id="${WebServiceWorker.student.studentId}" />
        </c:if>
        <c:if test="${not empty WebServiceWorker.gsClass}">
            <gsClass classId="${WebServiceWorker.gsClass.classId}" />
        </c:if>
        <c:if test="${not empty WebServiceWorker.unitState}">
            <unitStateForStudentClass
                dbId="${WebServiceWorker.unitState.id}"
                studentId="${WebServiceWorker.unitState.student.studentId}"
                classId="${WebServiceWorker.unitState.gsClass.classId}"
                unitId="${WebServiceWorker.unitState.appUnitNode.id}"
                unitNumber="${WebServiceWorker.unitState.unit}"
            />
        </c:if>
        <gs:messageForDevEnglishWebservice/>
    </c:if>
</NROCGradeServiceResponse>
