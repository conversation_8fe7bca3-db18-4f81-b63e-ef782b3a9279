<?xml version="1.0" encoding="UTF-8"?>
<%@page contentType="application/xml" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gsfn" uri="/WEB-INF/tlds/gsFunctions" %>
<%@taglib prefix="gs" tagdir="/WEB-INF/tags/gs/"%>
<hw:locale />
<NROCGradeServiceResponse
    <c:choose>
        <c:when test="${WebServiceWorker.status == 0}">
            status="<hw:text key="webservice-success"/>"
        </c:when>
        <c:otherwise>
            status="<hw:text key="webservice-failure"/>"
            desc="<hw:text key="webservice-status-${WebServiceWorker.status}"/>"
        </c:otherwise>
    </c:choose>
>
    <c:if test="${WebServiceWorker.status == 0}">
        <c:if test="${not empty WebServiceWorker.student}">
            <student
                id="${WebServiceWorker.student.studentId}"
            >
            </student>
        </c:if>
        <c:if test="${not empty WebServiceWorker.gsClass}">
            <c:set var="gsClass" value="${WebServiceWorker.gsClass}"/>
            <gsClass
                classId="${gsClass.classId}"
                active="${gsClass.active}"
                threshold="${gsClass.threshold}"
                quickPreassessment="${gsClass.quickPreassessment}"
                disablePreassessment="${gsClass.disablePreassessment}"
                retakePreassessment="${gsClass.retakePreassessment}"
                retakeTopicReview="${gsClass.retakeTopicReview}"
                allowEmailHelp="${gsClass.addressVerifiedAllowEmailHelp}"
            >
            </gsClass>
        </c:if>
        <c:if test="${not empty WebServiceWorker.topicStateForClass}">
            <c:set var="topicStateForClass" value="${WebServiceWorker.topicStateForClass}"/>
            <topicStateForClass
                dbId="${topicStateForClass.id}"
                classId="${topicStateForClass.gsClass.classId}"
                topicId="${topicStateForClass.appTopicNode.id}"
                unitNumber="${topicStateForClass.unit}"
                lessonNumber="${topicStateForClass.lesson}"
                topicNumber="${topicStateForClass.topic}"
                cloaked="${topicStateForClass.cloaked}"
            >
            </topicStateForClass>
        </c:if>
        <c:if test="${not empty WebServiceWorker.topicStateForStudentClass}">
            <c:set var="topicStateForStudentClass" value="${WebServiceWorker.topicStateForStudentClass}"/>
            <topicStateForStudentClass
                dbId="${topicStateForStudentClass.id}"
                studentId="${topicStateForStudentClass.student.studentId}"
                classId="${topicStateForStudentClass.gsClass.classId}"
                topicId="${topicStateForStudentClass.appTopicNode.id}"
                unitNumber="${topicStateForStudentClass.unit}"
                lessonNumber="${topicStateForStudentClass.lesson}"
                topicNumber="${topicStateForStudentClass.topic}"
                attempts="${topicStateForStudentClass.attempts}"
                preassessmentState="${topicStateForStudentClass.preassessmentState}"
                practiceComplete="${topicStateForStudentClass.practiceComplete}"
                practiceInProgress="${topicStateForStudentClass.practiceInProgress}"
                warmupComplete="${topicStateForStudentClass.warmupComplete}"
                warmupInProgress="${topicStateForStudentClass.warmupInProgress}"
                reviewComplete="${topicStateForStudentClass.reviewComplete}"
                reviewInProgress="${topicStateForStudentClass.reviewInProgress}"
                presentationComplete="${topicStateForStudentClass.presentationComplete}"
                presentationInProgress="${topicStateForStudentClass.presentationInProgress}"
                topicTextComplete="${topicStateForStudentClass.topicTextComplete}"
                topicTextInProgress="${topicStateForStudentClass.topicTextInProgress}"
                workedExamplesPlayed="${topicStateForStudentClass.workedExamplesPlayed}"
                mastered="${topicStateForStudentClass.mastered}"
            >
            </topicStateForStudentClass>
        </c:if>
        <c:if test="${not empty WebServiceWorker.unitState}">
            <c:set var="unitState" value="${WebServiceWorker.unitState}"/>
            <c:if test="${not empty unitState.unitStateForClass}">
                <unitStateForClass
                    dbId="${unitState.unitStateForClass.id}"
                    classId="${unitState.unitStateForClass.gsClass.classId}"
                    unitNumber="${unitState.unitStateForClass.unit}"
                    readingAssignmentAudioLensOn="${unitState.unitStateForClass.readingAssignmentAudioLensOn}"
                    readingAssignmentVocabularyLensOn="${unitState.unitStateForClass.readingAssignmentVocabularyLensOn}"
                    readingAssignmentGrammarLensOn="${unitState.unitStateForClass.readingAssignmentGrammarLensOn}"
                    reviewMasteryLevel="${unitState.unitStateForClass.reviewMasteryLevel}"
                    reviewAllowRetake="${unitState.unitStateForClass.reviewAllowRetake}"
                    showScaffoldTips="${unitState.unitStateForClass.showScaffoldTips}"
                >
                </unitStateForClass>
            </c:if>
            <unitStateForStudentClass
                dbId="${unitState.id}"
                studentId="${unitState.student.studentId}"
                classId="${unitState.gsClass.classId}"
                unitId="${unitState.appUnitNode.id}"
                unitNumber="${unitState.unit}"
                preattemtps="${unitState.preattempts}"
                preassessmentComplete="${unitState.preassessmentComplete}"
                preassessmentInProgress="${unitState.preassessmentInProgress}"
                preassessmentScore="${unitState.preassessmentScoreFormatted}"
                bookmarkCurrentTab="${unitState.currentTabBookmarkFormatted}"
                bookmarkCurrentTopicId="${unitState.currentTopicBookmarkFormatted}"
                attempts="${unitState.attempts}"
                formCode="${unitState.formCode}"
            >
                <c:if test="${not empty WebServiceWorker.activityStates}">
                    <c:forEach var="activityState" items="${WebServiceWorker.activityStates}">
                        <unitActivityState
                            dbId="${activityState.id}"
                            configId="${activityState.configId}"
                            parentId="${activityState.parentId}"
                            activityType="${activityState.activityType}"
                            siblingNumberForType="${activityState.siblingNumberForType}"
                        >
                            <%-- These values are made to look like the DevEnglish startup. E.g. attempts are derived from number of questionState elements, not the attempts attribute --%>
                            <c:if test="${not empty activityState.firstAttempt}">
                                <questionState
                                    indexOfSet="0"
                                    questionId="${activityState.firstAttempt.questionId}"
                                    response="<c:out escapeXml="true" value="${activityState.firstAttempt.firstResponse}"/>"
                                    responseType="DEV_MATH_2_RESPONSE_INITIAL"
                                    attempts="0"
                                    responseCorrect="${activityState.firstAttempt.firstResponseCorrect}"
                                    responseSkipped="${activityState.firstAttempt.firstResponseSkipped}"
                                >
                                </questionState>
                            </c:if>
                            <c:if test="${not empty activityState.secondAttempt}">
                                <questionState
                                    indexOfSet="1"
                                    questionId="${activityState.secondAttempt.questionId}"
                                    response="<c:out escapeXml="true" value="${activityState.secondAttempt.response}"/>"
                                    responseType="DEV_MATH_2_RESPONSE_MODIFIED"
                                    attempts="0"
                                    responseCorrect="${activityState.firstAttempt.responseCorrect}"
                                    responseSkipped="${activityState.firstAttempt.responseSkipped}"
                                >
                                </questionState>
                            </c:if>
                        </unitActivityState>
                    </c:forEach>
                </c:if>
            </unitStateForStudentClass>
        </c:if>
    </c:if>
</NROCGradeServiceResponse>