<?xml version="1.0" encoding="UTF-8"?>
<%@page contentType="application/xml" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gsfn" uri="/WEB-INF/tlds/gsFunctions" %>
<%@taglib prefix="gs" tagdir="/WEB-INF/tags/gs/"%>
<hw:locale />
<NROCGradeServiceResponse
    <c:choose>
        <c:when test="${WebServiceWorker.status == 0}">
            status="<hw:text key="webservice-success"/>"
        </c:when>
        <c:otherwise>
            status="<hw:text key="webservice-failure"/>"
            desc="<hw:text key="webservice-status-${WebServiceWorker.status}"/>"
        </c:otherwise>
    </c:choose>
>
    <c:if test="${param.all}"><viewAll/></c:if>
    <c:if test="${WebServiceWorker.status == 0}">
        <c:if test="${empty questionStates}">
            <c:if test="${not empty WebServiceWorker.student}">
                <student
                    id="${WebServiceWorker.student.studentId}"
                >
                </student>
            </c:if>
            <c:if test="${not empty WebServiceWorker.gsClass}">
                <gsClass
                    classId="${WebServiceWorker.gsClass.classId}"
                    active="${WebServiceWorker.gsClass.active}"
                    threshold="${WebServiceWorker.gsClass.threshold}"
                    quickPreassessment="${WebServiceWorker.gsClass.quickPreassessment}"
                    disablePreassessment="${WebServiceWorker.gsClass.disablePreassessment}"
                    retakePreassessment="${WebServiceWorker.gsClass.retakePreassessment}"
                    retakeTopicReview="${WebServiceWorker.gsClass.retakeTopicReview}"
                    allowEmailHelp="${WebServiceWorker.gsClass.addressVerifiedAllowEmailHelp}"
                >
                    <c:if test="${not empty topicStatesForClass}">
                        <c:forEach var="topicStateForClass" items="${topicStatesForClass}">
                            <topicStateForClass
                                classId="${topicStateForClass.gsClass.classId}"
                                topicId="${topicStateForClass.appTopicNode.id}"
                                unitNumber="${topicStateForClass.unit}"
                                lessonNumber="${topicStateForClass.lesson}"
                                topicNumber="${topicStateForClass.topic}"
                                cloaked="${topicStateForClass.cloaked}"
                            >
                            </topicStateForClass>
                        </c:forEach>
                    </c:if>
                </gsClass>
            </c:if>
            <c:if test="${not empty WebServiceWorker.unitState}">
                <c:set var="unitState" value="${WebServiceWorker.unitState}"/>
                <c:if test="${not empty unitState.unitStateForClass}">
                    <unitStateForClass
                        dbId="${unitState.unitStateForClass.id}"
                        classId="${unitState.unitStateForClass.gsClass.classId}"
                        unitNumber="${unitState.unitStateForClass.unit}"
                        readingAssignmentAudioLensOn="${unitState.unitStateForClass.readingAssignmentAudioLensOn}"
                        readingAssignmentVocabularyLensOn="${unitState.unitStateForClass.readingAssignmentVocabularyLensOn}"
                        readingAssignmentGrammarLensOn="${unitState.unitStateForClass.readingAssignmentGrammarLensOn}"
                        reviewMasteryLevel="${unitState.unitStateForClass.reviewMasteryLevel}"
                        reviewAllowRetake="${unitState.unitStateForClass.reviewAllowRetake}"
                        showScaffoldTips="${unitState.unitStateForClass.showScaffoldTips}"
                    >
                        <c:if test="${not empty unitState.unitStateForClass.readingAssignmentTextModified}">
                            <readingAssignmentTextModified><c:out escapeXml="true" value="${unitState.unitStateForClass.readingAssignmentTextModified}"/></readingAssignmentTextModified>
                        </c:if>
                        <c:if test="${not empty unitState.unitStateForClass.writingCenterSelfReviewSteps}">
                            <writingCenterSelfReviewSteps>
                                <c:set var="reviewStepsMap" value="${unitState.unitStateForClass.writingCenterSelfReviewSteps}"/>
                                <c:forEach var="reviewStepsMapEntry" items="${reviewStepsMap}">
                                    <step draft="${reviewStepsMapEntry.key}" review="${reviewStepsMapEntry.value}"/>
                                </c:forEach>
                            </writingCenterSelfReviewSteps>
                        </c:if>
                        <c:if test="${not empty unitState.unitStateForClass.writingCenterTeacherReviewSteps}">
                            <writingCenterTeacherReviewSteps>
                                <c:set var="reviewStepsMap" value="${unitState.unitStateForClass.writingCenterTeacherReviewSteps}"/>
                                <c:forEach var="reviewStepsMapEntry" items="${reviewStepsMap}">
                                    <step draft="${reviewStepsMapEntry.key}" review="${reviewStepsMapEntry.value}"/>
                                </c:forEach>
                            </writingCenterTeacherReviewSteps>
                        </c:if>
                        <c:if test="${not empty unitState.unitStateForClass.writingCenterPeerReviewSteps and unitState.unitStateForClass.numberOfPeerReviews > 0}">
                            <writingCenterPeerReviewSteps>
                                <c:set var="reviewStepsMap" value="${unitState.unitStateForClass.writingCenterPeerReviewSteps}"/>
                                <c:forEach var="reviewStepsMapEntry" items="${reviewStepsMap}">
                                    <step draft="${reviewStepsMapEntry.key}" review="${reviewStepsMapEntry.value}"/>
                                </c:forEach>
                            </writingCenterPeerReviewSteps>
                        </c:if>
                        <c:if test="${not empty unitState.unitStateForClass.writingCenterPreInstructionsMessage}">
                            <writingCenterPreInstructionsMessage><c:out escapeXml="true" value="${unitState.unitStateForClass.writingCenterPreInstructionsMessage}"/></writingCenterPreInstructionsMessage>
                        </c:if>
                        <c:if test="${unitState.unitStateForClass.showScaffoldTips and not empty unitState.unitStateForClass.writingCenterScaffoldTips}">
                            <writingCenterScaffoldTips>
                                <c:forEach var="scaffoldTip" items="${unitState.unitStateForClass.writingCenterScaffoldTips}">
                                    <scaffoldTip scaffoldId="${scaffoldTip.key}"><c:out escapeXml="true" value="${scaffoldTip.value}"/></scaffoldTip>
                                </c:forEach> 
                            </writingCenterScaffoldTips>
                        </c:if>
                    </unitStateForClass>
                </c:if>
                <unitStateForStudentClass
                    dbId="${unitState.id}"
                    studentId="${unitState.student.studentId}"
                    classId="${unitState.gsClass.classId}"
                    unitId="${unitState.appUnitNode.id}"
                    unitNumber="${unitState.unit}"
                    preattemtps="${unitState.preattempts}"
                    preassessmentComplete="${unitState.preassessmentComplete}"
                    preassessmentInProgress="${unitState.preassessmentInProgress}"
                    preassessmentScore="${unitState.preassessmentScoreFormatted}"
                    bookmarkCurrentTab="${unitState.currentTabBookmarkFormatted}"
                    bookmarkCurrentTopicId="${unitState.currentTopicBookmarkFormatted}"
                    attempts="${unitState.attempts}"
                    formCode="${unitState.formCode}"
                >
                    <c:if test="${WebServiceWorker.gsClass.courseId == 'devEnglish'}">
                        <devEnglishProgress
                            intro="${gsfn:devEnglishActivityStatus(unitState, 'DEV_ENGLISH_INTRO', WebServiceWorker.appUnitNode)}"
                            preReading="${gsfn:devEnglishActivityStatus(unitState, 'DEV_ENGLISH_PRE_READING', WebServiceWorker.appUnitNode)}"
                            activeReading="${gsfn:devEnglishActivityStatus(unitState, 'DEV_ENGLISH_ACTIVE_READING', WebServiceWorker.appUnitNode)}"
                            postReading="${gsfn:devEnglishActivityStatus(unitState, 'DEV_ENGLISH_POST_READING', WebServiceWorker.appUnitNode)}"
                            review="${gsfn:devEnglishReviewStatusWithMasteryCheck(unitState)}"
                            preWriting="${gsfn:devEnglishActivityStatus(unitState, 'DEV_ENGLISH_PRE_WRITING', WebServiceWorker.appUnitNode)}"
                            writingCenter="${gsfn:devEnglishActivityStatus(unitState, 'DEV_ENGLISH_WRITING_CENTER', WebServiceWorker.appUnitNode)}"
                        />
                    </c:if>
                    <c:forEach var="topicStateForStudentClass" items="${unitState.topicStates}">
                        <topicStateForStudentClass
                            studentId="${topicStateForStudentClass.student.studentId}"
                            classId="${topicStateForStudentClass.gsClass.classId}"
                            topicId="${topicStateForStudentClass.appTopicNode.id}"
                            unitNumber="${topicStateForStudentClass.unit}"
                            lessonNumber="${topicStateForStudentClass.lesson}"
                            topicNumber="${topicStateForStudentClass.topic}"
                            attempts="${topicStateForStudentClass.attempts}"
                            preassessmentState="${topicStateForStudentClass.preassessmentState}"
                            practiceComplete="${topicStateForStudentClass.practiceComplete}"
                            practiceInProgress="${topicStateForStudentClass.practiceInProgress}"
                            warmupComplete="${topicStateForStudentClass.warmupComplete}"
                            warmupInProgress="${topicStateForStudentClass.warmupInProgress}"
                            reviewComplete="${topicStateForStudentClass.reviewComplete}"
                            reviewInProgress="${topicStateForStudentClass.reviewInProgress}"
                            preassessmentScore="${topicStateForStudentClass.preassessmentScoreFormatted}"
                            warmupScore="${topicStateForStudentClass.warmupScoreFormatted}"
                            practiceScore="${topicStateForStudentClass.practiceScoreFormatted}"
                            bookmarkCurrentTab="${topicStateForStudentClass.currentTabBookmarkFormatted}"
                            reviewScore="${topicStateForStudentClass.reviewScoreFormatted}"
                        >
                        </topicStateForStudentClass>
                    </c:forEach>
                    <c:forEach var="unitActivityState" items="${unitState.unitActivityStatesFlatList}">
                        <unitActivityState
                            dbId="${unitActivityState.id}"
                            configId="${unitActivityState.configId}"
                            parentId="${unitActivityState.parent.id}"
                            activityType="${unitActivityState.activityType}"
                            siblingNumberForType="${unitActivityState.siblingNumberForType}"
                        >
                            <c:if test="${not empty unitActivityState.initialQuestionState}">
                                <questionState
                                    indexOfSet="${unitActivityState.initialQuestionState.indexOfSet}"
                                    questionId="${unitActivityState.initialQuestionState.questionId}"
                                    response="<c:out escapeXml="true" value="${unitActivityState.initialQuestionState.response}"/>"
                                    responseType="${unitActivityState.initialQuestionState.assessmentType}"
                                    attempts="${unitActivityState.initialQuestionState.attempts}"
                                >
                                </questionState>
                                <c:forEach var="questionState" items="${unitActivityState.modifiedQuestionStates}">
                                    <questionState
                                        indexOfSet="${questionState.indexOfSet}"
                                        questionId="${questionState.questionId}"
                                        response="<c:out escapeXml="true" value="${questionState.response}"/>"
                                        responseType="${questionState.assessmentType}"
                                        attempts="${questionState.attempts}"
                                    >
                                    </questionState>
                                </c:forEach>
                            </c:if>
                        </unitActivityState>
                    </c:forEach>
                    <c:forEach var="annotationState" items="${annotationStates}">
                        <annotationState
                            dbId="${annotationState.id}"
                            startElementId="${annotationState.startElementId}"    
                            endElementId="${annotationState.endElementId}"    
                            selectedText="<c:out escapeXml="true" value="${annotationState.selectedText}"/>"
                            commentText="<c:out escapeXml="true" value="${annotationState.commentText}"/>"
                            annotationQuestionId="${annotationState.annotationQuestionId}"
                        >
                        </annotationState>
                    </c:forEach>
                    <c:if test="${not empty unitState.writingCenterState}">
                        <writingCenterState
                            dbId="${unitState.writingCenterState.id}"
                            currentStep="${unitState.writingCenterState.currentStep}"
                            documentContent="<c:out escapeXml="true" value="${unitState.writingCenterState.documentContent}"/>"
                            finalDocument="<c:out escapeXml="true" value="${unitState.writingCenterState.finalDocument}"/>"
                            review1Pending="${unitState.hasDevEnglishTeacherReview1Pending or unitState.hasDevEnglishPeerReview1Pending}"
                            reviewNPending="${unitState.hasDevEnglishTeacherReviewNPending or unitState.hasDevEnglishPeerReviewNPending}"
                        >
                            <c:forEach var="scaffoldState" items="${unitState.writingCenterState.scaffold1State}">
                                <scaffold1State
                                    dbId="${scaffoldState.id}"
                                    configId="${scaffoldState.configId}"
                                    text="<c:out escapeXml="true" value="${scaffoldState.text}"/>"
                                >
                                </scaffold1State>
                            </c:forEach>
                            <c:forEach var="scaffoldState" items="${unitState.writingCenterState.scaffold2State}">
                                <scaffold2State
                                    dbId="${scaffoldState.id}"
                                    configId="${scaffoldState.configId}"
                                    text="<c:out escapeXml="true" value="${scaffoldState.text}"/>"
                                >
                                </scaffold2State>
                            </c:forEach>
                            <c:forEach var="reviewState" items="${unitState.writingCenterState.review1State}">
                                <review1State
                                    dbId="${reviewState.id}"
                                    configId="${reviewState.configId}"
                                    text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                    yes="${reviewState.yes}"
                                    type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                    liked="${reviewState.liked}"
                                    collapsed="${reviewState.collapsed}"
                                    reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                >
                                </review1State>
                            </c:forEach>
                            <c:forEach var="reviewState" items="${unitState.writingCenterState.reviewNState}">
                                <reviewNState
                                    dbId="${reviewState.id}"
                                    configId="${reviewState.configId}"
                                    text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                    yes="${reviewState.yes}"
                                    type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                    liked="${reviewState.liked}"
                                    collapsed="${reviewState.collapsed}"
                                    reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                >
                                </reviewNState>
                            </c:forEach>
                            <c:if test="${includeReview1TeacherState or param.all}">
                                <c:forEach var="reviewState" items="${unitState.writingCenterState.review1TeacherState}">
                                    <review1TeacherState
                                        dbId="${reviewState.id}"
                                        configId="${reviewState.configId}"
                                        text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                        yes="${reviewState.yes}"
                                        type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                        liked="${reviewState.liked}"
                                        collapsed="${reviewState.collapsed}"
                                        reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                    >
                                    </review1TeacherState>
                                </c:forEach>
                            </c:if>
                            <c:if test="${includeReviewNTeacherState or param.all}">
                                <c:forEach var="reviewState" items="${unitState.writingCenterState.reviewNTeacherState}">
                                    <reviewNTeacherState
                                        dbId="${reviewState.id}"
                                        configId="${reviewState.configId}"
                                        text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                        yes="${reviewState.yes}"
                                        type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                        liked="${reviewState.liked}"
                                        collapsed="${reviewState.collapsed}"
                                        reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                    >
                                    </reviewNTeacherState>
                                </c:forEach>
                            </c:if>
                            <c:if test="${includeReviewFinalTeacherState or param.all}">
                                <c:forEach var="reviewState" items="${unitState.writingCenterState.reviewFinalTeacherState}">
                                    <reviewFinalTeacherState
                                        dbId="${reviewState.id}"
                                        configId="${reviewState.configId}"
                                        text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                        yes="${reviewState.yes}"
                                        type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                        liked="${reviewState.liked}"
                                        collapsed="${reviewState.collapsed}"
                                        reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                    >
                                    </reviewFinalTeacherState>
                                </c:forEach>
                                <c:if test="${not empty unitState.writingCenterState.gradedFinalDocument}">
                                    <gradedFinalDocument><c:out value="${unitState.writingCenterState.gradedFinalDocument}"/></gradedFinalDocument>
                                </c:if>
                                <c:if test="${not empty unitState.writingCenterState.finalGrade}">
                                    <finalGrade><c:out value="${unitState.writingCenterState.finalGrade}"/></finalGrade>
                                </c:if>
                            </c:if>
                            <c:forEach var="reviewState" items="${unitState.writingCenterState.review1PeerStateSorted}">
                                <review1PeerState
                                    dbId="${reviewState.id}"
                                    configId="${reviewState.configId}"
                                    text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                    yes="${reviewState.yes}"
                                    type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                    liked="${reviewState.liked}"
                                    collapsed="${reviewState.collapsed}"
                                    reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                >
                                </review1PeerState>
                            </c:forEach>
                            <c:forEach var="reviewState" items="${unitState.writingCenterState.reviewNPeerState}">
                                <reviewNPeerState
                                    dbId="${reviewState.id}"
                                    configId="${reviewState.configId}"
                                    text="<c:out escapeXml="true" value="${reviewState.text}"/>"
                                    yes="${reviewState.yes}"
                                    type="<c:out escapeXml="true" value="${reviewState.type}"/>"
                                    liked="${reviewState.liked}"
                                    collapsed="${reviewState.collapsed}"
                                    reviewerId="<c:out escapeXml="true" value="${reviewState.reviewerId}"/>"
                                >
                                </reviewNPeerState>
                            </c:forEach>
                        </writingCenterState>
                    </c:if>
                </unitStateForStudentClass>
            </c:if>
        </c:if>
        <c:if test="${not empty questionStates and not empty activityAppNode}">
            <questionStatesForAssessment activityId="${activityAppNode.id}">
                <c:forEach var="questionState" items="${questionStates}">
                    <questionState
                        assessmentType="${questionState.assessmentType}"
                        indexOfSet="${questionState.indexOfSet}"
                        questionId="${questionState.questionId}"
                        response="<c:out escapeXml="true" value="${questionState.response}"/>"
                        attempts="${questionState.attempts}"
                    >
                    </questionState>
                </c:forEach>
            </questionStatesForAssessment>
        </c:if>
        <gs:messageForDevEnglishWebservice/>
    </c:if>
</NROCGradeServiceResponse>