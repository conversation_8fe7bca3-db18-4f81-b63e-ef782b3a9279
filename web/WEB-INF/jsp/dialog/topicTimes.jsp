<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<table id="dialogTopicTimes" cellpadding="0" cellspacing="0">
    <tr>
        <th colspan="3"><c:out value="${TopicTimesView.studentFullName}"/></th>
    </tr>
    <tr>
        <th colspan="3"><hw:text key="topic-topic"/> <c:out value="${TopicTimesView.topicId}"/></th>
    </tr>
    <tr>
        <th colspan="3"><c:out value="${TopicTimesView.topicLabel}"/></th>
    </tr>
    <tr>
        <th style="padding-top:10px"><hw:text key="topic-activity"/></th>
        <th style="padding-top:10px"><hw:text key="topic-time-short"/></th>
        <th style="padding-top:10px; padding-left:6px"><hw:text key="topic-dateCompleted"/></th>
    </tr>
    <c:choose>
        <c:when test="${TopicTimesView.areAllTimesNull}">
            <tr>
                <td colspan="3">
                    <hw:text key="dialog-noDataPresent"/>
                </td>
            </tr>
        </c:when>
        <c:otherwise>
            <tr>
                <td>
                    <hw:text key="topic-time-home"/>
                </td>
                <td>
                    ${TopicTimesView.homeTimeFormatted}
                </td>
                <td>
                    --
                </td>
            </tr>
            <tr>
                <td>
                    <hw:text key="topic-time-warmup"/>
                </td>
                <td>
                    ${TopicTimesView.warmupTimeFormatted}
                </td>
                <td>
                    ${TopicTimesView.warmupDateCompleted}
                </td>
            </tr>
            <tr>
                <td>
                    <hw:text key="topic-time-presentation"/>
                </td>
                <td>
                    ${TopicTimesView.presentationTimeFormatted}
                </td>
                <td>
                    ${TopicTimesView.presentationDateCompleted}
                </td>
            </tr>
            <tr>
                <td>
                    <hw:text key="topic-time-workedExmples"/>
                </td>
                <td>
                    ${TopicTimesView.workedExamplesTimeFormatted}
                </td>
                <td>
                    --
                </td>
            </tr>
            <tr>
                <td>
                    <hw:text key="topic-time-practice"/>
                </td>
                <td>
                    ${TopicTimesView.practiceTimeFormatted}
                </td>
                <td>
                    ${TopicTimesView.practiceDateCompleted}
                </td>
            </tr>
            <tr>
                <td>
                    <hw:text key="topic-time-review"/>
                </td>
                <td>
                    ${TopicTimesView.reviewTimeFormatted}
                </td>
                <td>
                    ${TopicTimesView.reviewDateCompleted}
                </td>
            </tr>            
        </c:otherwise>
    </c:choose>
</table>