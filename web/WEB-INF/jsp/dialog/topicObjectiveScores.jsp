<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<table id="dialogObjectiveScoresHeaders" cellpadding="0" cellspacing="0">
    <tr>
        <th><c:out value="${Student.fullName}"/></th>
    </tr>
    <tr>
        <th><hw:text key="${HeaderKey}"/> <c:out value="${TopicState.appTopicNode.id}"/></th>
    </tr>
    <tr>
        <th><c:out value="${TopicState.appTopicNode.label}"/></th>
    </tr>
    <tr>
        <th><hw:text key="dialog-objectiveScores"/></th>
    </tr>
</table>
<div class="modal-scrollable">
    <table id="dialogObjectiveScores" cellpadding="0" cellspacing="0">
        <c:choose>
            <c:when test="${allScoresNull}">
                <tr>
                    <td>
                        <hw:text key="dialog-noDataPresent"/>
                    </td>
                </tr>
            </c:when>
            <c:otherwise>
                <c:forEach var="objectiveScores" items="${ObjectiveScoreMap}">
                    <c:set var="objective" value="${objectiveScores.key}"/>
                    <c:set var="score" value="${objectiveScores.value}"/>
                    <tr>
                        <td>
                            <c:out value="${objective.label}" escapeXml="false"/>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${not empty score}">
                                    ${score}
                                </c:when>
                                <c:otherwise>
                                    --
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
            </c:otherwise>
        </c:choose>
    </table>
</div>