<%--<%@include file="/WEB-INF/jspf/noCache.jspf" %>--%>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<!DOCTYPE html>
<html>
    <head>
        <hw:google-analytics/>
        <title><hw:text key="global-title"/>: <hw:text key="report-sessionActivity-title"/></title>
        <hw:globalMetas/>
        <gs:jQueryEnable/>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <script type="text/javascript" src="<c:url value="/scripts/globalScript.js"/>"></script>
        <script src="<c:url value="/scripts/HWLIB.js"/>"></script>
        <script type="text/javascript">
            function changeMonth(){
                var selectedMonth = HWLIB.UTIL.getSelectedOptionValue("selectMonth");
                location.href='<c:url value="${ServletPath}"/>?month='+selectedMonth;
            }
            function downloadExcel(){
                location.href='<c:url value="${ServletPath}"/>?export=true';
            }
        </script>
    </head>
    <body>
        <gs:combinationBodyHeader/>
        <gs:bodyContainer>
            <p>
                <div>
                    <hw:text key="report-sessionActivity-changeDate"/>:
                </div>
                <div>
                    <select id="selectMonth" onchange="changeMonth();">
                        <option value="forever" <c:if test="${empty SessionLogReport.selectedMonth}">selected="selected"</c:if>>--<hw:text key="report-sessionActivity-allTime"/>--</option>
                        <c:forEach var="month" items="${SessionLogReport.selectableMonths}">
                            <fmt:formatDate var="monthParamValue" value="${month}" pattern="MM-yyyy"/>
                            <option value="${monthParamValue}"
                                <c:if test="${SessionLogReport.selectedMonth == month}">selected="selected"</c:if>
                            ><fmt:formatDate value="${month}" pattern="MMMM yyyy"/></option>
                        </c:forEach>
                    </select>
                </div>
            </p>
            <h1><hw:text key="report-sessionActivity-title"/></h1>
            <p>
                <input type="button" value="<hw:text key="form-downloadAsExcel"/>" onclick="downloadExcel();"/>
            </p>
            <jsp:include page="/WEB-INF/jsp/reports/sessionActivityReport.jsp"/>
        </gs:bodyContainer>
    </body>
</html>
