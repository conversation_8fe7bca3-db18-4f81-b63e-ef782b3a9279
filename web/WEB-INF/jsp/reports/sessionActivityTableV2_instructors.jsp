<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<table id="activityReportTable">
    <thead>
        <tr>
            <th><hw:text key="report-sessionActivityV2-institution"/></th>
            <th><hw:text key="report-sessionActivityV2-course"/></th>
            <th><hw:text key="report-sessionActivityV2-name"/></th>
            <th><hw:text key="report-sessionActivityV2-email"/></th>
            <th><hw:text key="report-sessionActivityV2-sessions"/></th>
            <th><hw:text key="report-sessionActivityV2-activeClasses"/></th>
            <th><hw:text key="report-sessionActivityV2-registeredClasses"/></th>
        </tr>
    </thead>
    <tbody>
        <c:set var="institutionName" value=""/>
        <c:forEach var="row" items="${report.teachersTableRows}">
            <tr>
                <td style="text-align:left">
                    <c:if test="${institutionName != row.institutionName_nonNull}">
                        <c:out value="${row.institutionName_nonNull}"/>
                    </c:if>
                </td>
                <td style="text-align:left">${row.courseId}</td>
                <td style="text-align:left"><c:out value="${row.teacherName_nonNull}"/></td>
                <td style="text-align:left"><c:out value="${row.teacher.email}"/></td>
                <td class="activityReportDataCell">${row.sessions}</td>
                <td class="activityReportDataCell">${row.activeClasses}</td>
                <td class="activityReportDataCell">${row.registeredClasses}</td>
            </tr>
            <c:set var="institutionName" value="${row.institutionName_nonNull}"/>
        </c:forEach>
    </tbody>
</table>