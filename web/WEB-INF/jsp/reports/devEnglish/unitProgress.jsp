<%--<%@include file="/WEB-INF/jspf/noCache.jspf" %>--%>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<!DOCTYPE html>
<html>
    <head>
        <hw:google-analytics/>
        <title><hw:text key="global-title"/>: <hw:text key="${TitleKey}"/></title>
        <hw:globalMetas/>
        <gs:jQueryEnable/>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <script type="text/javascript" src="<c:url value="/scripts/globalScript.js"/>"></script>
        <script src="<c:url value="/scripts/HWLIB.js"/>"></script>
        <script>
            function changeClassSelection(){
                var newClassId = HWLIB.UTIL.getSelectedOptionValue("classSelector");
                location.href = '<c:url value="${ServletPath}"/>?classId='+newClassId;
            }
            function changeUnitSelection(){
                var newUnitId = HWLIB.UTIL.getSelectedOptionValue("unitSelector");
                location.href = '<c:url value="${ServletPath}"/>?classId=${GSClass.classId}&unitId='+newUnitId;
            }
            function changeWCStepSelection(unitStateId){
                var newStep = HWLIB.UTIL.getSelectedOptionValue("wcStepSelector");
                initModalForDevEnglishWritingCenterProgress(unitStateId, newStep);
            }
            function checkReload(){
                <c:if test="${moreToLoad}">
                    animateLoading();
                    setTimeout(function(){ 
                        ajaxReplaceTable();
                    }, 2000);
                </c:if>
                <c:if test="${not moreToLoad}">
                    document.getElementById("buttonDetailsRefresh").style.display = "inline";
                </c:if>
            }
            var replaceTableUrl = "<c:url value="/report/unit_progress/de/ajax/replaceTable"/>";
            var loopMaxCount = ${loopMaxCount}; //This is a safeguard. See servlet for notes.
            var loopCurrentCount = 0;
            function ajaxReplaceTable(){
                console.log("ajaxReplaceTable");
                if( loopCurrentCount >= loopMaxCount ){
                    stopReplaceTable();
                    return;
                }
                var jqxhr = $.ajax(replaceTableUrl)
                    .done(function(data) {
                        console.log("success");
                        $("#unitProgressTable").replaceWith(data);
                        if( $("#unitProgressTable").attr("moreToLoad") === 'true' ){
                            ajaxReplaceTable();
                        }
                        else{
                            stopReplaceTable();
                        }
                    })
                    .fail(function() {
                        console.log("error");
                    })
                    .always(function() {
                        console.log("complete");
                        loopCurrentCount++;
                    });
            }
            function stopReplaceTable(){
                stopAnimateLoading();
                document.getElementById("buttonDetailsRefresh").style.display = "inline";
            }
        </script>
    </head>
    <body onload="checkReload();">
        <gs:modalDialog/>
        <gs:combinationBodyHeader/>
        <gs:bodyContainer>
            <c:if test="${not empty SortedClasses}">
                <p>
                    <div>
                        <hw:text key="viewStudentsByClass-change"/>:
                    </div>
                    <div>
                        <gs:selectClass selectedValue="${GSClass.classId}" gsClasses="${SortedClasses}" name="classSelector" showOptionForNone="ifSelectedValueEmpty"
                            attributes="onchange='changeClassSelection();'"
                        />
                    </div>
                </p>
            </c:if>
            <h1><hw:text key="${TitleKey}"/></h1>
            <gs:reportNavigation/>
            <table cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        <c:if test="${not empty SortedClasses}">
                            <table class="classAndUnitSelectTable" cellpadding="0" cellspacing="0">
                                <c:if test="${not empty GSClass}">
                                    <tr>
                                        <td>
                                            <hw:text key="report-unitProgress-changeUnit"/>:
                                        </td>
                                        <td>
                                            <gs:selectUnit selectedValue="${Unit.id}" units="${AppCourse.children}" name="unitSelector"
                                                attributes="onchange='changeUnitSelection();'"
                                            />
                                        </td>
                                    </tr>
                                </c:if>
                            </table>
                        </c:if>
                        <c:if test="${not empty UnitStateMap}">
                            <form action="<c:url value="/report/unit_progress/de"/>" method="POST">
                                <p>
                                    <input type="button" value="<hw:text key="report-refreshData"/>" onclick="location.href='<c:url value="${ServletPath}"/>?refresh=true';" id="buttonDetailsRefresh" style="width:120px; display:none;"/>
                                    <input type="submit" value="<hw:text key="form-downloadAsExcel"/>" onclick="document.getElementById('export').value='true';" style="margin-left:20px"/>
                                    <input type="hidden" name="export" id="export" value="false"/>
                                    <hw:loadingTextAnimation />
                                </p>
                            </form>
                        </c:if>
                    </td>
                    <td class="reportColorKeyCell">
                        <c:if test="${not empty UnitStateMap}">
                            <gs:reportColorKeyPound/>
                        </c:if>
                    </td>
                </tr>
            </table>
            <c:if test="${not empty UnitStateMap}">
                <jsp:include page="/WEB-INF/jsp/reports/devEnglish/unitProgressTable.jsp"/>
            </c:if>
            <c:if test="${empty UnitStateMap}">
                <p><hw:text key="report-empty"/></p>
            </c:if>
        </gs:bodyContainer>
        <gs:modalDialogScript/>
    </body>
</html>
