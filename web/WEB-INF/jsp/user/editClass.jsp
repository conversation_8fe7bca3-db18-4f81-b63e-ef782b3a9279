<%--<%@include file="/WEB-INF/jspf/noCache.jspf" %>--%>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<!DOCTYPE html>
<html>
    <head>
        <hw:google-analytics/>
        <title><hw:text key="global-title"/>: <hw:text key="editClass-title"/></title>
        <hw:globalMetas/>
        <gs:jQueryEnable/>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <script type="text/javascript" src="<c:url value="/scripts/globalScript.js"/>"></script>
        <script src="<c:url value="/scripts/md5.js"/>"></script>
        <script src="<c:url value="/scripts/HWLIB.js"/>"></script>
        <script>
            function preEditSubmit(event){
                var isValid = true;
                if( !isValid ){
                    event.preventDefault();
                    return false;
                }
            }
            function changeClassSelection(){
                var newClassId = HWLIB.UTIL.getSelectedOptionValue("classSelector");
                location.href = '<c:url value="/course_settings"/>?classId='+newClassId;
            }
            var invalidClassName = false;
            <c:if test="${invalidClassName}">invalidClassName = true;</c:if>
            function checkValidityPopups(){
                if( invalidClassName ){
                    showModalAlert("uniqueClassNameWarning");
                }
            }
        </script>
    </head>
    <body onload="checkValidityPopups();">
        <gs:modalAlert id="uniqueClassNameWarning" includeFile="/WEB-INF/jsp/dialog/uniqueClassNameWarning.jsp"/>
        <gs:combinationBodyHeader/>
        <gs:bodyContainer>
            <form action="<c:url value="/course_settings"/>" method="post" onsubmit="return preEditSubmit(event);">
                <c:if test="${not param.finalCreate and not empty SortedClasses}">
                    <p>
                        <div>
                            <hw:text key="editClass-change"/>:
                        </div>
                        <div>
                            <gs:selectClass selectedValue="${GSClass.classId}" gsClasses="${SortedClasses}" name="classSelector" showOptionForNone="ifSelectedValueEmpty"
                                attributes="onchange='changeClassSelection();'"
                            />
                        </div>
                    </p>
                </c:if>
                <h1><hw:text key="editClass-title"/></h1>
                <c:if test="${empty SortedClasses}">
                    <h3 class="warningMessage"><hw:text key="noClassesPermission"/></h3>
                </c:if>
                <c:if test="${Validator.saveError}">
                    <h3 class="warningMessage"><hw:text key="form-saveError"/></h3>
                </c:if>
                <c:if test="${NewTeacherSuccess}">
                    <h3 class="successMessage"><hw:text key="editClass-teacherCreated"/></h3>
                </c:if>
                <c:if test="${param.alreadyExists}">
                    <h3 class="successMessage"><hw:text key="checkClassStatus-alreadyExists"/></h3>
                </c:if>
                <c:if test="${param.finalCreate}">
                    <h3 class="successMessage"><hw:text key="checkClassStatus-finalCreate"/></h3>
                </c:if>
                <c:if test="${Validator.isTransactionComplete}">
                    <h3 class="successMessage"><hw:text key="editClass-success"/></h3>
                </c:if>
                <c:if test="${not empty GSClass}">
                    <table class="formTableSmallSpace institutionSummary" cellpadding="0" cellspacing="0">
                        <tr>
                            <td><hw:text key="editClass-current"/>:</td>
                            <td><c:out value="${GSClass.className}"/></td>
                        </tr>
                        <tr>
                            <td><hw:text key="editClass-courseId"/>:</td>
                            <td><c:out value="${GSClass.siteClassId}"/></td>
                        </tr>
                        <tr>
                            <td><hw:text key="editClass-courseLocation"/>:</td>
                            <td><c:out value="${GSClass.siteUrl}"/></td>
                        </tr>
                    </table>
                </c:if>
                <table class="formTableMediumSpace validatorThreeColumnForm" cellpadding="0" cellspacing="0">
                    <c:if test="${not empty GSClass}">
                        <tr>
                            <c:set var="inputName" value="GSClass_0_className"/>
                            <fmt:message var="fieldLabel" key="editClass-className" bundle="${Text}"/>
                            <td>${fieldLabel}:</td>
                            <td>
                                <input type="hidden" name="validatorInput" value="${inputName}"/>
                                <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                            </td>
                            <c:choose>
                                <c:when test="${invalidClassName}">
                                    <td><span class="invalidMessage"><hw:text key="checkClassStatus-uniqueCourseName"/></span></td>
                                </c:when>
                                <c:otherwise>
                                    <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                                </c:otherwise>
                            </c:choose>
                        </tr>
                        <gs:selectTeacherPrimaryAndSecondaryRows />
                        <tr>
                            <c:set var="inputName" value="Manual_validate_quickPreassessment"/>
                            <fmt:message var="fieldLabel" key="editClass-quickPreassessment" bundle="${Text}"/>
                            <td>${fieldLabel}:</td>
                            <td>
                                <div style="padding-top:5px; padding-bottom:5px">
                                    <div>
                                        <input type="radio" name="${inputName}" value="true" <c:if test="${param[inputName]=='true' || GSClass.quickPreassessment}">checked="checked"</c:if> />
                                        <hw:text key="editClass-quickPreassessment-true"/>
                                    </div>
                                    <div>
                                        <input type="radio" name="${inputName}" value="false" <c:if test="${param[inputName]=='false' || not GSClass.quickPreassessment}">checked="checked"</c:if> />
                                        <hw:text key="editClass-quickPreassessment-false"/>
                                    </div>
                                    <div>
                                        <input type="radio" name="${inputName}" value="none" <c:if test="${param[inputName]=='none' || GSClass.disablePreassessment}">checked="checked"</c:if> />
                                        <hw:text key="editClass-quickPreassessment-none"/>
                                    </div>
                                </div>
                            </td>
                            <td></td>
                        </tr>
                        <tr>
                            <c:set var="inputName" value="GSClass_0_retakePreassessment"/>
                            <fmt:message var="fieldLabel" key="editClass-retakePreassessment" bundle="${Text}"/>
                            <td>${fieldLabel}:</td>
                            <td style="height:50px">
                                <input type="hidden" name="validatorInput" value="${inputName}"/>
                                <div>
                                    <input type="radio" name="${inputName}" value="true" <c:if test="${Validator.param[inputName]=='true'}">checked="checked"</c:if> />
                                    <hw:text key="editClass-retakePreassessment-true"/>
                                </div>
                                <div>
                                    <input type="radio" name="${inputName}" value="false" <c:if test="${Validator.param[inputName]=='false'}">checked="checked"</c:if> />
                                    <hw:text key="editClass-retakePreassessment-false"/>
                                </div>
                            </td>
                            <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                        </tr>
                        <tr>
                            <c:set var="inputName" value="GSClass_0_retakeTopicReview"/>
                            <fmt:message var="fieldLabel" key="editClass-retakeTopicReview" bundle="${Text}"/>
                            <td>${fieldLabel}:</td>
                            <td style="height:50px">
                                <input type="hidden" name="validatorInput" value="${inputName}"/>
                                <div>
                                    <input type="radio" name="${inputName}" value="true" <c:if test="${Validator.param[inputName]=='true'}">checked="checked"</c:if> />
                                    <hw:text key="editClass-retakeTopicReview-true"/>
                                </div>
                                <div>
                                    <input type="radio" name="${inputName}" value="false" <c:if test="${Validator.param[inputName]=='false'}">checked="checked"</c:if> />
                                    <hw:text key="editClass-retakeTopicReview-false"/>
                                </div>
                            </td>
                            <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                        </tr>
                        <c:if test="${not GSClass.isDevMath2}">
                            <tr>
                                <c:set var="inputName" value="GSClass_0_allowEmailHelp"/>
                                <fmt:message var="fieldLabel" key="editClass-allowEmailHelp" bundle="${Text}"/>
                                <td>${fieldLabel}:</td>
                                <td style="height:50px">
                                    <input type="hidden" name="validatorInput" value="${inputName}"/>
                                    <div>
                                        <input type="radio" name="${inputName}" value="true" <c:if test="${Validator.param[inputName]=='true'}">checked="checked"</c:if> />
                                        <hw:text key="editClass-allowEmailHelp-true"/>
                                    </div>
                                    <div>
                                        <input type="radio" name="${inputName}" value="false" <c:if test="${Validator.param[inputName]=='false'}">checked="checked"</c:if> />
                                        <hw:text key="editClass-allowEmailHelp-false"/>
                                    </div>
                                </td>
                                <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                            </tr>
                        </c:if>
                        <tr>
                            <c:set var="inputName" value="GSClass_0_threshold"/>
                            <fmt:message var="fieldLabel" key="editClass-masteryScore" bundle="${Text}"/>
                            <td>${fieldLabel}:</td>
                            <td>
                                <input type="hidden" name="validatorInput" value="${inputName}"/>
                                <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                            </td>
                            <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                        </tr>
                        <tr>
                            <td colspan="3"><input type="submit" value="<hw:text key="form-submit"/>"/></td>
                        </tr>
                    </c:if>
                </table>
            </form>
        </gs:bodyContainer>
        <gs:modalAlertScript/>
    </body>
</html>
