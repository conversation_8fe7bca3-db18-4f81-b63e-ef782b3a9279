<%--<%@include file="/WEB-INF/jspf/noCache.jspf" %>--%>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<%@taglib prefix="gsfn" uri="/WEB-INF/tlds/gsFunctions" %>
<!DOCTYPE html>
<html>
    <head>
        <hw:google-analytics/>
        <title><hw:text key="global-title"/>: <hw:text key="viewReviews-title"/></title>
        <hw:globalMetas/>
        <gs:jQueryEnable/>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <script type="text/javascript" src="<c:url value="/scripts/globalScript.js"/>"></script>
        <script src="<c:url value="/scripts/HWLIB.js"/>"></script>
        <script>
            function changeClassSelection(){
                var newClassId = HWLIB.UTIL.getSelectedOptionValue("classSelector");
                location.href = '<c:url value="/reviews"/>?classId='+newClassId;
            }
        </script>
    </head>
    <body>
        <gs:combinationBodyHeader/>
        <gs:bodyContainer>
            <c:if test="${not empty SortedClasses}">
                <p>
                    <div>
                        <hw:text key="viewStudentsByClass-change"/>:
                    </div>
                    <div>
                        <gs:selectClass selectedValue="${GSClass.classId}" gsClasses="${SortedClasses}" name="classSelector" showOptionForNone="ifSelectedValueEmpty"
                            attributes="onchange='changeClassSelection();'"
                        />
                    </div>
                </p>
            </c:if>
            <h1><hw:text key="viewReviews-title"/><c:if test="${not empty UnitStatesForActionPending}">: <hw:text key="viewReviews-actionPending"/></c:if></h1>
            <c:if test="${not empty param.navFromPlayer}">
                <h3 class="successMessage"><hw:text key="viewReviews-reviewCompleted"/></h3>
            </c:if>
            <c:if test="${not empty UnitStatesForActionPending}">
                <table class="viewReviewsTable" cellpadding="0" cellspacing="0">
                    <tr style="text-decoration:none; cursor:default">
                        <th><hw:text key="student-name"/></th>
                        <th><hw:text key="unit-unit"/></th>
                        <th><hw:text key="viewReviews-reviewType"/></th>
                         <th><hw:text key="viewReviews-dateSubmitted"/></th> 
                    </tr>
                    <c:forEach var="unitState" items="${UnitStatesForActionPending}">
                        <c:if test="${empty GSClass || GSClass.classId == unitState.gsClass.classId}">
                            <c:set var="reviewInfo" value="${unitState.student.fullName}  Unit ${unitState.unit}  ${unitState.appUnitNode.writingCenterStepLabels[unitState.actionValueInt]}"/>
                            <tr onclick="location.href='<c:url value="${unitState.actionUrl}"/>&headerText=${gsfn:myURLEncoder(reviewInfo, 'UTF-8')}'">
                                <td>
                                    <c:out value="${unitState.student.fullName}"/>
                                </td>
                                <td>
                                    ${unitState.unit}
                                </td>
                                <td>
                                    <c:out value="${unitState.appUnitNode.writingCenterStepLabels[unitState.actionValueInt]}"/>
                                </td>
                                <td>
                                    ${unitState.teacherActionDateSubmittedFormatted}
                                </td>
                            </tr>
                        </c:if>
                    </c:forEach>
                </table>
            </c:if>
        </gs:bodyContainer>
    </body>
</html>