<%--<%@include file="/WEB-INF/jspf/noCache.jspf" %>--%>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<!DOCTYPE html>
<html>
    <head>
        <hw:google-analytics/>
        <title><hw:text key="global-title"/>: <hw:text key="viewTeachers-title"/></title>
        <hw:globalMetas/>
        <gs:jQueryEnable/>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <script type="text/javascript" src="<c:url value="/scripts/globalScript.js"/>"></script>
        <script src="<c:url value="/scripts/HWLIB.js"/>"></script>
    </head>
    <body>
        <gs:combinationBodyHeader/>
        <gs:bodyContainer>
            <h1><hw:text key="viewTeachers-title"/></h1>
            <table class="formTableSmallSpace institutionSummary" cellpadding="0" cellspacing="0">
                <tr>
                    <td><hw:text key="institution-institutionName"/>:</td>
                    <td><c:out value="${User.institution.institutionName}"/></td>
                </tr>
                <tr>
                    <td><hw:text key="institution-key"/>:</td>
                    <td><c:out value="${User.institution.key}"/></td>
                </tr>
                <tr>
                    <td><hw:text key="institution-keySecret"/>:</td>
                    <td><c:out value="${User.institution.keySecret}"/></td>
                </tr>
            </table>
            <table class="viewTeachersTable" cellpadding="0" cellspacing="0">
                <tr>
                    <th><hw:text key="user-userName"/></th>
                    <th><hw:text key="teacher-lastName"/></th>
                    <th><hw:text key="teacher-firstName"/></th>
                    <th><hw:text key="teacher-email"/></th>
                    <th colspan="3"><hw:text key="viewTeachers-options"/></th>
                </tr>
                <c:forEach var="teacher" items="${SortedInstitutionTeachers}">
                    <tr>
                        <td>
                            <c:out value="${teacher.user.userName}"/>
                        </td>
                        <td>
                            <c:out value="${teacher.lastName}"/>
                        </td>
                        <td>
                            <c:out value="${teacher.firstName}"/>
                        </td>
                        <td>
                            <c:out value="${teacher.email}"/>
                        </td>
                        <td>
                            <button type='button' onclick="location.href='<c:url value="/edit_teacher?teacherId=${teacher.userId}"/>';">
                                <hw:text key="viewTeachers-edit"/>
                            </button>
                        </td>
                        <td>
                            <button type='button' onclick="if( confirm('<hw:text key="viewTeachers-deleteConfirm"/>\n<c:out value="${teacher.fullName}"/>') ) location.href='<c:url value="/delete_teacher?teacherId=${teacher.userId}"/>';">
                                <hw:text key="viewTeachers-delete"/>
                            </button>
                        </td>
                        <td>
                            <button type='button' onclick="location.href='<c:url value="/loginas?loginAs=${teacher.user.userName}"/>';">
                                <hw:text key="viewInstitutions-loginAs"/>
                            </button>
                        </td>
                    </tr>
                </c:forEach>
            </table>
            <p>
                <button type='button' onclick="location.href='<c:url value="/create_teacher"/>';">
                    <hw:text key="viewTeachers-create"/>
                </button>
            </p>
        </gs:bodyContainer>
    </body>
</html>
