<%--<%@include file="/WEB-INF/jspf/noCache.jspf" %>--%>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<!DOCTYPE html>
<html>
    <head>
        <hw:google-analytics/>
        <title><hw:text key="global-title"/>: <hw:text key="editInstitution-title"/></title>
        <hw:globalMetas/>
        <gs:jQueryEnable/>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <script type="text/javascript" src="<c:url value="/scripts/globalScript.js"/>"></script>
        <script src="<c:url value="/scripts/md5.js"/>"></script>
        <script src="<c:url value="/scripts/HWLIB.js"/>"></script>
        <script>
            function checkEmailConflict(){
                <c:if test="${not empty emailConflict}">
                    var alertMessage = "<fmt:message key="emailConflict-username" bundle="${Text}"><fmt:param value="${emailConflict}"/></fmt:message>";
                    alert(alertMessage);
                </c:if>
            }
        </script>
    </head>
    <body onload="checkEmailConflict()">
        <gs:combinationBodyHeader/>
        <gs:bodyContainer>
            <form action="<c:url value="/edit_institution?institutionId=${institution.userId}"/>" method="post">
                <h1><hw:text key="editInstitution-title"/></h1>
                <c:if test="${param.institutionCreated}">
                    <h3 class="successMessage"><hw:text key="createInstitution-success"/></h3>
                </c:if>
                <c:if test="${Validator.saveError}">
                    <h3 class="warningMessage"><hw:text key="form-saveError"/></h3>
                </c:if>
                <c:if test="${Validator.isTransactionComplete}">
                    <h3 class="successMessage"><hw:text key="editInstitution-success"/></h3>
                </c:if>
                <table class="formTableMediumSpace validatorFourColumnForm" cellpadding="0" cellspacing="0">
                    <tr>
                        <td colspan="4">
                            <hw:text key="form-requiredFields"/>: *
                        </td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="Institution_0_institutionName"/>
                        <fmt:message var="fieldLabel" key="institution-institutionName" bundle="${Text}"/>
                        <td>*</td>
                        <td>${fieldLabel}:</td>
                        <td>
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="User_0_userName"/>
                        <fmt:message var="fieldLabel" key="user-userName" bundle="${Text}"/>
                        <td>*</td>
                        <td>${fieldLabel}:</td>
                        <td>
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="Institution_0_adminFirstName"/>
                        <fmt:message var="fieldLabel" key="institution-firstName" bundle="${Text}"/>
                        <td>*</td>
                        <td><hw:text key="institution-administrator"/> ${fieldLabel}:</td>
                        <td>
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="Institution_0_adminLastName"/>
                        <fmt:message var="fieldLabel" key="institution-lastName" bundle="${Text}"/>
                        <td>*</td>
                        <td><hw:text key="institution-administrator"/> ${fieldLabel}:</td>
                        <td>
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="Institution_0_email"/>
                        <fmt:message var="fieldLabel" key="institution-email" bundle="${Text}"/>
                        <td>*</td>
                        <td>${fieldLabel}:</td>
                        <td>
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <input type="text" name="${inputName}" value="<c:out value="${Validator.param[inputName]}"/>"/>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="Institution_0_active"/>
                        <fmt:message var="fieldLabel" key="editInstitution-status" bundle="${Text}"/>
                        <td>*</td>
                        <td>${fieldLabel}:</td>
                        <td style="height:50px">
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <div>
                                <input type="radio" name="${inputName}" value="true" <c:if test="${Validator.param[inputName]=='true'}">checked="checked"</c:if> />
                                <hw:text key="institution-active"/>
                            </div>
                            <div>
                                <input type="radio" name="${inputName}" value="false" <c:if test="${Validator.param[inputName]=='false'}">checked="checked"</c:if> />
                                <hw:text key="institution-inactive"/>
                            </div>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <c:set var="inputName" value="Institution_0_allowMultiURLforLTI"/>
                        <fmt:message var="fieldLabel" key="editInstitution-allowMultiURLforLTI" bundle="${Text}"/>
                        <td>*</td>
                        <td>${fieldLabel}:</td>
                        <td style="height:50px">
                            <input type="hidden" name="validatorInput" value="${inputName}"/>
                            <div>
                                <input type="radio" name="${inputName}" value="true" <c:if test="${Validator.param[inputName]=='true'}">checked="checked"</c:if> />
                                <hw:text key="institution-allowed"/>
                            </div>
                            <div>
                                <input type="radio" name="${inputName}" value="false" <c:if test="${Validator.param[inputName]=='false'}">checked="checked"</c:if> />
                                <hw:text key="institution-notAllowed"/>
                            </div>
                        </td>
                        <td><hw:invalid validatorInput="${Validator.inputs[inputName]}" fieldLabel="${fieldLabel}"/></td>
                    </tr>
                    <tr>
                        <td colspan="4"><input type="submit" value="<hw:text key="form-submit"/>"/></td>
                    </tr>
                </table>
                <p><a href="<c:url value="/change_password_entry"/>"/><hw:text key="navigation-changePassword"/></a></p>
            </form>
        </gs:bodyContainer>
    </body>
</html>
