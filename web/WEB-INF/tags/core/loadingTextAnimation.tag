<span class="loadingAnimSpan">Loading</span><span class="loadingAnimSpan" id="loadingAnimElipses">...</span>
<script type="text/javascript">
    var animateLoadingContinue = true;
    function animateLoading(){
        if( animateLoadingContinue === false ){
            stopAnimateLoading();
            return;
        }
        $(".loadingAnimSpan").css("display","inline");
        var currentElipses = $("#loadingAnimElipses").html();
        var nextElipses = "";
        if( currentElipses === "..") nextElipses = "...";
        if( currentElipses === ".") nextElipses = "..";
        if( currentElipses === "") nextElipses = ".";
        $("#loadingAnimElipses").html(nextElipses);
        if( animateLoadingContinue === true ){
            setTimeout(function(){ animateLoading(); }, 300);
        } 
    }
    function stopAnimateLoading(){
        animateLoadingContinue = false;
        $(".loadingAnimSpan").css("display","none");
    }
</script>
