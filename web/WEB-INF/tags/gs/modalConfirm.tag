<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@attribute name="id" required="true"%>
<%@attribute name="includeFile" required="true"%>
<%@attribute name="okAltTextKey" required="false"%>
<%@attribute name="cancelAltTextKey" required="false"%>
<%@attribute name="formNumber" required="false"%>
<%@attribute name="okCallback" required="false"%>
<%@attribute name="cancelCallback" required="false"%>
<c:set var="form" value="null"/>
<c:if test="${not empty formNumber and empty okCallback}">
    <c:set var="form" value="${formNumber}"/>
</c:if>
<c:set var="okTextKey" value="confirm-ok"/>
<c:if test="${not empty okAltTextKey}">
    <c:set var="okTextKey" value="${okAltTextKey}"/>
</c:if>
<c:set var="cancelTextKey" value="confirm-cancel"/>
<c:if test="${not empty cancelAltTextKey}">
    <c:set var="cancelTextKey" value="${cancelAltTextKey}"/>
</c:if>
<div id="modalScreen_${id}" class="modalScreen"></div>
<div id="modalSpacer_${id}" class="modalSpacer">
    <div id="modalContainer_${id}" class="modalContainer">
        <div id="modalContent_${id}" class="modalContent">
            <jsp:include page="${includeFile}"/>
            <table style="margin-left: auto; margin-right:auto">
                <tr>
                    <td><input type="button" value="<hw:text key="${cancelTextKey}"/>" onclick="closeModalConfirm('${id}', false, ${form}); ${cancelCallback}"/></td>
                    <td style="width:10px">&nbsp;</td>
                    <td><input type="button" value="<hw:text key="${okTextKey}"/>" onclick="closeModalConfirm('${id}', true, ${form}); ${okCallback}"/></td>
                </tr>
            </table>
        </div>
    </div>
</div>
