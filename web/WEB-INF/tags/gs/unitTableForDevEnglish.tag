<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gsfn" uri="/WEB-INF/tlds/gsFunctions" %>
<c:set var="completionDates" value="${UnitTableForDevEnglishTagView.completionDates}"/>
<c:set var="activityTimes" value="${UnitTableForDevEnglishTagView.activityTimes}"/>
<c:set var="activityHasILEs" value="${UnitTableForDevEnglishTagView.activityHasILEs}"/>
<c:set var="UnitState" value="${StudentTabReports.currentUnitState}"/>
<c:if test="${not empty DialogUnitState}">
    <c:set var="UnitState" value="${DialogUnitState}"/>
</c:if>
<c:set var="Unit" value="${StudentTabReports.currentUnit}"/>
<c:if test="${not empty DialogUnit}">
    <c:set var="Unit" value="${DialogUnit}"/>
</c:if>
<c:if test="${empty Dialog}">
    <p>
        <input type="button" value="<hw:text key="report-refreshData"/>" onclick="location.href='<c:url value="${ServletPath}/unit"/>?studentId=${StudentTabReports.currentStudent.studentIdUrlEncoded}&unitId=${Unit.id}&refresh=true';" id="buttonDetailsRefresh" style="width:120px"/>
    </p>
</c:if>
<table class="unitTable" cellspacing="0" cellpadding="0">
    <tr>
        <td><hw:text key="unit-attempts"/>:</td>
        <td>${UnitState.attempts}</td>
        <td></td>
    </tr>
    <tr>
        <td><hw:text key="unit-review"/>:</td>
        <td class="${UnitTableForDevEnglishTagView.reviewCssClass}">${UnitTableForDevEnglishTagView.reviewScore}</td>
        <td></td>
    </tr>
    <tr>
        <td><hw:text key="unit-time-nobr"/>:</td>
        <td>${UnitTableForDevEnglishTagView.totalTime}</td>
        <td style="text-decoration:underline;"><hw:text key="report-dateCompleted"/></td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-home"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_HOME"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>--</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-introduction"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_INTRO"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-preReading"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_PRE_READING"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}
        </td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-activeReading"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_ACTIVE_READING"/>
        <c:set var="arTime" value="${activityTimes[activityId]}"/> 
        <c:set var="hasArTime" value="${not empty arTime and arTime!='0:00:00'}"/> 
        <td <c:if test="${hasArTime}">class="link"</c:if>
            <c:if test="${hasArTime}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeActiveReader')"</c:if>
        >${arTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-postReading"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_POST_READING"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-review"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_REVIEW"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-preWriting"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_PRE_WRITING"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-writingCenter"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_WRITING_CENTER"/>
        <c:set var="wcTime" value="${activityTimes[activityId]}"/> 
        <c:set var="hasWcTime" value="${not empty wcTime and wcTime!='0:00:00'}"/> 
        <td <c:if test="${hasWcTime}">class="link"</c:if>
            <c:if test="${hasWcTime}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeWritingCenter')"</c:if>
        >${wcTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-myJournal"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_JOURNAL"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
    <tr>
        <td><hw:text key="devEnglish-timeIn"/> <hw:text key="devEnglish-resources"/>:</td>
        <c:set var="activityId" value="DEV_ENGLISH_RESOURCES"/>
        <c:set var="hasILETimes" value="${activityHasILEs[activityId]}"/>
        <c:set var="activityTime" value="${activityTimes[activityId]}"/>
        <c:if test="${empty activityTime or activityTime == '0:00:00'}">
            <c:set var="hasILETimes" value="${false}"/>
        </c:if>
        <td <c:if test="${hasILETimes}">class="link"</c:if>
            <c:if test="${hasILETimes}">onclick="initModalForDevEnglishUnitProgress(${UnitState.id},'timeILE','${activityId}')"</c:if>
        >${activityTime}</td>
        <td>${completionDates[activityId]}</td>
    </tr>
</table>
