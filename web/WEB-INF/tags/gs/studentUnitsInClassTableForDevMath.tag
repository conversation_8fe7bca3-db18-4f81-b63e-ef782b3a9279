<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<table class="studentUnitsInClassTable-devMath" cellspacing="0" cellpadding="0">
    <tr>
        <th><hw:text key="unit-unit"/></th>
        <th><hw:text key="unit-score"/></th>
        <th><hw:text key="unit-prescore"/></th>
        <th><hw:text key="unit-time"/></th>
        <c:if test="${User.isAdministrator or User.isInstitution}">
            <th></th>
        </c:if>
    </tr>
    <c:forEach var="unitState" items="${Student.classStatisticsMap[GSClass].unitStates}">
        <tr>
            <td><a href="<c:url value="/student/dm?unitId=${unitState.appUnitNode.id}"/>"><c:out value="${unitState.appUnitNode.id}"/></a></td>
            <td><span class="${unitState.unitStatistics.averageTopicReviewScoreCssClass}"><fmt:formatNumber type="number" minFractionDigits="1" maxFractionDigits="1" value="${unitState.unitStatistics.averageTopicReviewScore}"/></span></td>
            <td><span class="${unitState.preassessmentCssClass}">${unitState.preassessmentScoreFormatted}</span></td>
            <td><span>${unitState.unitStatistics.totalTimeFormatted}</span></td>
            <c:if test="${User.isAdministrator or User.isInstitution}">
                <c:if test="${not unitState.gsClass.isDevMath1}">
                    <td><button onclick="window.open('<c:url value="/launchstudentunit?unitStateId=${unitState.id}"/>','unitWindow')"><hw:text key="unit-launch"/></button></td>
                </c:if>
            </c:if>
        </tr>
    </c:forEach>
</table>
