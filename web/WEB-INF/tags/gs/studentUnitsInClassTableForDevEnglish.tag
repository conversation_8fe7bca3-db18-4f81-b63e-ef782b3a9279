<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@taglib prefix="gsfn" uri="/WEB-INF/tlds/gsFunctions" %>
<p>
    <input type="button" value="<hw:text key="report-refreshData"/>" onclick="location.href='<c:url value="${ServletPath}/detail"/>?studentId=${StudentTabReports.currentStudent.studentIdUrlEncoded}&refresh=true';" id="buttonDetailsRefresh" style="width:120px"/>
</p>
<table class="studentUnitsInClassTable-devEnglish" cellspacing="0" cellpadding="0">
    <tr>
        <th><hw:text key="unit-unit"/></th>
        <th><hw:text key="unit-attempts"/></th>
        <th><hw:text key="unit-review"/></th>
        <th><hw:text key="unit-time"/></th>
        <c:if test="${User.isAdministrator or User.isInstitution}">
            <th></th>
        </c:if>
    </tr>
    <c:forEach var="unitState" items="${StudentTabReports.currentStatsForClass.unitStates}">
        <tr>
            <td><a href="<c:url value="/student/de/unit?unitId=${unitState.appUnitNode.id}"/>"><c:out value="${unitState.appUnitNode.viewStringShort}"/></a></td>
            <td><span>${unitState.attempts}</span></td>
            <td><span class="${StudentUnitsInClassTableForDevEnglishTagView.reviewCssClass[unitState.id]}">${StudentUnitsInClassTableForDevEnglishTagView.reviewScores[unitState.id]}</span></td>
            <td><span>${StudentUnitsInClassTableForDevEnglishTagView.totalTimes[unitState.id]}</span></td>
            <c:if test="${User.isAdministrator or User.isInstitution}">
                <td><button onclick="window.open('<c:url value="/launchstudentunit?unitStateId=${unitState.id}"/>','unitWindow')"><hw:text key="unit-launch"/></button></td>
            </c:if>
        </tr>
    </c:forEach>
</table>
