<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gs" tagdir="/WEB-INF/tags/gs/"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<tr>
    <c:set var="inputName" value="GSClass_0_teacherId"/>
    <fmt:message var="fieldLabel" key="editClass-teacherId" bundle="${Text}"/>
    <td>${fieldLabel}:</td>
    <td>
        <input type="hidden" name="validatorInput" value="${inputName}"/>
        <gs:selectTeacher selectedValue="${Validator.param[inputName]}" teachers="${SortedTeachers}" name="${inputName}" />
    </td>
    <td>
        <c:if test="${User.isInstitution}"><a href="<c:url value="/create_teacher?returnToClassEdit=${GSClass.classId}"/>" style="margin-left:10px; font-size:10pt"><hw:text key="editClass-assignNewTeacher"/></a></c:if>
        <c:if test="${not Validator.inputs[inputName].valid and not empty Validator.inputs[inputName].failureType}">
            <span class="invalidMessage"><hw:text key="invalid-instructor"/></span>
        </c:if>
    </td>
</tr>
<tr>
    <c:set var="inputName" value="Manual_validate_add_secondaryTeacherId"/>
    <fmt:message var="fieldLabel" key="editClass-secondaryTeacherId" bundle="${Text}"/>
    <td>${fieldLabel}:</td>
    <td>
        <gs:selectTeacher teachers="${SortedAvailableSecondaryTeachers}" name="${inputName}" />
    </td>
    <td>
        <input type="submit" value="<hw:text key="form-add"/>"/>
        <c:if test="${User.isInstitution}"><a href="<c:url value="/create_teacher?asSecondary=true&returnToClassEdit=${GSClass.classId}"/>" style="margin-left:10px; font-size:10pt"><hw:text key="editClass-assignNewSecondaryTeacher"/></a></c:if>
    </td>
</tr>
<c:forEach var="teacher" items="${SortedCurrentSecondaryTeachers}">
    <tr>
        <td></td>
        <td>
            <c:out value="${teacher.lastName}, ${teacher.firstName}"/>
        </td>
        <td>
            <input type="hidden" id="Manual_validate_delete_secondaryTeacherId" name="Manual_validate_delete_secondaryTeacherId" value=""/>
            <input type="submit" value="<hw:text key="form-remove"/>" onclick="document.getElementById('Manual_validate_delete_secondaryTeacherId').value='${teacher.userId}';"/>
        </td>
    </tr>
</c:forEach>