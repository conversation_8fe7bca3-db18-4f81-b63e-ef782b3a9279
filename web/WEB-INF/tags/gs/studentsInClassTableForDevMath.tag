<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<table class="studentsInClassTable-devMath" cellspacing="0" cellpadding="0">
    <tr>
        <th><hw:text key="student-name"/></th>
        <th><hw:text key="student-preAssessmentScore"/></th>
        <th><hw:text key="student-courseScore"/></th>
        <th><hw:text key="student-topicsAttempted"/></th>
        <th><hw:text key="student-topicsMastered"/></th>
        <th><hw:text key="student-unitsCompleted"/></th>
        <th><hw:text key="student-courseTime"/></th>
        <th colspan="2"><hw:text key="viewStudentsByClass-options"/></th>
    </tr>
    <c:forEach var="stud" items="${SortedStudents}">
        <tr>
            <td class="studentActive-${stud.active}"><a href="<c:url value="/student/dm?studentId=${stud.studentId}"/>"><c:out value="${stud.lastName}"/>, <c:out value="${stud.firstName}"/></a></td>
            <td class="studentActive-${stud.active}"><span><fmt:formatNumber type="number" minFractionDigits="1" maxFractionDigits="1" value="${stud.classStatisticsMap[GSClass].averageUnitPreassessmentScore}"/></span></td>
            <td class="studentActive-${stud.active}"><span><fmt:formatNumber type="number" minFractionDigits="1" maxFractionDigits="1" value="${stud.classStatisticsMap[GSClass].averageTopicReviewScore}"/></span></td>
            <td class="studentActive-${stud.active}"><span>${stud.classStatisticsMap[GSClass].topicsAttempted}</span></td>
            <td class="studentActive-${stud.active}"><span>${stud.classStatisticsMap[GSClass].topicsMastered}</span></td>
            <td class="studentActive-${stud.active}"><span>${stud.classStatisticsMap[GSClass].unitsCompleted}</span></td>
            <td class="studentActive-${stud.active}"><span>${stud.classStatisticsMap[GSClass].totalTimeFormatted}</span></td>
            <td>
                <c:if test="${stud.active}">
                    <input type="button" value="<hw:text key="form-inactivate"/>"
                           onclick="location.href='<c:url value="/change_student_status?fwd=${ServletPath}&studentId=${stud.studentId}"/>';"
                    />
                </c:if>
                <c:if test="${not stud.active}">
                    <input type="button" value="<hw:text key="form-activate"/>"
                           onclick="location.href='<c:url value="/change_student_status?fwd=${ServletPath}&studentId=${stud.studentId}"/>';"
                    />
                </c:if>
            </td>
            <td>
                <input type="button" value="<hw:text key="form-delete"/>"
                       onclick="if( confirm('<hw:text key="report-unitProgress-deleteConfirm"/>\n<c:out value="${stud.fullName}"/>\n\n<hw:text key="report-unitProgress-deleteWarning"/>') ) location.href='<c:url value="/delete_student?fwd=${ServletPath}&studentId=${stud.studentId}"/>';"
                />
            </td>
        </tr>
    </c:forEach>
</table>
