<script type="text/javascript">
    function unitMapCustomAlert(key, targetElement){
        unitMapCustomAlertCloseAll();
        var mainContainerElement = document.getElementById("unitMapContainer");
        var parentElement = targetElement.parentNode;
        var alertId = "unitMapCustomAlert-"+key;
        var alertElement = document.getElementById(alertId);
        
        var alertRect = alertElement.getBoundingClientRect();
        var targetRect = targetElement.getBoundingClientRect();
        var parentRect = parentElement.getBoundingClientRect();
        
        var targetMiddleY = targetRect.top + targetRect.height/2 + window.pageYOffset;
        var alertTop = targetMiddleY-alertRect.height/2;
        var alertLeft = parentRect.right - mainContainerElement.offsetLeft - alertRect.width - 8 + window.pageXOffset;

        alertElement.style.visibility = "visible";
        document.getElementById("unitMapCustomAlert-startedNotCompleted").style.top = alertTop+"px";
        document.getElementById("unitMapCustomAlert-startedNotCompleted").style.left = alertLeft+"px";
        document.getElementById("unitMapCustomAlert-completedNotMastered").style.top = alertTop+"px";
        document.getElementById("unitMapCustomAlert-completedNotMastered").style.left = alertLeft+"px";
        document.getElementById("unitMapCustomAlert-completedAndMastered").style.top = alertTop+"px";
        document.getElementById("unitMapCustomAlert-completedAndMastered").style.left = alertLeft+"px";
    }
    function unitMapCustomAlertCloseAll(){
        document.getElementById("unitMapCustomAlert-startedNotCompleted").style.visibility = "hidden";
        document.getElementById("unitMapCustomAlert-completedNotMastered").style.visibility = "hidden";
        document.getElementById("unitMapCustomAlert-completedAndMastered").style.visibility = "hidden";
    }
</script>