<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<script>
    var modalConfirmValue = null;
    function hideModalConfirm(id){
        $("#modalSpacer_"+id).hide();
        $("#modalScreen_"+id).hide();
    }
    function showModalConfirm(id){
        $("#modalSpacer_"+id).show();
        $("#modalScreen_"+id).show();
    }
    function closeModalConfirm(id, value, formNumber){
        console.log("closeModalConfirm: "+value+", "+formNumber);
        modalConfirmValue = value;
        hideModalConfirm(id);
        if( modalConfirmValue === true && formNumber !== null ){
            if( document.forms[formNumber] ){
                document.forms[formNumber].submit();
            }
        }
    }
</script>
