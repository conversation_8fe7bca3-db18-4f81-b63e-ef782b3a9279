<%@attribute name="key" required="true"%>
<%@attribute name="isForHomePage" required="true" type="java.lang.Boolean" %>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<div class="unitMapCustomAlertContainer" id="unitMapCustomAlert-${key}">
    <table class="unitMapCustomAlertHeader" cellpadding="0" cellspacing="0">
        <tr>
            <td class="unitMapCustomAlertHeaderLeftCell">
                <c:choose>
                    <c:when test="${key == 'startedNotCompleted'}">
                        <img src="<c:url value="/images/unitContents/Bookmark.png"/>" /></td>
                    </c:when>
                    <c:when test="${key == 'completedNotMastered'}">
                        <img src="<c:url value="/images/unitContents/CheckmarkOutline.png"/>" /></td>
                    </c:when>
                    <c:when test="${key == 'completedAndMastered'}">
                        <img src="<c:url value="/images/unitContents/Checkmark.png"/>" /></td>
                    </c:when>
                </c:choose>
            <td class="unitMapCustomAlertHeaderRightCell"><span class="unitMapCustomAlertCloseIcon" onclick="unitMapCustomAlertCloseAll();">X</span></td>
        </tr>
    </table>
    <div class="unitMapCustomAlertTextContainer">
        <c:choose>
            <c:when test="${isForHomePage}">
                <hw:text key="unitMap-alert-home-${key}"/>
            </c:when>
            <c:otherwise>
                <hw:text key="unitMap-alert-${key}"/>
            </c:otherwise>
        </c:choose>
    </div>
</div>