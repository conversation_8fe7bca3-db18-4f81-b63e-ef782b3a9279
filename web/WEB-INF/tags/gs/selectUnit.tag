<%@attribute name="name" required="true"%>
<%@attribute name="units" required="true" type="java.util.Collection"%>
<%@attribute name="selectedValue" required="false"%>
<%@attribute name="attributes" required="false"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<select name="${name}" id="${name}" ${attributes}>
    <c:if test="${empty selectedValue}">
        <option>--none selected--</option>
    </c:if>
    <c:forEach var="unit" items="${units}">
        <option value="${unit.id}"
            <c:if test="${not empty selectedValue and selectedValue==unit.id}">selected="selected"</c:if>
        ><c:out value="${unit.viewString}"/></option> 
    </c:forEach>
</select>
