<?xml version="1.0" encoding="UTF-8"?>
<!--
Format for rules attribute:
methodName,methodName:param1,methodName:param1:param2
Comma separates validation methods calls. Colon separates method call and parameters.
Use URL : escape for literal colon %3A (case insensitive)
Strings will be trimmed after splits.
-->
<validation>
    <form name="EditProfile">
        <bean type="org.montereyinstitute.model.User">
            <property name="userName" rules="required,uniqueUserName"/>
        </bean>
        <bean type="org.montereyinstitute.model.Teacher">
            <property name="email" rules="required,email,uniqueEmail"/>
            <property name="firstName" rules="required"/>
            <property name="lastName" rules="required"/>
        </bean>
        <bean type="org.montereyinstitute.model.Institution">
            <property name="email" rules="required,email,uniqueEmail"/>
            <property name="adminFirstName" rules="required"/>
            <property name="adminLastName" rules="required"/>
        </bean>
    </form>
    <form name="CreateClass">
        <bean type="org.montereyinstitute.model.GSClass">
            <property name="className" rules="required"/>
        </bean>
    </form>
    <form name="EditClass">
        <bean type="org.montereyinstitute.model.GSClass">
            <property name="className" rules="required"/>
            <property name="retakePreassessment" rules="required,boole"/>
            <property name="retakeTopicReview" rules="required,boole"/>
            <property name="allowEmailHelp" rules="required,boole"/>
            <property name="teacherId" rules="required,integer"/>
            <property name="threshold" rules="required,integer,min:0,max:100"/>
        </bean>
    </form>
    <form name="EditClassForDevEnglish">
        <bean type="org.montereyinstitute.model.GSClass">
            <property name="className" rules="required"/>
            <property name="retakeTopicReview" rules="required,boole"/>
            <property name="teacherId" rules="required,integer"/>
            <property name="threshold" rules="required,integer,min:0,max:100"/>
            <property name="numberOfPeerReviews" rules="required,integer,min:0,max:4"/>
            <property name="peerReviewDays" rules="required,integer,min:1,max:14"/>
            <property name="allowEmailForInstructorReview" rules="required,boole"/>
        </bean>
    </form>
    <form name="EditTeacher">
        <bean type="org.montereyinstitute.model.User">
            <property name="userName" rules="required,uniqueUserName"/>
        </bean>
        <bean type="org.montereyinstitute.model.Teacher">
            <property name="firstName" rules="required"/>
            <property name="lastName" rules="required"/>
            <property name="email" rules="required,email,uniqueEmail"/>
        </bean>
    </form>
    <form name="CreateTeacher">
        <bean type="org.montereyinstitute.model.User">
            <property name="userName" rules="required,uniqueUserName"/>
            <property name="password" rules="required"/>
        </bean>
        <bean type="org.montereyinstitute.model.Teacher">
            <property name="firstName" rules="required"/>
            <property name="lastName" rules="required"/>
            <property name="email" rules="required,email,uniqueEmail"/>
        </bean>
    </form>
    <form name="EditInstitution">
        <bean type="org.montereyinstitute.model.User">
            <property name="userName" rules="required,uniqueUserName"/>
        </bean>
        <bean type="org.montereyinstitute.model.Institution">
            <property name="institutionName" rules="required"/>
            <property name="adminFirstName" rules="required"/>
            <property name="adminLastName" rules="required"/>
            <property name="email" rules="required,email,uniqueEmail"/>
            <property name="active" rules="required,boole"/>
            <property name="allowMultiURLforLTI" rules="required,boole"/>
        </bean>
    </form>
    <form name="CreateInstitution">
        <bean type="org.montereyinstitute.model.User">
            <property name="userName" rules="required,uniqueUserName"/>
            <property name="password" rules="required"/>
        </bean>
        <bean type="org.montereyinstitute.model.Institution">
            <property name="institutionName" rules="required"/>
            <property name="adminFirstName" rules="required"/>
            <property name="adminLastName" rules="required"/>
            <property name="email" rules="required,email,uniqueEmail"/>
            <property name="active" rules="required,boole"/>
            <property name="allowMultiURLforLTI" rules="required,boole"/>
        </bean>
    </form>
    <form name="LostPassword">
        <bean type="org.montereyinstitute.model.User">
            <property name="userName" rules="required"/>
            <property name="email" rules="required,email"/>
        </bean>
    </form>
</validation>
