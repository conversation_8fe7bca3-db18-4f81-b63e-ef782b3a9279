<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" name="CourseManager" packages="">
  <Appenders>
    <Console name="STDOUT" target="SYSTEM_OUT">
      <PatternLayout>
        <Pattern>%d %p %c{1.} [%t] %m%n</Pattern>
      </PatternLayout>
    </Console>
    <SystemPropertyArbiter propertyName="SERVER_CONFIG_NAME" propertyValue="mite_production">
        <RollingFile name="RollingFile" fileName="/var/log/tomcat/catalina.out"
                     filePattern="/var/log/tomcat/catalina-%i.log.gz">
          <PatternLayout>
            <Pattern>%d %p %c{1.} [%t] %m%n</Pattern>
          </PatternLayout>
          <Policies>
            <SizeBasedTriggeringPolicy size="250 MB"/>
          </Policies>
        </RollingFile>
    </SystemPropertyArbiter>
    <SystemPropertyArbiter propertyName="SERVER_CONFIG_NAME" propertyValue="mite_staging">
        <RollingFile name="RollingFile" fileName="/var/log/tomcat/catalina.out"
                     filePattern="/var/log/tomcat/catalina-%i.log.gz">
          <PatternLayout>
            <Pattern>%d %p %c{1.} [%t] %m%n</Pattern>
          </PatternLayout>
          <Policies>
            <SizeBasedTriggeringPolicy size="250 MB"/>
          </Policies>
        </RollingFile>
    </SystemPropertyArbiter>
    <SystemPropertyArbiter propertyName="SERVER_CONFIG_NAME" propertyValue="greg_development">
        <RollingFile name="RollingFile" fileName="logs/CourseManager.out"
                     filePattern="logs/CourseManager-%i.log.gz">
          <PatternLayout>
            <Pattern>%d %p %c{1.} [%t] %m%n</Pattern>
          </PatternLayout>
          <Policies>
            <SizeBasedTriggeringPolicy size="250 MB"/>
          </Policies>
        </RollingFile>
    </SystemPropertyArbiter>
  </Appenders>
  <Loggers>
    <SystemPropertyArbiter propertyName="SERVER_CONFIG_NAME" propertyValue="mite_staging">
        <Logger name="gs.hinkleworld" level="all"></Logger>      
        <Logger name="org.montereyinstitute" level="all"></Logger>      
    </SystemPropertyArbiter>
    <SystemPropertyArbiter propertyName="SERVER_CONFIG_NAME" propertyValue="greg_development">
        <Logger name="gs.hinkleworld" level="all"></Logger>      
        <Logger name="org.montereyinstitute" level="all"></Logger>      
    </SystemPropertyArbiter>
    <Root level="info">
      <AppenderRef ref="STDOUT"/>
      <AppenderRef ref="RollingFile"/>
    </Root>
  </Loggers>
</Configuration>