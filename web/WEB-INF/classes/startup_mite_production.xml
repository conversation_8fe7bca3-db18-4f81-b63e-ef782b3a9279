<?xml version="1.0" encoding="UTF-8"?>
<startup>
    <country appDefault="true" name="United States" abbreviation="U.S." statesPropertyFile="/WEB-INF/classes/States_US.properties"/>
    <courses>
        <course id="devMath">
            <externalContent urlRoot="https://s3-us-west-1.amazonaws.com/mitecontent/DevelopmentalMath/"/>
        </course>
        <course id="devMath2">
            <externalContent urlRoot="https://content.nroc.org/DevelopmentalMath.HTML5/" 
                             courseDataDirectory="https://s3-us-west-1.amazonaws.com/mitecontent/DevelopmentalMath.HTML5/"
                             courseDataDirectory2="https://s3-us-west-1.amazonaws.com/mitecontent/PreAssessmentPlayer.HTML5/metadata/"
            />
        </course>
        <course id="devEnglish">
            <externalContent urlRoot="https://content.nroc.org/DevelopmentalEnglish/"
                             courseDataDirectory="https://s3-us-west-1.amazonaws.com/mitecontent/DevelopmentalEnglish/"
            />
        </course>
    </courses>
    <preassessmentPlayer urlRoot="https://content.nroc.org/PreAssessmentPlayer.HTML5/"/>
    <mathJax url="https://content.nroc.org/MathJax.2.7.5/MathJax.js?config=AM_SVG.js" />
    <writableDirectory path="/usr/share/tomcat/temp/"/>
    <localhostServletRequestRoot urlRoot="http://localhost:8080/"/>
    <internalReportingEmail recipient="<EMAIL>"/>
    <courseRegisteredEmail recipient="<EMAIL>"/>
    <playerNonce required="true"/> 
    <email 
        smtpOn="true"
        hostName="email-smtp.us-west-2.amazonaws.com"
        port="587"
        userName="AKIAYMGYPCXGPRI4B6MA"
        password="BFjzCQqqjmh07D+tTds0QNpNy+I8P08QMWWMS/mNGEXj"
        defaultFromName="NROC Course Manager"
        defaultFromAddress="<EMAIL>"
        defaultReplyToName="NO REPLY"
        defaultReplyToAddress="<EMAIL>"
        dumpOn="false"
        dumpFile="/var/www/tomcat/GradeServiceEmail.out"
        dumpAppend="true"
    >
        <!-- Email subjects need to be set in Text resourceBundle by convention: "email-{templatename}-subject" -->
        <template name="test" file="/WEB-INF/email/test.html"/>
        <template name="lostPassword2" file="/WEB-INF/email/lostPassword2.html"/>
        <template name="studentQuestion" file="/WEB-INF/email/studentQuestion.html"/>
        <template name="pendingInstructorReview" file="/WEB-INF/email/pendingInstructorReview.html"/>
        <template name="courseRegistered" file="/WEB-INF/email/courseRegistered.html"/>
    </email>
</startup>