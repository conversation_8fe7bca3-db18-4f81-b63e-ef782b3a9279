<?xml version="1.0" encoding="UTF-8"?>
<web-app version="4.0" xmlns="http://xmlns.jcp.org/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd">
    
    <!--APP SETTINGS and INIT PARAMS -->
    <session-config>
        <session-timeout>60</session-timeout>
        <tracking-mode>COOKIE</tracking-mode>        
        <!--<tracking-mode>URL</tracking-mode>-->        
    </session-config>
    
    <!--STANDARD PAGES-->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    <login-config>
        <auth-method>FORM</auth-method>
        <form-login-config>
            <form-login-page>/WEB-INF/login.jsp</form-login-page>
            <form-error-page>/WEB-INF/login.jsp?retry=true</form-error-page>
        </form-login-config>
    </login-config>
    <error-page>
        <error-code>403</error-code>
        <location>/WEB-INF/login.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/jsp/errors/error.jsp</location>
    </error-page>
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/jsp/errors/notfound.jsp</location>
    </error-page>
    
    <!--DATABASE-->
    <resource-ref>
        <description>GradeService DataSource</description>
        <res-ref-name>jdbc/gradeservice</res-ref-name>
        <res-type>javax.sql.DataSource</res-type>
        <res-auth>Container</res-auth>
        <res-sharing-scope>Shareable</res-sharing-scope>
    </resource-ref>
    
    <!--SECURITY-->
    <security-role>
        <role-name>0</role-name>
        <description>Administrator</description>
    </security-role>
    <security-role>
        <role-name>1</role-name>
        <description>Institution</description>
    </security-role>
    <security-role>
        <role-name>2</role-name>
        <description>Teacher</description>
    </security-role>
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>All User Access</web-resource-name>
            <url-pattern>/my_settings/*</url-pattern>
            <url-pattern>/login/*</url-pattern>
            <url-pattern>/home/<USER>/url-pattern>
            <url-pattern>/student/*</url-pattern>
            <url-pattern>/report/*</url-pattern>
            <url-pattern>/ajax/*</url-pattern>
            <url-pattern>/user_unit_launch/*</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>0</role-name>
            <role-name>1</role-name>
            <role-name>2</role-name>
        </auth-constraint>
    </security-constraint>
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>All But Admin Access</web-resource-name>
            <url-pattern>/edit_settings/*</url-pattern>
            <url-pattern>/course_settings/*</url-pattern>
            <url-pattern>/change_class_status/*</url-pattern>
            <url-pattern>/delete_class/*</url-pattern>
            <url-pattern>/create_class/*</url-pattern>
            <url-pattern>/class/*</url-pattern>
            <url-pattern>/topics/*</url-pattern>
            <url-pattern>/units/*</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>1</role-name>
            <role-name>2</role-name>
        </auth-constraint>
    </security-constraint>
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Teacher Access</web-resource-name>
            <url-pattern>/reviews/*</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>2</role-name>
        </auth-constraint>
    </security-constraint>
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Institution Access</web-resource-name>
            <url-pattern>/teacher/*</url-pattern>
            <url-pattern>/create_teacher/*</url-pattern>
            <url-pattern>/edit_teacher/*</url-pattern>
            <url-pattern>/delete_teacher/*</url-pattern>
            <url-pattern>/report/*/institution_summary</url-pattern>
            <url-pattern>/activity/*</url-pattern>
            <url-pattern>/activityV1/*</url-pattern>
            <url-pattern>/loginas</url-pattern>
            <url-pattern>/launchstudentunit</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>1</role-name>
        </auth-constraint>
    </security-constraint>
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Admin Access</web-resource-name>
            <url-pattern>/dev/*</url-pattern>
            <url-pattern>/admin/*</url-pattern>
            <url-pattern>/institution/*</url-pattern>
            <url-pattern>/change_institution_status/*</url-pattern>
            <url-pattern>/edit_institution/*</url-pattern>
            <url-pattern>/create_institution/*</url-pattern>
            <url-pattern>/delete_institution/*</url-pattern>
            <url-pattern>/site_management/*</url-pattern>
            <url-pattern>/activity/*</url-pattern>
            <url-pattern>/activityV1/*</url-pattern>
            <url-pattern>/loginas</url-pattern>
            <url-pattern>/launchstudentunit</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>0</role-name>
        </auth-constraint>
    </security-constraint>
</web-app>
