/* 
    Created on : Oct 22, 2016, 12:13:02 AM
    Author     : greg
*/
*{
    font-family: "Proxima Nova";
}
body, table td{
  font-family: "Proxima Nova" !important;
}
a{
    text-decoration: none;
}
a:hover{
    text-decoration: underline;
}
body{
    margin: 0px;
}
.unitMapDarkBlue{  
    color: #822c80;
}
.unitMapLightBlue{  
    color: #3a9bcb;
}
.unitMapCopyrightBlue{  
    color: #00749e;
}
#unitMapContainer{
    width: 1024px;
    border: 1px solid #D6D8D7;
    background-color: #FFF;
    color: #414143;
    text-align: left;
    margin-left: auto;
    margin-right: auto;
    position:relative;
}
#unitMapHeaderContainer{}
#unitMapHeaderTable{
    height:83px;
    width:100%;
    border-bottom: 1px solid #D6D8D7;
}
#unitMapHeaderTableLeftCell{
    vertical-align:top;
}
#unitMapHeaderTableLeftTop{
    height:53px;
    background-repeat:repeat-x;
}
#unitMapHeaderTextTable{
    font-size: 17pt;
    font-weight: bold;
    margin-left:15px;
    height:100%;
}
#unitMapHeaderTableLeftBottom{
    height:30px;
}
#unitMapHeaderTableRightCell{
    width:143px;
    border-left: 1px solid #D6D8D7;
    cursor:pointer;
}
#unitMapHeaderTableRightCell table{
    margin-left:auto;
    margin-right:auto;
}
#unitMapHelpTextCell{
    font-weight: bold;
    font-size: 11pt;
    padding-left:4px;
}
#unitMapBodyContainer{
    min-height: 596px;
    padding-left:42px;
    padding-top:45px;
}
#unitMapPreassessmentContainer{
    height: 596px;
}
#unitMapPreassessmentContainer iframe{
    height:100%;
    width:100%;
    border:none;
}
#unitMapBodyTopTable{
    width:100%;
}
#unitMapBodyTopTable td{
    vertical-align: top;
}
#unitMapBodyTopTableLeftCell{
    width:45%;
    padding-right:59px;
}
#unitMapBodyLabel{
    font-size:23pt;
    font-weight: bold;
}
#unitMapBodyInstructions{
    font-size: 13pt;
    margin-top: 23px;
}
#unitMapBodyTopTableRightCell{
    width:55%;
    padding-top:50px;
}
#unitMapContentsContainer{
    margin-top: 40px;
    margin-bottom: 40px;
}
#unitMapFooterContainer{
    border-top: 1px solid #D6D8D7;
    height:52px;
}
#unitMapFooterTable{
    width:100%;
    height:100%;
}
#unitMapFooterImageCell{
    padding-left:50px;
}
#unitMapFooterTextCell{
    text-align: right;
    padding-right: 20px;
    font-size: 7pt;
}
/* UNIT MAP CONTENTS PAGE */
#unitMapContentsTable td{
    vertical-align: middle;
}
.unitMapContentsStatusCell{
    text-align:center; 
    vertical-align:middle; 
    height:40px; 
    width:42px;
}
.unitMapContentsStatusIcon{
    vertical-align:middle;
}
.unitMapContentsNumberCell{
    height:40px; 
    width:40px; 
    position:relative;
}
.unitMapContentsTextCell{
    text-align:left; 
    vertical-align:middle; 
    height:40px; 
    padding-left:10px;
    font-size: 20pt;
    font-weight: bold;
}
.unitMapContentsSpacerCell{
    height:16px; 
    width:40px; 
    position:relative;
}
.unitMapContentsNumberVerticalShaftFirst{
    height:20px; 
    width:10px; 
    position:absolute; 
    bottom:0px; 
    left:15px; 
    background-color:#d6d7d8;
}
.unitMapContentsNumberVerticalShaftMiddle{
    height:40px; 
    width:10px; 
    position:absolute; 
    top:0px; 
    left:15px; 
    background-color:#d6d7d8;
}
.unitMapContentsNumberVerticalShaftLast{
    height:20px; 
    width:10px; 
    position:absolute; 
    top:0px; 
    left:15px; 
    background-color:#d6d7d8;
}
.unitMapContentsNumberVerticalShaftSpacer{
    height:16px; 
    width:10px; 
    position:absolute; 
    top:0px; 
    left:15px; 
    background-color:#d6d7d8;
}
.unitMapContentsNumberCircle{
    height:40px; 
    width:40px; 
    border-radius:20px; 
    background-color:#3a9bcb; 
    position:absolute; 
    top:0px; 
    left:0px;
}
.unitMapContentsNumberText{
    color:white; 
    font-weight:bold; 
    font-size:13pt; 
    position:absolute; 
    top:10px; 
    left:0px; 
    text-align:center; 
    width:40px;
}
.untMapLessonTitle{
    font-weight:bold; 
    font-size:18pt;
    margin-top: 23px;
    margin-bottom: 15px;
}
.unitMapTopicRow{
    cursor:pointer;
    color: #3a9bcb;
}
.unitMapTopicRowDisabled{
    cursor:default;
    color: #ccc;
}
.unitMapTopicRow:hover{
    color: #822c80;
}
.unitMapMyLearningPathCompletedText{
    color: #3a9bcb;
    font-weight:bold; 
    font-size:18pt;
    text-align:center;
}
/* UNIT MAP BREADCRUMB PAGE */
#unitMapBreadcrumbTable{
    height:30px;
}
#unitMapBreadcrumbHomeCell{
    border-right:1px solid white;
    cursor:pointer;
     width:44px;
}
.unitMapBreadcrumbItemCell{
    background-color: #3a9bcb;
}
.unitMapBreadcrumbItemTable{
    height:100%;
}
.unitMapBreadcrumbItemTable td{
    border:none;
}
.unitMapBreadcrumbItemLeftCell{
    width:34px;
    position:relative;
}
.unitMapBreadcrumbNumberCircle{
    height:20px;
    width:20px;
    border-radius:10px;
    position:absolute;
    top:5px;
    left:7px;
    background-color:white;
}
.unitMapBreadcrumbNumberText{
    position:absolute;
    top:7px;
    text-align: center;
    width:34px;
    font-weight: bold;
    font-size: 11pt;
}
.unitMapBreadcrumbItemRightCell{
    color:white;
    padding-left: 4px;
    padding-right: 12px;
    vertical-align: middle;
    font-weight: bold;
    font-size: 11pt;
    text-align: left;
}
/* UNIT MAP CUSTOM ALERT */
.unitMapCustomAlertContainer{
    position: absolute;
    width: 125px;
    min-height: 100px;
    padding-left: 5px;
    padding-right: 5px;
    padding-bottom: 5px;
    background-color: #eee;
    box-shadow: 0 0 5px 2px rgba(0,0,0,0.5);
    top:100px;
    left:0px;
    font-size: small;
    visibility:hidden;
    transition: top 0.5s;
}
.unitMapCustomAlertHeader{
    width:100%;
}
.unitMapCustomAlertHeaderLeftCell{
}
.unitMapCustomAlertHeaderRightCell{
    color: gray;
    text-align: right;
}
.unitMapCustomAlertCloseIcon{
    cursor:pointer;
}
.unitMapCustomAlertTextContainer{
    color: #414143;
}
/* Keyboard Focus */
::-moz-focus-inner {
border: 0;
}
.hide-focus:focus, .hide-focus :focus{
outline: none;
}
.show-focus:focus, .show-focus :focus{
outline: dotted #3a9bcb 2px;
outline-offset: -2px;
}
.show-focus .reverse-focus:focus{
outline: dotted #ffffff 2px;
outline-offset: -2px;
}
.unitMapKeyboardFocusSelector{
    bottom:80px;
    right:50px;
    text-align:left;
    width:200px;
    font-size: 9pt;
    position:absolute;
    vertical-align:bottom;
}
.unitMapScreenReaderSelector{
    bottom:102px;
    right:50px;
    text-align:left;
    width:200px;
    font-size: 9pt;
    position:absolute;
    vertical-align:bottom;
}